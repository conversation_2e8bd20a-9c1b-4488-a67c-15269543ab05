<html>
<head>
<META HTTP-EQUIV="Content-Type" content="text/html; charset=Windows-1252">
</head>
<body>
<pre>
<table width=100% bgcolor=#CFCFE5><tr> <td> <font face=arial size=+3>
Build Log
</font></table><table width=* cellspacing=0 cellpadding=0><tr><td width=0 bgcolor=#EDEDF5>&nbsp;</td><td width=0 bgcolor=#FFFFFF>&nbsp;</td><td width=*><pre>
<h3>------- Build started: Project: China, Configuration: hshield_5|Win32 -------
</h3>
</pre></table><table width=100% bgcolor=#DFDFE5><tr><td><font face=arial size=+2>
Command Lines
</font></table><table width=* cellspacing=0 cellpadding=0><tr><td width=0 bgcolor=#EDEDF5>&nbsp;</td><td width=0 bgcolor=#FFFFFF>&nbsp;</td><td width=*><pre>Creating temporary file "d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000003.rsp" with contents
[
/O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_USRDLL" /D "CHINA_EXPORTS" /D "_WINDLL" /D "_MBCS" /FD /EHsc /MT /GS /Yu"stdafx.h" /Fp"hshield_5/China.pch" /Fo"hshield_5/" /Fd"hshield_5/vc70.pdb" /W3 /c /Wp64 /Zi /TP
".\China.cpp"
]
Creating command line "cl.exe @"d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000003.rsp" /nologo"
Creating temporary file "d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000004.rsp" with contents
[
/O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_USRDLL" /D "CHINA_EXPORTS" /D "_WINDLL" /D "_MBCS" /FD /EHsc /MT /GS /Yc"stdafx.h" /Fp"hshield_5/China.pch" /Fo"hshield_5/" /Fd"hshield_5/vc70.pdb" /W3 /c /Wp64 /Zi /TP
".\stdafx.cpp"
]
Creating command line "cl.exe @"d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000004.rsp" /nologo"
Creating temporary file "d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000005.rsp" with contents
[
/OUT:"..\China.dll" /INCREMENTAL:NO /NOLOGO /DLL /DEBUG /PDB:"hshield_5/China.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /IMPLIB:"..\China.lib" /MACHINE:X86  kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib
".\hshield_5\China.obj"
".\hshield_5\stdafx.obj"
]
Creating command line "link.exe @"d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\RSP000005.rsp""
</pre></table><table width=100% bgcolor=#DFDFE5><tr><td><font face=arial size=+2>
Output Window
</font></table><table width=* cellspacing=0 cellpadding=0><tr><td width=0 bgcolor=#EDEDF5>&nbsp;</td><td width=0 bgcolor=#FFFFFF>&nbsp;</td><td width=*><pre>Compiling...
stdafx.cpp
Compiling...
China.cpp
Linking...
   Creating library ..\China.lib and object ..\China.exp
</pre></table><table width=100% bgcolor=#DFDFE5><tr><td><font face=arial size=+2>
Results
</font></table><table width=* cellspacing=0 cellpadding=0><tr><td width=0 bgcolor=#EDEDF5>&nbsp;</td><td width=0 bgcolor=#FFFFFF>&nbsp;</td><td width=*><pre>
Build log was saved at "file://d:\kal\Kal Development\2009 engine sources\MainGame\MainGame\China\hshield_5\BuildLog.htm"
China - 0 error(s), 0 warning(s)</pre></table><table   width=100% height=20 bgcolor=#CFCFE5><tr><td><font face=arial size=+2>
</font></table></body></html>