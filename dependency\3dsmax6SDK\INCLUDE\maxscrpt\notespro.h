/*	
 *		noteTrack_protocol.h - protocol for MAX note tracks
 *
 *
 */


	def_visible_generic	( sortNote<PERSON>eys,			"sortNoteKeys" );
	def_visible_generic	( addNewNote<PERSON><PERSON>,		"addNewNoteKey" );
	def_visible_generic	( deleteNoteKeys,		"deleteNoteKeys" );
	def_visible_generic	( delete<PERSON><PERSON><PERSON><PERSON>,		"deleteNoteKey" );
	def_visible_generic	( getNoteKeyTime,		"getNoteKeyTime" );
	def_visible_generic	( getNoteKeyIndex,		"getNoteKeyIndex" );
