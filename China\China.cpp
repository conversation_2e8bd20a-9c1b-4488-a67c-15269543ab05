// China.cpp : Defines the entry point for the DLL application.
//

#include "stdafx.h"
#include "china.h"


const WORD pBIGTable[]=
{
	0xa1a1, 0xaca3, 0xa2a1, 0xa3a1, 0xaea3, 0xa4a1, 0xbba3, 0xbaa3, 
	0xbfa3, 0xa1a3, 0xcba1, 0xada1, 0xa7a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa4a1, 0xb0a2, 0xbaa3, 0xbfa3, 0xa1a3, 0xa6a9, 0xa1a1, 0xf2a6, 
	0xaaa1, 0xf4a6, 0xa1a1, 0xf5a6, 0xa1a1, 0xa8a3, 0xa9a3, 0xe0a6, 
	0xe1a6, 0xfba3, 0xfda3, 0xf0a6, 0xf1a6, 0xb2a1, 0xb3a1, 0xe2a6, 
	0xe3a6, 0xbea1, 0xbfa1, 0xeea6, 0xefa6, 0xb6a1, 0xb7a1, 0xe6a6, 
	0xe7a6, 0xb4a1, 0xb5a1, 0xe4a6, 0xe5a6, 0xb8a1, 0xb9a1, 0xe8a6, 
	0xe9a6, 0xbaa1, 0xbba1, 0xeaa6, 0xeba6, 0xa1a1, 0xa1a1, 0x7fa1, 
	0x80a1, 0x81a1, 0x82a1, 0x83a1, 0x84a1, 0x85a1, 0x86a1, 0x87a1, 
	0x88a1, 0x89a1, 0x8aa1, 0x8ba1, 0x8ca1, 0x8da1, 0x8ea1, 0x8fa1, 
	0x90a1, 0x91a1, 0x92a1, 0x93a1, 0x94a1, 0x95a1, 0x96a1, 0x97a1, 
	0x98a1, 0x99a1, 0x9aa1, 0x9ba1, 0x9ca1, 0x9da1, 0x9ea1, 0x9fa1, 
	0xa0a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xaea1, 0xafa1, 0xb0a1, 
	0xb1a1, 0xa1a1, 0xa8a1, 0xe0a3, 0xe4a1, 0xa3a3, 0xa6a3, 0xaaa3, 
	0xf9a1, 0xeca1, 0xa8a1, 0xf0a1, 0xf1a1, 0xf7a1, 0xf8a1, 0xf2a1, 
	0xeea1, 0xefa1, 0xf3a1, 0xf4a1, 0xf5a1, 0xf6a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdfa3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xaba3, 
	0xada3, 0xc1a1, 0xc2a1, 0xc0a1, 0xcca1, 0xbca3, 0xbea3, 0xbda3, 
	0xdca1, 0xdda1, 0xd9a1, 0xdea1, 0xd6a1, 0xd4a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfea3, 0xc9a1, 0xc8a1, 0xcda1, 0xcfa1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd2a1, 0xd3a1, 0xdfa1, 0xe0a1, 
	0xe2a1, 0xe1a1, 0xaba3, 0xd1a1, 0xfca1, 0xfda1, 0xfba1, 0xfaa1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcea1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xafa3, 0xdca3, 0xe7a1, 0xa4a3, 0xa1a1, 0xe9a1, 0xeaa1, 0xa5a3, 
	0xc0a3, 0xe6a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdfa3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe0a9, 0xd8a9, 0xd0a9, 0xc8a9, 0xc0a9, 0xa1a1, 0xa4a9, 0xa6a9, 
	0xa1a1, 0xb0a9, 0xb4a9, 0xb8a9, 0xbca9, 0xa1a1, 0x7fa2, 0x80a2, 
	0x81a2, 0x82a2, 0x83a2, 0x84a2, 0x85a2, 0x86a2, 0x87a2, 0x88a2, 
	0x89a2, 0x8aa2, 0x8ba2, 0x8ca2, 0x8da2, 0x8ea2, 0x8fa2, 0x90a2, 
	0x91a2, 0x92a2, 0x93a2, 0x94a2, 0x95a2, 0x96a2, 0x97a2, 0x98a2, 
	0x99a2, 0x9aa2, 0x9ba2, 0x9ca2, 0x9da2, 0x9ea2, 0x9fa2, 0xa0a2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb0a3, 0xb1a3, 
	0xb2a3, 0xb3a3, 0xb4a3, 0xb5a3, 0xb6a3, 0xb7a3, 0xb8a3, 0xb9a3, 
	0xf1a2, 0xf2a2, 0xf3a2, 0xf4a2, 0xf5a2, 0xf6a2, 0xf7a2, 0xf8a2, 
	0xf9a2, 0xfaa2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xaeca, 0xa1a1, 0xa6d8, 0xc1a3, 0xc2a3, 
	0xc3a3, 0xc4a3, 0xc5a3, 0xc6a3, 0xc7a3, 0xc8a3, 0xc9a3, 0xcaa3, 
	0xcba3, 0xcca3, 0xcda3, 0xcea3, 0xcfa3, 0xd0a3, 0xd1a3, 0xd2a3, 
	0xd3a3, 0xd4a3, 0xd5a3, 0xd6a3, 0xd7a3, 0xd8a3, 0xd9a3, 0xdaa3, 
	0xe1a3, 0xe2a3, 0xe3a3, 0xe4a3, 0xe5a3, 0xe6a3, 0xe7a3, 0xe8a3, 
	0xe9a3, 0xeaa3, 0xeba3, 0xeca3, 0xeda3, 0xeea3, 0xefa3, 0xf0a3, 
	0xf1a3, 0xf2a3, 0xf3a3, 0xf4a3, 0xf5a3, 0xf6a3, 0xf7a3, 0xf8a3, 
	0xf9a3, 0xfaa3, 0xa1a6, 0xa2a6, 0xa3a6, 0xa4a6, 0xa5a6, 0xa6a6, 
	0xa7a6, 0xa8a6, 0xa9a6, 0xaaa6, 0xaba6, 0xaca6, 0xada6, 0xaea6, 
	0xafa6, 0xb0a6, 0xb1a6, 0xb2a6, 0xb3a6, 0xb4a6, 0xb5a6, 0xb6a6, 
	0xb7a6, 0xb8a6, 0xc1a6, 0xc2a6, 0xc3a6, 0xc4a6, 0xc5a6, 0xc6a6, 
	0xc7a6, 0xc8a6, 0xc9a6, 0xcaa6, 0xcba6, 0xcca6, 0xcda6, 0xcea6, 
	0xcfa6, 0xd0a6, 0xd1a6, 0xd2a6, 0xd3a6, 0xd4a6, 0xd5a6, 0xd6a6, 
	0xd7a6, 0xd8a6, 0xc5a8, 0xc6a8, 0xc7a8, 0xc8a8, 0xc9a8, 0xcaa8, 
	0xcba8, 0xcca8, 0xcda8, 0xcea8, 0xcfa8, 0x7fa3, 0x80a3, 0x81a3, 
	0x82a3, 0x83a3, 0x84a3, 0x85a3, 0x86a3, 0x87a3, 0x88a3, 0x89a3, 
	0x8aa3, 0x8ba3, 0x8ca3, 0x8da3, 0x8ea3, 0x8fa3, 0x90a3, 0x91a3, 
	0x92a3, 0x93a3, 0x94a3, 0x95a3, 0x96a3, 0x97a3, 0x98a3, 0x99a3, 
	0x9aa3, 0x9ba3, 0x9ca3, 0x9da3, 0x9ea3, 0x9fa3, 0xa0a3, 0xd0a8, 
	0xd1a8, 0xd2a8, 0xd3a8, 0xd4a8, 0xd5a8, 0xd6a8, 0xd7a8, 0xd8a8, 
	0xd9a8, 0xdaa8, 0xdba8, 0xdca8, 0xdda8, 0xdea8, 0xdfa8, 0xe0a8, 
	0xe1a8, 0xe2a8, 0xe3a8, 0xe4a8, 0xe5a8, 0xe6a8, 0xe7a8, 0xe8a8, 
	0xe9a8, 0xa1a1, 0xa5a1, 0xa1a1, 0xa6a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbbd2, 0xd2d2, 0xa1b6, 
	0xdfc6, 0xcbc4, 0xc5be, 0xcbc1, 0xfeb6, 0xcbc8, 0xf9b6, 0xebc8, 
	0xcbb0, 0xb8bc, 0xb6b5, 0xf3b5, 0xa6c1, 0xb0d8, 0xaeca, 0xb7b2, 
	0xd6d3, 0xfdc8, 0xc2cf, 0xc9d5, 0xcfc9, 0xbed1, 0xe8cd, 0xb2b7, 
	0xc3be, 0xb4c3, 0xb2d2, 0xf2c6, 0xdad3, 0xf6cd, 0xa3d8, 0xd0c8, 
	0xd7c9, 0xa7c7, 0xe6b2, 0xdabf, 0xc1cd, 0xbfca, 0xa6cf, 0xf3b4, 
	0xaec5, 0xd3d7, 0xdde6, 0xdee6, 0xe7b4, 0xa1d0, 0xccde, 0xacca, 
	0xbdc9, 0xa8b4, 0xa4b9, 0xbabc, 0xd1d2, 0xc8cb, 0xedbd, 0xc9b8, 
	0xc3de, 0xaedf, 0xadb9, 0xc5b2, 0x7fa4, 0x80a4, 0x81a4, 0x82a4, 
	0x83a4, 0x84a4, 0x85a4, 0x86a4, 0x87a4, 0x88a4, 0x89a4, 0x8aa4, 
	0x8ba4, 0x8ca4, 0x8da4, 0x8ea4, 0x8fa4, 0x90a4, 0x91a4, 0x92a4, 
	0x93a4, 0x94a4, 0x95a4, 0x96a4, 0x97a4, 0x98a4, 0x99a4, 0x9aa4, 
	0x9ba4, 0x9ca4, 0x9da4, 0x9ea4, 0x9fa4, 0xa0a4, 0xf3b3, 0xa4d8, 
	0xbbb2, 0xd0d6, 0xe1b7, 0xa4b5, 0xaed6, 0xfcd2, 0xe8d3, 0xc6d4, 
	0xaebe, 0xa5bb, 0xe5ce, 0xbabf, 0xcac8, 0xb2ca, 0xead8, 0xcdc6, 
	0xf0b3, 0xd4c8, 0xf1bd, 0xe9bd, 0xc6d8, 0xaad4, 0xcad4, 0xdac4, 
	0xf9c1, 0xe2d9, 0xabb9, 0xdfc8, 0xd7d0, 0xd6b7, 0xd0c7, 0xd7d8, 
	0xc8d4, 0xb4b9, 0xf0ce, 0xafbb, 0xa5c6, 0xe7ce, 0xfdc9, 0xa6d8, 
	0xe5b1, 0xf2b6, 0xd1d3, 0xb0bc, 0xb4b7, 0xc9c8, 0xeccc, 0xf2b7, 
	0xabcc, 0xb2d8, 0xd7bf, 0xd9c9, 0xc8d3, 0xdfb3, 0xcdcd, 0xcdb0, 
	0xc3bb, 0xa5d8, 0xf5b5, 0xfdd2, 0xc4d0, 0xeab8, 0xa7bb, 0xd6ca, 
	0xfad4, 0xa7d6, 0xc4ce, 0xb7b6, 0xefbd, 0xbdb7, 0xd5c8, 0xbbd4, 
	0xc2d4, 0xbec4, 0xb7c7, 0xb9d6, 0xf5b4, 0xe3ce, 0xc8b1, 0xabc3, 
	0xcfca, 0xaecb, 0xf0bb, 0xa6d7, 0xb8b8, 0xb3d8, 0xacc6, 0xc0d1, 
	0xa3c5, 0xaec8, 0xf5cd, 0xfbb1, 0xc0ca, 0xa7d8, 0xd2c7, 0xf0c7, 
	0xf7d6, 0xa7d5, 0xa6b7, 0xf5ba, 0xd4d2, 0xb6b8, 0xd0d7, 0xcbca, 
	0xfbcb, 0xccd5, 0xfab4, 0xeec1, 0xc9cf, 0xf0d8, 0xe4b3, 0xd6d0, 
	0xbdc8, 0xe1b2, 0xacb6, 0xbcb0, 0xf6b3, 0xb9cd, 0xafbf, 0xd3bc, 
	0xa6b9, 0xfcb0, 0xd2b4, 0xb1b1, 0xd1d4, 0xaac7, 0xebb0, 0xdcbb, 
	0xa8bf, 0xbcd5, 0xaec3, 0xb4d8, 0xa5c8, 0xc9bf, 0xc5b9, 0xd2d3, 
	0xd9d5, 0xa3b6, 0xb5df, 0xb6df, 0xf0b5, 0xbecb, 0xcfd8, 0xd0bd, 
	0xedc1, 0xbbd6, 0xb7ca, 0xb3df, 0xa8cc, 0xe4be, 0xc8b0, 0xb7df, 
	0xc4cb, 0xf4c7, 0xe2cd, 0x7fa5, 0x80a5, 0x81a5, 0x82a5, 0x83a5, 
	0x84a5, 0x85a5, 0x86a5, 0x87a5, 0x88a5, 0x89a5, 0x8aa5, 0x8ba5, 
	0x8ca5, 0x8da5, 0x8ea5, 0x8fa5, 0x90a5, 0x91a5, 0x92a5, 0x93a5, 
	0x94a5, 0x95a5, 0x96a5, 0x97a5, 0x98a5, 0x99a5, 0x9aa5, 0x9ba5, 
	0x9ca5, 0x9da5, 0x9ea5, 0x9fa5, 0xa0a5, 0xebd1, 0xa7ca, 0xabc5, 
	0xccc4, 0xd0d4, 0xfccb, 0xe1c4, 0xdebe, 0xc9c7, 0xf3d7, 0xd0ca, 
	0xbcb2, 0xbdc6, 0xd7d3, 0xcddb, 0xebba, 0xa5b8, 0xd8b1, 0xecce, 
	0xf2b4, 0xd3c8, 0xc7b0, 0xcbc6, 0xe2b3, 0xa9b5, 0xf5ca, 0xbeb1, 
	0xb4ce, 0xa9c4, 0xfdd4, 0xfdd5, 0xb8c4, 0xf1c3, 0xb5d8, 0xc0d3, 
	0xadd6, 0xa1cd, 0xa1a1, 0xb8b7, 0xfed0, 0xf1d3, 0xcfb9, 0xdfcd, 
	0xcab8, 0xfac9, 0xc3d3, 0xa6cb, 0xefcc, 0xc9d3, 0xd7bc, 0xeac9, 
	0xe2f1, 0xd7b0, 0xa4c6, 0xf3c3, 0xbfc4, 0xacc3, 0xb8ca, 0xafca, 
	0xbeca, 0xccba, 0xa8d1, 0xa2c1, 0xa9d8, 0xaab6, 0xb9c6, 0xd2c5, 
	0xc0d8, 0xa8d8, 0xbbbd, 0xe0d2, 0xa5ba, 0xc2b7, 0xf8d8, 0xefbb, 
	0xc1d2, 0xa1a1, 0xe9ce, 0xa5b7, 0xddd0, 0xfcb7, 0xd9d6, 0xfebc, 
	0xcec8, 0xf6d1, 0xf2d8, 0xddb7, 0xf3c6, 0xa1a1, 0xe2b9, 0xd7d0, 
	0xd7d5, 0xc8cf, 0xabc8, 0xb2b9, 0xd9d4, 0xf9b1, 0xd0c1, 0xccd0, 
	0xaebb, 0xd8d8, 0xbeeb, 0xd3c1, 0xd9d0, 0xefbf, 0xb3bd, 0xa1d3, 
	0xa3ce, 0xaabc, 0xf4c0, 0xaccd, 0xf5b5, 0xc2cd, 0xf5d3, 0xe7b4, 
	0xf7b8, 0xf2cf, 0xfbc3, 0xcfba, 0xd4b3, 0xf3ba, 0xbadf, 0xb8df, 
	0xf2d2, 0xd8bb, 0xeee0, 0xdadb, 0xd8b5, 0xdad4, 0xe7b9, 0xd8db, 
	0xdddb, 0xd7db, 0xedd9, 0xe0b6, 0xc4d2, 0xe4bf, 0xfdcd, 0xe9bc, 
	0xfae5, 0xc3ba, 0xfdcb, 0xe7c8, 0xf9e5, 0xd6d7, 0xe6b4, 0xeed3, 
	0xd8ca, 0xacd5, 0xb2b0, 0xc2cb, 0xe2bc, 0xd9d2, 0xddd6, 0xabb7, 
	0xa2b2, 0xeac4, 0x7fa6, 0x80a6, 0x81a6, 0x82a6, 0x83a6, 0x84a6, 
	0x85a6, 0x86a6, 0x87a6, 0x88a6, 0x89a6, 0x8aa6, 0x8ba6, 0x8ca6, 
	0x8da6, 0x8ea6, 0x8fa6, 0x90a6, 0x91a6, 0x92a6, 0x93a6, 0x94a6, 
	0x95a6, 0x96a6, 0x97a6, 0x98a6, 0x99a6, 0x9aa6, 0x9ba6, 0x9ca6, 
	0x9da6, 0x9ea6, 0x9fa6, 0xa0a6, 0xbdca, 0xdab3, 0xa6c3, 0xe2e2, 
	0xd6c8, 0xe7d0, 0xf9ca, 0xc9b3, 0xdbbf, 0xb8bf, 0xd0cd, 0xd5ca, 
	0xe7d4, 0xbcd6, 0xaed1, 0xf1d0, 0xfac7, 0xb7d2, 0xd0d3, 0xe0d0, 
	0xd3c6, 0xecd6, 0xe4b6, 0xceb4, 0xcbb4, 0xc0cb, 0xcac4, 0xeac8, 
	0xb9ba, 0xdbce, 0xadbd, 0xd8b3, 0xabcf, 0xc7c9, 0xdbce, 0xb4d1, 
	0xa1a1, 0xbab7, 0xd2bb, 0xb2c4, 0xf2ea, 0xd9b0, 0xf1d6, 0xd7c3, 
	0xe9f4, 0xbef3, 0xf2d1, 0xf0d3, 0xcfc0, 0xbcbf, 0xf8b6, 0xe7f1, 
	0xfab6, 0xb2ed, 0xe2c8, 0xdfc0, 0xa1bc, 0xbcb3, 0xd4d7, 0xc1d6, 
	0xcabe, 0xe0c9, 0xb6e2, 0xdbd6, 0xdef4, 0xabc9, 0xacb0, 0xe6b3, 
	0xaad1, 0xd0d0, 0xc2d2, 0xf7ce, 0xe4da, 0xaeb4, 0xe0ba, 0xbbce, 
	0xa1d7, 0xf9d8, 0xa2d9, 0xfad8, 0xe9b0, 0xf0b7, 0xceba, 0xc0b9, 
	0xf4d7, 0xd3d3, 0xa4d9, 0xc5cb, 0xecc9, 0xe8b5, 0xbcd5, 0xc6cb, 
	0xabb5, 0xb6d3, 0xf7d7, 0xe3c4, 0xaeb2, 0xcdb5, 0xe6c1, 0xe0d3, 
	0xfed8, 0xbcb2, 0xfdd8, 0xd2b6, 0xcbbf, 0xe2c3, 0xf8b1, 0xb1d2, 
	0xe4c0, 0xf0b1, 0xd0c5, 0xfbc0, 0xbec9, 0xd9c5, 0xd9bd, 0xfad6, 
	0xacc5, 0xbedb, 0xbbcf, 0xb4bc, 0xd1c2, 0xdfc1, 0xd4bf, 0xcccd, 
	0xe1ce, 0xf1b7, 0xa1a1, 0xc9b0, 0xf4b4, 0xc0df, 0xe2ce, 0xcab3, 
	0xc0c2, 0xfdbe, 0xd4b7, 0xe6b8, 0xb5b4, 0xc7ce, 0xfcce, 0xb1cb, 
	0xb3b3, 0xc5c4, 0xcdb7, 0xf0ba, 0xbdd1, 0xa8d6, 0xacba, 0xf7d2, 
	0xfdcc, 0xd1b4, 0xa7c0, 0xdab6, 0xf1e0, 0xbbb7, 0xd3bf, 0xb7d6, 
	0xaecc, 0x7fa7, 0x80a7, 0x81a7, 0x82a7, 0x83a7, 0x84a7, 0x85a7, 
	0x86a7, 0x87a7, 0x88a7, 0x89a7, 0x8aa7, 0x8ba7, 0x8ca7, 0x8da7, 
	0x8ea7, 0x8fa7, 0x90a7, 0x91a7, 0x92a7, 0x93a7, 0x94a7, 0x95a7, 
	0x96a7, 0x97a7, 0x98a7, 0x99a7, 0x9aa7, 0x9ba7, 0x9ca7, 0x9da7, 
	0x9ea7, 0x9fa7, 0xa0a7, 0xf9be, 0xb2bf, 0xf8bb, 0xf8d7, 0xb5bb, 
	0xdfdb, 0xb3d7, 0xd0bc, 0xb1d7, 0xcab6, 0xc1b7, 0xa4e6, 0xfee5, 
	0xeec3, 0xfdd1, 0xfbe5, 0xa5e6, 0xcbbc, 0xd1c8, 0xd7cd, 0xa2d0, 
	0xced7, 0xdae6, 0xc3d8, 0xeacd, 0xcecb, 0xeaba, 0xcede, 0xd6be, 
	0xa8c6, 0xf2c4, 0xb2ce, 0xaae1, 0xafe1, 0xedb2, 0xa7e1, 0xd7ce, 
	0xa3cf, 0xf2d0, 0xd3b1, 0xb2b4, 0xa2cd, 0xaac5, 0xdcb5, 0xaecd, 
	0xced0, 0xdde1, 0xdbd2, 0xfccd, 0xc9bc, 0xbed6, 0xccc8, 0xc0b3, 
	0xecbf, 0xeee2, 0xece2, 0xe4bd, 0xd2ce, 0xadb3, 0xb9bf, 0xb6b6, 
	0xbcbc, 0xf6b7, 0xf1be, 0xa4c5, 0xd1b0, 0xf3b6, 0xd2d5, 0xfac5, 
	0xe2b0, 0xe3ca, 0xb6b3, 0xdbd5, 0xe7b0, 0xb6cd, 0xa5d7, 0xd6d2, 
	0xa1a1, 0xc4b8, 0xa5b9, 0xfcd8, 0xb5ba, 0xfcb8, 0xf8ca, 0xeec0, 
	0xd3d0, 0xc4b2, 0xe5b4, 0xc5b6, 0xc8d5, 0xbde8, 0xbcc9, 0xcbb8, 
	0xdcb8, 0xbce8, 0xa1a1, 0xbdb2, 0xbfc3, 0xf3c7, 0xafb9, 0xb3c9, 
	0xdfc7, 0xf2c9, 0xc1b3, 0xe4e3, 0xe6c5, 0xf4cd, 0xf6be, 0xe5e3, 
	0xadcc, 0xe7e3, 0xe8e3, 0xe5b3, 0xbbc3, 0xfbc6, 0xd6ce, 0xb3bc, 
	0xdab7, 0xeae3, 0xece3, 0xebe3, 0xfcd9, 0xe6e3, 0xa1a1, 0xcad2, 
	0xeed4, 0xc6d7, 0xd6d4, 0xc4be, 0xcec0, 0xb5c4, 0xfccb, 0xd2b5, 
	0xf1bf, 0xc1be, 0xaef0, 0xa6b8, 0xd0c4, 0xe9b5, 0xedd4, 0xa2b6, 
	0xd3d2, 0xbdcb, 0xe3d0, 0xbacd, 0xbfbe, 0xb5cf, 0xb1ba, 0xa4d0, 
	0xc1eb, 0xceb8, 0xe2d6, 0xd8b8, 0xc7b6, 0xfdd3, 0xbcc1, 0xa2c3, 
	0x7fa8, 0x80a8, 0x81a8, 0x82a8, 0x83a8, 0x84a8, 0x85a8, 0x86a8, 
	0x87a8, 0x88a8, 0x89a8, 0x8aa8, 0x8ba8, 0x8ca8, 0x8da8, 0x8ea8, 
	0x8fa8, 0x90a8, 0x91a8, 0x92a8, 0x93a8, 0x94a8, 0x95a8, 0x96a8, 
	0x97a8, 0x98a8, 0x99a8, 0x9aa8, 0x9ba8, 0x9ca8, 0x9da8, 0x9ea8, 
	0x9fa8, 0xa0a8, 0xf3d3, 0xd6c9, 0xfbbc, 0xc7bd, 0xd4d1, 0xc8b9, 
	0xb9b6, 0xb9f5, 0xb4b1, 0xe0b3, 0xdfd7, 0xe3d7, 0xedc9, 0xb5b3, 
	0xc1d0, 0xbdb3, 0xd8d3, 0xc6e5, 0xb8d1, 0xf9c6, 0xb2d1, 0xd8d2, 
	0xcfd0, 0xb0d0, 0xeeb0, 0xc7c4, 0xcfd3, 0xc9b2, 0xefc0, 0xc0b7, 
	0xeec8, 0xe5da, 0xe6da, 0xa1a1, 0xa2b2, 0xd4b9, 0xe9c8, 0xc2ca, 
	0xa9d0, 0xc7d1, 0xedcf, 0xa9be, 0xf0d1, 0xc0d2, 0xccca, 0xd1bc, 
	0xb9ca, 0xd0c0, 0xa9b9, 0xfdc0, 0xb4c0, 0xa9d9, 0xdbb0, 0xa2b2, 
	0xdeb3, 0xe5c5, 0xacd9, 0xd8c2, 0xabd9, 0xaad9, 0xa7d9, 0xa1a1, 
	0xc3cd, 0xf9b6, 0xeed9, 0xbdc1, 0xdfbe, 0xe4c6, 0xe4b5, 0xfdd9, 
	0xafba, 0xccbf, 0xafc8, 0xa2cb, 0xccb4, 0xbdb5, 0xceb9, 0xc6d6, 
	0xe7b6, 0xc0db, 0xa1a1, 0xe4d7, 0xadd0, 0xbfd7, 0xb0b1, 0xd4d8, 
	0xedbe, 0xb6d0, 0xf4d0, 0xa1c8, 0xe5ca, 0xdcca, 0xb6ce, 0xc7ba, 
	0xa7bf, 0xdec5, 0xbeb9, 0xd7be, 0xebc9, 0xc8df, 0xcddf, 0xe4d6, 
	0xd8c5, 0xf4ba, 0xc0b8, 0xc9df, 0xcedf, 0xcdba, 0xcbdf, 0xd8c4, 
	0xdcd6, 0xa6d5, 0xfcc3, 0xccbe, 0xccb9, 0xacc0, 0xc0bf, 0xbac6, 
	0xe1db, 0xc2c6, 0xb9cc, 0xa4c0, 0xe5db, 0xb9d2, 0xeeb7, 0xe6c6, 
	0xcec4, 0xd9d1, 0xbcb1, 0xaae6, 0xdec6, 0xafce, 0xc3c3, 0xddc4, 
	0xc3b9, 0xb7c4, 0xe3bd, 0xa9e6, 0xbcca, 0xd5d0, 0xa2e6, 0xa8e6, 
	0xe3c4, 0xa6e6, 0xa1a1, 0xcfc3, 0xc2b9, 0xbebc, 0xdad7, 0xa8b6, 
	0xd9b9, 0xcbd2, 0xe6d6, 0xf0cd, 0xd0c9, 0xfcc7, 0xd3be, 0x7fa9, 
	0x80a9, 0x81a9, 0x82a9, 0x83a9, 0x84a9, 0x85a9, 0x86a9, 0x87a9, 
	0x88a9, 0x89a9, 0x8aa9, 0x8ba9, 0x8ca9, 0x8da9, 0x8ea9, 0x8fa9, 
	0x90a9, 0x91a9, 0x92a9, 0x93a9, 0x94a9, 0x95a9, 0x96a9, 0x97a9, 
	0x98a9, 0x99a9, 0x9aa9, 0x9ba9, 0x9ca9, 0x9da9, 0x9ea9, 0x9fa9, 
	0xa0a9, 0xecbd, 0xbae1, 0xd4b8, 0xb6b0, 0xd2d1, 0xb6e1, 0xb7e1, 
	0xc0d4, 0xb1c1, 0xe3d6, 0xfbcc, 0xc1c5, 0xafb2, 0xfbe0, 0xd2d0, 
	0xfdb8, 0xeab5, 0xaeb8, 0xd7b5, 0xd2e2, 0xd3d1, 0xd2cf, 0xa1bb, 
	0xf3e5, 0xf9cd, 0xf7d5, 0xf0b7, 0xcbb1, 0xc3e3, 0xd2d6, 0xf6ba, 
	0xeec4, 0xdeb7, 0xf3e2, 0xfad5, 0xd3c7, 0xf0e2, 0xc0b2, 0xd6b9, 
	0xc2c5, 0xf9e2, 0xd4d0, 0xf5e2, 0xf6e2, 0xf2e2, 0xf2bb, 0xdee3, 
	0xbfb7, 0xe5ec, 0xf9cb, 0xd0b3, 0xadc0, 0xe8b0, 0xf4d6, 0xf2c3, 
	0xf7b7, 0xa8c4, 0xdcbe, 0xd0d5, 0xfbc5, 0xd8cd, 0xceb0, 0xd7c5, 
	0xe9c4, 0xeac5, 0xe9b3, 0xbad1, 0xd5b9, 0xbed7, 0xb4c4, 0xc4c5, 
	0xd6b5, 0xd5de, 0xa7b1, 0xd0be, 0xcfcd, 0xd6de, 0xf0b2, 0xa7cc, 
	0xe0c1, 0xc5b7, 0xabb8, 0xb6ec, 0xfacd, 0xf4ce, 0xd7d2, 0xfdb2, 
	0xa5c0, 0xbab0, 0xf7c3, 0xc0ea, 0xe8bb, 0xbfea, 0xbbea, 0xfdc9, 
	0xfeb7, 0xf3c5, 0xbcba, 0xcae8, 0xedd5, 0xabb6, 0xfbb9, 0xc3e8, 
	0xcbe8, 0xc1e8, 0xa6d6, 0xd6c1, 0xadb1, 0xdcbd, 0xe5b0, 0xf7cd, 
	0xc9cb, 0xf6ce, 0xc6e8, 0xb6c3, 0xa1a1, 0xcce8, 0xc2e8, 0xbdea, 
	0xc0d0, 0xe4ce, 0xe7c6, 0xe2e9, 0xa5c3, 0xd5b7, 0xfcc6, 0xa2d7, 
	0xbed3, 0xfbe3, 0xdac3, 0xe0c4, 0xd3ba, 0xc1b9, 0xb4d5, 0xd3d5, 
	0xa8b2, 0xadc4, 0xa8b7, 0xfce3, 0xd0b7, 0xb9d0, 0xcdd3, 0xf6bf, 
	0xdabe, 0xf4e3, 0xf6c7, 0xf3e3, 0xd8d1, 0xced6, 0xddc5, 0xbab7, 
	0xb4b2, 0xa1a1, 0xfde3, 0xa1a1, 0xf7e3, 0xf6e3, 0x7faa, 0x80aa, 
	0x81aa, 0x82aa, 0x83aa, 0x84aa, 0x85aa, 0x86aa, 0x87aa, 0x88aa, 
	0x89aa, 0x8aaa, 0x8baa, 0x8caa, 0x8daa, 0x8eaa, 0x8faa, 0x90aa, 
	0x91aa, 0x92aa, 0x93aa, 0x94aa, 0x95aa, 0x96aa, 0x97aa, 0x98aa, 
	0x99aa, 0x9aaa, 0x9baa, 0x9caa, 0x9daa, 0x9eaa, 0x9faa, 0xa0aa, 
	0xbbbf, 0xd7d1, 0xb4b3, 0xb6b4, 0xcbd6, 0xc0c5, 0xf9d5, 0xd6b0, 
	0xe6b0, 0xc1c4, 0xefce, 0xb4d7, 0xf2e1, 0xd1be, 0xb7b9, 0xfcba, 
	0xe6cd, 0xe5e7, 0xe4e7, 0xb5c3, 0xa1a1, 0xdadb, 0xdef0, 0xedb8, 
	0xcebe, 0xc4b5, 0xdbd3, 0xa4c3, 0xb1d6, 0xaad6, 0xf9ce, 0xe7c9, 
	0xebec, 0xeec6, 0xfcb1, 0xccf4, 0xd5bf, 0xb7f1, 0xc3f3, 0xc0be, 
	0xe8d8, 0xbcc7, 0xc2d8, 0xdfd5, 0xceb7, 0xcab7, 0xabd6, 0xc5eb, 
	0xc9b9, 0xc6eb, 0xe7bc, 0xc8eb, 0xbeb7, 0xcfbf, 0xd4ce, 0xa7f4, 
	0xe1c9, 0xbcb7, 0xa5d6, 0xbddc, 0xc5b0, 0xbfd1, 0xcfdc, 0xdbc7, 
	0xa8bb, 0xd2b7, 0xe6bd, 0xbed0, 0xbfdc, 0xa1a1, 0xc1dc, 0xc0dc, 
	0xc6dc, 0xa2bb, 0xadca, 0xf5b3, 0xedb1, 0xfed4, 0xadd3, 0xb5b7, 
	0xfcbd, 0xdbc9, 0xa1db, 0xf1c7, 0xfdda, 0xc9b2, 0xf0bd, 0xa4b3, 
	0xc5c3, 0xb7b8, 0xd3cd, 0xa2b0, 0xe8d7, 0xbdb8, 0xe9da, 0xbff6, 
	0xead3, 0xe0c7, 0xc7b7, 0xbdd8, 0xa4cd, 0xc1c1, 0xc5d0, 0xd6c7, 
	0xeeba, 0xe3b1, 0xc0cf, 0xb8d9, 0xcec7, 0xa3b1, 0xd9b4, 0xc2c2, 
	0xfdb7, 0xb9d9, 0xa1bf, 0xd7cb, 0xeace, 0xfec0, 0xedb6, 0xb5cf, 
	0xb5d9, 0xded9, 0xe1d3, 0xd6be, 0xf0d9, 0xb0c3, 0xd0eb, 0xdab9, 
	0xb2c9, 0xeacc, 0xf7cf, 0xb0c7, 0xddd8, 0xcbbf, 0xf2d4, 0xc2d3, 
	0xe3c3, 0xaab2, 0xa2be, 0xe9d9, 0xcfc4, 0xb4c8, 0xf1ba, 0xd1c5, 
	0xa7d2, 0xa7b0, 0xc9d7, 0xa5b0, 0xd5d4, 0xcccf, 0xd7df, 0xc8bf, 
	0xdbcd, 0xd3df, 0xcad1, 0xe4df, 0xb7c6, 0x7fab, 0x80ab, 0x81ab, 
	0x82ab, 0x83ab, 0x84ab, 0x85ab, 0x86ab, 0x87ab, 0x88ab, 0x89ab, 
	0x8aab, 0x8bab, 0x8cab, 0x8dab, 0x8eab, 0x8fab, 0x90ab, 0x91ab, 
	0x92ab, 0x93ab, 0x94ab, 0x95ab, 0x96ab, 0x97ab, 0x98ab, 0x99ab, 
	0x9aab, 0x9bab, 0x9cab, 0x9dab, 0x9eab, 0x9fab, 0xa0ab, 0xe5ba, 
	0xfeb9, 0xa9bf, 0xebe5, 0xdbd4, 0xdddf, 0xe3df, 0xd6df, 0xdedf, 
	0xf3e0, 0xb9b4, 0xcdd0, 0xf3db, 0xabd4, 0xb8b9, 0xc7b3, 0xe5bf, 
	0xf2db, 0xc8de, 0xf5c6, 0xe0d7, 0xfcbf, 0xbcdb, 0xaabd, 0xb0e6, 
	0xcbd7, 0xafe6, 0xccd2, 0xdecd, 0xd1c0, 0xb6d6, 0xa6d2, 0xe9bc, 
	0xfecd, 0xf6d2, 0xa2ba, 0xfbd0, 0xc2bb, 0xd2ca, 0xcdbf, 0xb6e5, 
	0xe2b7, 0xbaca, 0xc1c6, 0xacca, 0xddce, 0xc5d6, 0xbce1, 0xefcf, 
	0xdbb5, 0xa7cb, 0xa1a1, 0xc4d3, 0xd4e2, 0xc8b6, 0xa8bd, 0xc4de, 
	0xf4e5, 0xe5d1, 0xdcba, 0xfdb4, 0xb2bb, 0xc9c2, 0xdfe1, 0xe1e1, 
	0xe0e1, 0xadc5, 0xbccb, 0xa1b5, 0xb1bc, 0xf5d4, 0xb9d4, 0xd0bb, 
	0xa1c7, 0xdeba, 0xd6bb, 0xe3ba, 0xd1ca, 0xf1cc, 0xb2b6, 0xa1e3, 
	0xf4d0, 0xe2b1, 0xddb0, 0xdacd, 0xb4b0, 0xb4c6, 0xc3ca, 0xd6b3, 
	0xd7de, 0xa7d7, 0xb8d6, 0xb0b9, 0xbdbf, 0xfcd5, 0xa8c0, 0xb0ca, 
	0xa9cb, 0xf4cc, 0xd2b9, 0xfed5, 0xcab9, 0xbded, 0xa9ca, 0xc8bc, 
	0xbab4, 0xd1d5, 0xb3d3, 0xc1c3, 0xc7ca, 0xc7d0, 0xf2d7, 0xc5ea, 
	0xa1a1, 0xc2ea, 0xc1ca, 0xbec8, 0xf9d6, 0xe1c8, 0xb3c4, 0xedbc, 
	0xdcbc, 0xddbf, 0xa4d5, 0xd1e8, 0xc2bf, 0xfab1, 0xccb8, 0xd5b9, 
	0xd6e8, 0xe9b2, 0xdbe8, 0xd8b0, 0xf5d7, 0xf8c1, 0xd2e8, 0xd4e8, 
	0xdce8, 0xd8e8, 0xe2c6, 0xe1cd, 0xead1, 0xf9b4, 0xceb6, 0xbeb6, 
	0xfec5, 0xfab7, 0xaac8, 0xf3d1, 0xded6, 0xe9ba, 0xf7c1, 0xf2bd, 
	0xa3e4, 0xfdb6, 0xb4b6, 0xb4cf, 0x7fac, 0x80ac, 0x81ac, 0x82ac, 
	0x83ac, 0x84ac, 0x85ac, 0x86ac, 0x87ac, 0x88ac, 0x89ac, 0x8aac, 
	0x8bac, 0x8cac, 0x8dac, 0x8eac, 0x8fac, 0x90ac, 0x91ac, 0x92ac, 
	0x93ac, 0x94ac, 0x95ac, 0x96ac, 0x97ac, 0x98ac, 0x99ac, 0x9aac, 
	0x9bac, 0x9cac, 0x9dac, 0x9eac, 0x9fac, 0xa0ac, 0xeebb, 0xa2c7, 
	0xc9c5, 0xdad0, 0xe5c2, 0xc3b1, 0xa1e4, 0xa2e4, 0xa1a1, 0xb9d0, 
	0xace4, 0xade4, 0xa9e4, 0xaae4, 0xc5ec, 0xaace, 0xfeb1, 0xe6be, 
	0xbcbe, 0xbfcc, 0xa8d5, 0xdac5, 0xa1a1, 0xbceb, 0xfcc9, 0xf4ea, 
	0xa1a1, 0xf7e1, 0xddba, 0xc6bd, 0xe8e7, 0xbac9, 0xa3b2, 0xe1c1, 
	0xe4d5, 0xeae7, 0xe9e7, 0xf5c9, 0xc2b1, 0xb7ce, 0xe7bd, 0xb0ee, 
	0xb1ee, 0xdfd2, 0xccb0, 0xeabd, 0xa1a1, 0xe0f0, 0xefb9, 0xd4bd, 
	0xcabb, 0xa7f0, 0xafd3, 0xe8c5, 0xadb1, 0xd1d6, 0xa1ca, 0xefed, 
	0xe0cf, 0xbcc3, 0xb4bf, 0xdcb6, 0xcec5, 0xf0ed, 0xe6f1, 0xb0c9, 
	0xd0d1, 0xf6c6, 0xb3bf, 0xecec, 0xedec, 0xedc6, 0xf3ec, 0xedd3, 
	0xaed8, 0xc6bf, 0xebc3, 0xefc7, 0xa9b4, 0xbbcd, 0xcdb8, 0xc4f3, 
	0xd1d7, 0xfbe6, 0xecba, 0xcdbc, 0xd2c8, 0xfce6, 0xbcd4, 0xfae6, 
	0xd7b8, 0xc0c3, 0xe0f4, 0xa3eb, 0xcdc4, 0xa3cb, 0xcbb6, 0xaed2, 
	0xd6c5, 0xe3f1, 0xdfc5, 0xb8ce, 0xd0eb, 0xb3b1, 0xfaba, 0xceeb, 
	0xa5cc, 0xfbb0, 0xb7d8, 0xd5eb, 0xc2d6, 0xaef4, 0xd1dc, 0xb6b7, 
	0xa9c3, 0xc4dc, 0xc1bf, 0xe0bf, 0xd1c7, 0xf4c8, 0xafc3, 0xd4dc, 
	0xdbdc, 0xe7c3, 0xa2d3, 0xc2d7, 0xd9dc, 0xa6cc, 0xb7d4, 0xfab0, 
	0xdfdc, 0xb6b9, 0xbdb1, 0xe2dc, 0xb0c5, 0xe7ba, 0xb5f2, 0xb3f2, 
	0xdcd1, 0xc0c9, 0xaad2, 0xeebd, 0xc6bc, 0xa9b6, 0xbcb8, 0xead5, 
	0xbab8, 0xb0b8, 0xf1f4, 0xbfc5, 0xfcbe, 0xecb9, 0xf6ca, 0xc8e5, 
	0xf6cc, 0xcfb5, 0xc4e5, 0x7fad, 0x80ad, 0x81ad, 0x82ad, 0x83ad, 
	0x84ad, 0x85ad, 0x86ad, 0x87ad, 0x88ad, 0x89ad, 0x8aad, 0x8bad, 
	0x8cad, 0x8dad, 0x8ead, 0x8fad, 0x90ad, 0x91ad, 0x92ad, 0x93ad, 
	0x94ad, 0x95ad, 0x96ad, 0x97ad, 0x98ad, 0x99ad, 0x9aad, 0x9bad, 
	0x9cad, 0x9dad, 0x9ead, 0x9fad, 0xa0ad, 0xfcb5, 0xc8c6, 0xc6e5, 
	0xcae5, 0xbcbd, 0xc9c0, 0xf4d3, 0xa1a1, 0xf5c7, 0xfaf4, 0xd8d6, 
	0xc5e3, 0xdecf, 0xaac2, 0xb0c4, 0xb5bd, 0xe6c3, 0xefb8, 0xa4ce, 
	0xc2be, 0xf4d2, 0xb3d2, 0xe7b7, 0xc9b7, 0xb3ca, 0xd7ca, 0xe3cf, 
	0xcbb3, 0xf1d9, 0xc4d9, 0xb6b1, 0xc2b7, 0xa9b8, 0xebbe, 0xc5d9, 
	0xbad9, 0xbbd9, 0xd2d0, 0xa9c1, 0xb5d6, 0xe8bd, 0xd0d2, 0xb9b5, 
	0xc7c3, 0xb3b0, 0xf6d8, 0xf3be, 0xc6d9, 0xe3be, 0xabb3, 0xf6b8, 
	0xf2ba, 0xc8cc, 0xbdd9, 0xded0, 0xc1d9, 0xdfc4, 0xc2d9, 0xd7c2, 
	0xd6b2, 0xe6bc, 0xa9d4, 0xa4da, 0xa3da, 0xb3b6, 0xe8c1, 0xbcd7, 
	0xf2b5, 0xcac6, 0xe0d8, 0xdecc, 0xd5b8, 0xfeb0, 0xcbb7, 0xe4c7, 
	0xadd4, 0xc8d8, 0xc5db, 0xdac9, 0xc6cc, 0xe4d1, 0xa1e0, 0xdfba, 
	0xe7b8, 0xdcd5, 0xf4cb, 0xb8b2, 0xeddf, 0xa8c1, 0xdebf, 0xb1d4, 
	0xa6b0, 0xf8cf, 0xc4c4, 0xb6c5, 0xf3df, 0xbdb4, 0xecdf, 0xf1df, 
	0xd4c6, 0xf4e0, 0xa1b9, 0xd2c6, 0xf1c2, 0xa3b0, 0xa1a1, 0xc4cf, 
	0xd7cc, 0xcade, 0xc9de, 0xb6e6, 0xefc4, 0xc8c4, 0xeabe, 0xe9d3, 
	0xb8e6, 0xa7bc, 0xefc9, 0xb7e6, 0xe4c3, 0xf0b6, 0xb2e6, 0xb3e6, 
	0xefcb, 0xa1a1, 0xd7d4, 0xa6ba, 0xd2bc, 0xe7d1, 0xacb9, 0xfccf, 
	0xddc8, 0xb7e5, 0xe4c9, 0xbcd0, 0xb9d5, 0xece5, 0xcdc7, 0xbfcf, 
	0xfebe, 0xf8d3, 0xebb6, 0xe5b7, 0xbab5, 0xb6c7, 0xade1, 0xeeb2, 
	0xafcf, 0xa6ca, 0xe2bf, 0xa5cd, 0xf9d7, 0xf5c8, 0xbdcd, 0xb6be, 
	0xecd0, 0xa6ed, 0x7fae, 0x80ae, 0x81ae, 0x82ae, 0x83ae, 0x84ae, 
	0x85ae, 0x86ae, 0x87ae, 0x88ae, 0x89ae, 0x8aae, 0x8bae, 0x8cae, 
	0x8dae, 0x8eae, 0x8fae, 0x90ae, 0x91ae, 0x92ae, 0x93ae, 0x94ae, 
	0x95ae, 0x96ae, 0x97ae, 0x98ae, 0x99ae, 0x9aae, 0x9bae, 0x9cae, 
	0x9dae, 0x9eae, 0x9fae, 0xa0ae, 0xa7ed, 0xdcb3, 0xd6bf, 0xa1cb, 
	0xa7b9, 0xf7b6, 0xa2cf, 0xc4c7, 0xf2ce, 0xa4e3, 0xb7ba, 0xdabb, 
	0xa9e3, 0xc3d4, 0xa3e3, 0xc8c9, 0xadc8, 0xfcea, 0xc3c4, 0xd3c9, 
	0xaed0, 0xf1d5, 0xb6b2, 0xe6ce, 0xa6c0, 0xf3c4, 0xbdd7, 0xa6cd, 
	0xe8be, 0xeccd, 0xb2c5, 0xecb4, 0xa4b0, 0xb4ba, 0xc6b0, 0xa7d0, 
	0xcdf4, 0xcfc1, 0xd4c5, 0xc3c2, 0xb1ca, 0xfabd, 0xccea, 0xcebb, 
	0xb9c9, 0xcec9, 0xa1a1, 0xcbea, 0xe9ca, 0xb7cb, 0xdeeb, 0xcac0, 
	0xa3d0, 0xcbba, 0xb8b0, 0xf2bf, 0xb8bb, 0xf9b8, 0xf0b9, 0xdbbd, 
	0xf2e8, 0xe1ca, 0xf5c0, 0xc0d7, 0xa3c9, 0xd4d4, 0xf1b2, 0xa9cd, 
	0xeee8, 0xf1b8, 0xd2cc, 0xead6, 0xa6ce, 0xa8cb, 0xa1a1, 0xece8, 
	0xe2ca, 0xb3d1, 0xf3d2, 0xf8c6, 0xf5d1, 0xb1b0, 0xa4ba, 0xb3eb, 
	0xa9cc, 0xcbc0, 0xe9cc, 0xfbcf, 0xfee3, 0xd6c6, 0xfebd, 0xa3ba, 
	0xe3d5, 0xb8e4, 0xa1a1, 0xe6c9, 0xa1b8, 0xa3bf, 0xa1d4, 0xc6ba, 
	0xbfd3, 0xa1a1, 0xa4e4, 0xf9c4, 0xa1a1, 0xb9e4, 0xc8ec, 0xe6ba, 
	0xbebf, 0xd3c0, 0xd2c1, 0xdace, 0xf9b5, 0xd8cc, 0xc7c0, 0xc1cf, 
	0xb7b1, 0xeac0, 0xfae1, 0xa1a1, 0xe0b0, 0xf0c1, 0xe5c5, 0xe9d6, 
	0xe7b9, 0xf3e7, 0xcfc5, 0xb6c4, 0xf3d0, 0xcedb, 0xf4c1, 0xb2bc, 
	0xa1b2, 0xa2d6, 0xa3c6, 0xe1f0, 0xd2be, 0xdbcc, 0xeed5, 0xe8f0, 
	0xe3f0, 0xdeb8, 0xe5f0, 0xe6d2, 0xc1ee, 0xbbb0, 0xa3d1, 0xe6d5, 
	0xdfc3, 0xa3d5, 0xd8be, 0xe9c5, 0xe8d5, 0xd2d4, 0xc0ed, 0xc6c6, 
	0xe9c9, 0x7faf, 0x80af, 0x81af, 0x82af, 0x83af, 0x84af, 0x85af, 
	0x86af, 0x87af, 0x88af, 0x89af, 0x8aaf, 0x8baf, 0x8caf, 0x8daf, 
	0x8eaf, 0x8faf, 0x90af, 0x91af, 0x92af, 0x93af, 0x94af, 0x95af, 
	0x96af, 0x97af, 0x98af, 0x99af, 0x9aaf, 0x9baf, 0x9caf, 0x9daf, 
	0x9eaf, 0x9faf, 0xa0af, 0xc6ed, 0xbeed, 0xa1a1, 0xc4ed, 0xdac5, 
	0xd8c3, 0xd3d3, 0xf4ec, 0xeecb, 0xe6d7, 0xf1c9, 0xa3d7, 0xf3ec, 
	0xf1ec, 0xd3b3, 0xf7ef, 0xedd1, 0xe2d7, 0xd8c7, 0xc8d6, 0xd8c3, 
	0xadd5, 0xbaf1, 0xbed5, 0xcab0, 0xa6d0, 0xdbb7, 0xc4b7, 0xb4c9, 
	0xc6ce, 0xc9ce, 0xd8cb, 0xf7cb, 0xbfb4, 0xa6c5, 0xa2e7, 0xb6bc, 
	0xa1e7, 0xc9c4, 0xbdd6, 0xd7b7, 0xb1c8, 0xb9ee, 0xe1b8, 0xe1b3, 
	0xccce, 0xc8ea, 0xc5d4, 0xfbb8, 0xd2b0, 0xc4ba, 0xa2b5, 0xa2b9, 
	0xd7eb, 0xacd6, 0xc8d2, 0xb2d0, 0xd9eb, 0xd8eb, 0xe0b4, 0xd8d0, 
	0xecb8, 0xf6c2, 0xdcc4, 0xb9bc, 0xddeb, 0xe8bf, 0xf4b3, 0xabf4, 
	0xa8d2, 0xc2f3, 0xbdba, 0xb3f4, 0xb2f4, 0xe3b0, 0xbbdb, 0xa3c3, 
	0xc4bb, 0xf3c0, 0xa3be, 0xd7c8, 0xf6bc, 0xddb2, 0xf0d2, 0xeedc, 
	0xf3dc, 0xc8d7, 0xe3c8, 0xe8b2, 0xf8dc, 0xf7dc, 0xefdc, 0xc4b4, 
	0xf5dc, 0xaff2, 0xc3ce, 0xbdf2, 0xbef2, 0xe9d4, 0xbff2, 0xf6b0, 
	0xbcf2, 0xc1d1, 0xa5cb, 0xd4d6, 0xacd4, 0xc7f1, 0xc5f1, 0xbbd6, 
	0xc7bc, 0xa6da, 0xd6cc, 0xa7da, 0xa8da, 0xb6d1, 0xd0cd, 0xb5d1, 
	0xfdc6, 0xa1a1, 0xa1a1, 0xf1c6, 0xf2b2, 0xaab1, 0xc6b2, 0xb1b9, 
	0xf0c6, 0xaab9, 0xf9d0, 0xede9, 0xa1a1, 0xe8c8, 0xcdcb, 0xe6c4, 
	0xd4c3, 0xcbcd, 0xa1a1, 0xd8bb, 0xd3cc, 0xb7d7, 0xcbe5, 0xc5b1, 
	0xdfe7, 0xa4bf, 0xc2ba, 0xabdb, 0xc6be, 0xe4c5, 0xc3d7, 0xa4b6, 
	0xebd5, 0xc8ee, 0xaab8, 0xc7ee, 0xc1c9, 0xbad4, 0xf3d5, 0xb8b6, 
	0x7fb0, 0x80b0, 0x81b0, 0x82b0, 0x83b0, 0x84b0, 0x85b0, 0x86b0, 
	0x87b0, 0x88b0, 0x89b0, 0x8ab0, 0x8bb0, 0x8cb0, 0x8db0, 0x8eb0, 
	0x8fb0, 0x90b0, 0x91b0, 0x92b0, 0x93b0, 0x94b0, 0x95b0, 0x96b0, 
	0x97b0, 0x98b0, 0x99b0, 0x9ab0, 0x9bb0, 0x9cb0, 0x9db0, 0x9eb0, 
	0x9fb0, 0xa0b0, 0xddb1, 0xc2c9, 0xfdb3, 0xeada, 0xfdc9, 0xa7d6, 
	0xa2bc, 0xedc2, 0xc7b9, 0xdfb8, 0xb7b6, 0xaad8, 0xedb9, 0xacc7, 
	0xdbd4, 0xb1ce, 0xa3cd, 0xd9bc, 0xc8d9, 0xbcd9, 0xf6d7, 0xb0ce, 
	0xa1bd, 0xbcc5, 0xcbd9, 0xc9d9, 0xecd5, 0xe0b2, 0xb5cd, 0xabc6, 
	0xbfd9, 0xa1a1, 0xa1a1, 0xb5b6, 0xe1c3, 0xcbbb, 0xf4bc, 0xb1b8, 
	0xd5c0, 0xf1ce, 0xb1bf, 0xafb6, 0xebd9, 0xcbde, 0xd7b3, 0xe4c4, 
	0xf8c7, 0xd2d8, 0xceb2, 0xfcc2, 0xccc9, 0xbec5, 0xb2c0, 0xc4d7, 
	0xc6d1, 0xc8b7, 0xd0bf, 0xa1b0, 0xaab3, 0xa2e0, 0xcace, 0xfbdf, 
	0xa8ce, 0xa1c6, 0xeec4, 0xdbca, 0xa8e0, 0xa3bb, 0xcecf, 0xa6e0, 
	0xfadf, 0xa2e0, 0xa6c8, 0xfab9, 0xf6e0, 0xf2d3, 0xe1bc, 0xd1db, 
	0xd1b6, 0xbab2, 0xfddb, 0xf9bb, 0xc3cc, 0xc2b6, 0xb4d6, 0xe0c5, 
	0xbbb9, 0xddc9, 0xa2c8, 0xa6c2, 0xf1cd, 0xbeb8, 0xb7c0, 0xb9e6, 
	0xbde6, 0xbee6, 0xe9bb, 0xc5c6, 0xbbe6, 0xebca, 0xdcbf, 0xfad2, 
	0xc4bc, 0xc5bc, 0xdecb, 0xdcc3, 0xbece, 0xa8d7, 0xabbd, 0xc0cd, 
	0xebcc, 0xa1a1, 0xe7b3, 0xc7e1, 0xe9c6, 0xc8e1, 0xc2d1, 0xbfe1, 
	0xa5c0, 0xc0b1, 0xdeb4, 0xd8c2, 0xc5e1, 0xc2e1, 0xdab8, 0xb2b3, 
	0xa3b3, 0xf8b4, 0xcad5, 0xa1e1, 0xb5bf, 0xb9d3, 0xfcca, 0xd6e2, 
	0xd7e2, 0xc5d5, 0xbfc7, 0xe7e5, 0xf2b1, 0xcab2, 0xf2b5, 0xc3b5, 
	0xe3e1, 0xd3b4, 0xc7c5, 0xf9d3, 0xe2e1, 0xe4e1, 0xc1d3, 0xbcbb, 
	0xa4cf, 0xc6d3, 0xfac4, 0xefcd, 0xb2e3, 0xebb5, 0xa1a1, 0x7fb1, 
	0x80b1, 0x81b1, 0x82b1, 0x83b1, 0x84b1, 0x85b1, 0x86b1, 0x87b1, 
	0x88b1, 0x89b1, 0x8ab1, 0x8bb1, 0x8cb1, 0x8db1, 0x8eb1, 0x8fb1, 
	0x90b1, 0x91b1, 0x92b1, 0x93b1, 0x94b1, 0x95b1, 0x96b1, 0x97b1, 
	0x98b1, 0x99b1, 0x9ab1, 0x9bb1, 0x9cb1, 0x9db1, 0x9eb1, 0x9fb1, 
	0xa0b1, 0xe9c7, 0xace3, 0xeae2, 0xa7cf, 0xbfb5, 0xafe3, 0xe8cc, 
	0xb0e3, 0xa9ce, 0xc2bc, 0xb1e3, 0xa1a1, 0xddc6, 0xa9ea, 0xe8ec, 
	0xd3c2, 0xd8bf, 0xedbe, 0xb4d2, 0xbdcc, 0xd3bd, 0xddbd, 0xf5c5, 
	0xf2be, 0xebb4, 0xdfde, 0xdad1, 0xf4b5, 0xa8c9, 0xd2b9, 0xd1de, 
	0xc6cd, 0xd5c2, 0xdaca, 0xf5d5, 0xc9b2, 0xe4de, 0xc5c5, 0xcdcc, 
	0xc6cf, 0xedc4, 0xe6de, 0xe1c9, 0xe0de, 0xd6b1, 0xbdb0, 0xc8be, 
	0xccbd, 0xdcb0, 0xf4c6, 0xf4c3, 0xf0d0, 0xb7eb, 0xa1a1, 0xb1d0, 
	0xfaf5, 0xb6d5, 0xe5d7, 0xfdd0, 0xbaec, 0xbbec, 0xe7d6, 0xedcd, 
	0xeece, 0xbfb3, 0xdebb, 0xa1a1, 0xdcb2, 0xc3db, 0xfbcd, 0xbac1, 
	0xddcc, 0xd2c9, 0xf7e8, 0xf3e8, 0xcbb8, 0xb0cd, 0xa1a1, 0xe0ce, 
	0xa3b9, 0xb5d0, 0xe8e8, 0xfac6, 0xf3cb, 0xf0b0, 0xb7c3, 0xd9e8, 
	0xf5cc, 0xe6c0, 0xc9e8, 0xa1a1, 0xa1a1, 0xfbd3, 0xb1c9, 0xc1ba, 
	0xf2c7, 0xe2c7, 0xd1cf, 0xb9c1, 0xbeb4, 0xc8e4, 0xbad2, 0xadb5, 
	0xcacc, 0xd9d3, 0xedcc, 0xb3c7, 0xe5c7, 0xbfe4, 0xdcc1, 0xc4d1, 
	0xe7ca, 0xcce4, 0xc1e4, 0xcdd1, 0xd4ba, 0xecbb, 0xa8d4, 0xc0e4, 
	0xe0c6, 0xbee4, 0xadba, 0xe1c0, 0xf9d2, 0xd4cc, 0xd9c2, 0xeec9, 
	0xb4bb, 0xbbbe, 0xfdcf, 0xcdd7, 0xa2b8, 0xe3b4, 0xc3e4, 0xc6e4, 
	0xebc5, 0xc9d1, 0xb8ba, 0xe9b7, 0xa9cf, 0xaccb, 0xa3c7, 0xe7c0, 
	0xc2b2, 0xcdc3, 0xfeb2, 0xa3e2, 0xf8d5, 0xcac2, 0xc5c0, 0xf0e7, 
	0xf2c7, 0xedc0, 0xd6cf, 0xa1a1, 0xadf0, 0xbfc6, 0x7fb2, 0x80b2, 
	0x81b2, 0x82b2, 0x83b2, 0x84b2, 0x85b2, 0x86b2, 0x87b2, 0x88b2, 
	0x89b2, 0x8ab2, 0x8bb2, 0x8cb2, 0x8db2, 0x8eb2, 0x8fb2, 0x90b2, 
	0x91b2, 0x92b2, 0x93b2, 0x94b2, 0x95b2, 0x96b2, 0x97b2, 0x98b2, 
	0x99b2, 0x9ab2, 0x9bb2, 0x9cb2, 0x9db2, 0x9eb2, 0x9fb2, 0xa0b2, 
	0xc9b4, 0xf0cc, 0xfab2, 0xd4c2, 0xe8c6, 0xcfb1, 0xecd2, 0xe8ca, 
	0xccd6, 0xdbba, 0xc3b4, 0xacc8, 0xeaf0, 0xa8f0, 0xf8bf, 0xd0ba, 
	0xa2ca, 0xecbe, 0xdad6, 0xdbd1, 0xf4bf, 0xf8ed, 0xf7cc, 0xf2c1, 
	0xecd6, 0xcaed, 0xe9cf, 0xb1c6, 0xc0bc, 0xc6d2, 0xcfd6, 0xbbf1, 
	0xd2f3, 0xbfb1, 0xd1b5, 0xdab5, 0xfbb7, 0xcff3, 0xd7f3, 0xd0f3, 
	0xa3c1, 0xd6b4, 0xc9c6, 0xedb0, 0xd2cf, 0xb3cd, 0xfad4, 0xdcc9, 
	0xa8e7, 0xa9e7, 0xb8cf, 0xf0c9, 0xe9d7, 0xdbc0, 0xd5d6, 0xa5e7, 
	0xa6e7, 0xa7b2, 0xdfd0, 0xe7c1, 0xeed2, 0xe1f4, 0xb0cf, 0xeaf1, 
	0xc4c1, 0xf6f1, 0xacb8, 0xb1b2, 0xbfb4, 0xd1cd, 0xded0, 0xa1a1, 
	0xa1a1, 0xa9f4, 0xe6b6, 0xcfcf, 0xb0b2, 0xacb4, 0xafc9, 0xb8dd, 
	0xb7dd, 0xa9dd, 0xd4bc, 0xa5be, 0xa7c3, 0xaac4, 0xecdc, 0xafd7, 
	0xaedd, 0xf2c0, 0xacdd, 0xc9ba, 0xb6dd, 0xb1dd, 0xcec6, 0xc8dc, 
	0xa6b4, 0xebb1, 0xdfc9, 0xfbd6, 0xc0f2, 0xc1f2, 0xc2f2, 0xf9c7, 
	0xb0b5, 0xc6f2, 0xc7f2, 0xc8f2, 0xf5ca, 0xf2d9, 0xc2f4, 0xbbb1, 
	0xbbcc, 0xe4d0, 0xdbc5, 0xfcb4, 0xd9c3, 0xe6b9, 0xc3b7, 0xc8d1, 
	0xf7be, 0xabda, 0xedd0, 0xe8c9, 0xcfcb, 0xefb6, 0xc0d0, 0xf9f4, 
	0xe0eb, 0xb7b7, 0xf0d4, 0xe1b9, 0xf5bb, 0xb0cc, 0xb6c6, 0xf6f4, 
	0xe2c9, 0xbad6, 0xc3f5, 0xeee9, 0xedc8, 0xe2d5, 0xd0e5, 0xa8cd, 
	0xbab6, 0xacc1, 0xd9cb, 0xc5ca, 0xf0d6, 0xc9e5, 0xd1b3, 0xecd4, 
	0xb8cd, 0xeab7, 0xd1e5, 0xe4b9, 0xbecd, 0x7fb3, 0x80b3, 0x81b3, 
	0x82b3, 0x83b3, 0x84b3, 0x85b3, 0x86b3, 0x87b3, 0x88b3, 0x89b3, 
	0x8ab3, 0x8bb3, 0x8cb3, 0x8db3, 0x8eb3, 0x8fb3, 0x90b3, 0x91b3, 
	0x92b3, 0x93b3, 0x94b3, 0x95b3, 0x96b3, 0x97b3, 0x98b3, 0x99b3, 
	0x9ab3, 0x9bb3, 0x9cb3, 0x9db3, 0x9eb3, 0x9fb3, 0xa0b3, 0xbfb2, 
	0xf9b9, 0xbcb6, 0xefd0, 0xb0d2, 0xceee, 0xdbbf, 0xf6b5, 0xcbee, 
	0xa1a1, 0xb0b7, 0xd5b1, 0xe3c5, 0xeac1, 0xc2b3, 0xbdc2, 0xf5d2, 
	0xf0da, 0xd5cc, 0xddcf, 0xeeda, 0xb8c8, 0xa9d1, 0xa7f6, 0xc2d5, 
	0xb9be, 0xa5b6, 0xeac7, 0xe3d3, 0xf1c4, 0xb1c2, 0xb9c2, 0xf3c2, 
	0xe9c2, 0xd2bc, 0xf8b0, 0xb5b8, 0xb8b1, 0xdcbd, 0xfebf, 0xf7d8, 
	0xa1c9, 0xa7d0, 0xeed7, 0xadbf, 0xeeb8, 0xdcd8, 0xb4b4, 0xa3ca, 
	0xcdc0, 0xa4ca, 0xabd1, 0xa9b2, 0xcad8, 0xb4e0, 0xa6bf, 0xfad0, 
	0xe4cc, 0xb0ba, 0xc8ba, 0xadb4, 0xb9ce, 0xb2cf, 0xa5c9, 0xb8e0, 
	0xaec0, 0xa9e0, 0xabe0, 0xfbd4, 0xa5b5, 0xb0e0, 0xd9cd, 0xb4d3, 
	0xbdbb, 0xf7d3, 0xc7c7, 0xace0, 0xb1e0, 0xedba, 0xd4b3, 0xb9e0, 
	0xa7ce, 0xa2d2, 0xb0bf, 0xa1b3, 0xccb5, 0xdfd1, 0xa8b1, 0xa4b1, 
	0xf6db, 0xa9dc, 0xbcd2, 0xf8ba, 0xecb5, 0xc3e6, 0xc4c3, 0xf6d0, 
	0xbdc3, 0xc2e6, 0xb4e6, 0xdce6, 0xeee5, 0xaeba, 0xbbb8, 0xa2d4, 
	0xc2c3, 0xf0d7, 0xb0d1, 0xcdbe, 0xb6c7, 0xb0e1, 0xcbe1, 0xfaef, 
	0xe3d9, 0xf9b7, 0xb1c3, 0xa1d6, 0xf8e0, 0xb8bc, 0xc8c0, 0xdeb2, 
	0xe1cf, 0xc7be, 0xf6e5, 0xedc5, 0xb4b8, 0xadd1, 0xe5e1, 0xf3bb, 
	0xf1b6, 0xafb1, 0xc6c3, 0xddbb, 0xabe3, 0xb6e3, 0xcad0, 0xb5e3, 
	0xe8b6, 0xfce2, 0xb7e3, 0xaebf, 0xd5c4, 0xb9e3, 0xccbb, 0xe4d3, 
	0xb8e3, 0xa1a1, 0xaaea, 0xe9ec, 0xb8b3, 0xc6d5, 0xe8c3, 0xf0bc, 
	0xabbf, 0xe0c8, 0xf1de, 0xe1d7, 0x7fb4, 0x80b4, 0x81b4, 0x82b4, 
	0x83b4, 0x84b4, 0x85b4, 0x86b4, 0x87b4, 0x88b4, 0x89b4, 0x8ab4, 
	0x8bb4, 0x8cb4, 0x8db4, 0x8eb4, 0x8fb4, 0x90b4, 0x91b4, 0x92b4, 
	0x93b4, 0x94b4, 0x95b4, 0x96b4, 0x97b4, 0x98b4, 0x99b4, 0x9ab4, 
	0x9bb4, 0x9cb4, 0x9db4, 0x9eb4, 0x9fb4, 0xa0b4, 0xe5b2, 0xa7b4, 
	0xe1cc, 0xd5ce, 0xbed2, 0xd2bd, 0xd3bb, 0xb7b4, 0xaed4, 0xbebe, 
	0xbbbb, 0xf0de, 0xefd1, 0xb3b1, 0xa8b3, 0xd8b6, 0xd2b8, 0xa2c9, 
	0xdfb0, 0xb3ec, 0xb9cb, 0xd5c6, 0xface, 0xe7c7, 0xa7be, 0xb0be, 
	0xeeca, 0xc7d6, 0xc0c1, 0xd0ea, 0xf8d4, 0xe6cc, 0xdac6, 0xafb3, 
	0xd7b9, 0xd8d7, 0xc4cc, 0xacbc, 0xe6d4, 0xced2, 0xb0b6, 0xc3bf, 
	0xadc9, 0xbbd5, 0xfee8, 0xf4b0, 0xdcc6, 0xa6e9, 0xe5c6, 0xf7b9, 
	0xb2d6, 0xb7bd, 0xb5d7, 0xdec3, 0xefc5, 0xfae8, 0xa1a1, 0xeebf, 
	0xdbc6, 0xd5c7, 0xd0b2, 0xb3d6, 0xc7bf, 0xbacc, 0xaab5, 0xc8c2, 
	0xb2eb, 0xdbb8, 0xced3, 0xd5e4, 0xc9b6, 0xd6e4, 0xbfd3, 0xd5b4, 
	0xfec7, 0xd7e4, 0xfcd4, 0xf5bc, 0xbfd5, 0xe6cf, 0xb3b2, 0xfeba, 
	0xcee4, 0xbcce, 0xd0ce, 0xc0cc, 0xcabf, 0xc4cd, 0xecc3, 0xe2b2, 
	0xc8c5, 0xe5d3, 0xebbb, 0xccd7, 0xc8b8, 0xc1bb, 0xcfe4, 0xc9e3, 
	0xd8e4, 0xa1a1, 0xa1a1, 0xd2e4, 0xbab1, 0xd9b7, 0xb9bd, 0xe6d1, 
	0xdece, 0xbbc8, 0xf3d6, 0xa1a1, 0xc6c5, 0xf7ea, 0xaccf, 0xccd3, 
	0xabe2, 0xefba, 0xc9d0, 0xa9b7, 0xf7e7, 0xd5c1, 0xc1d7, 0xfae7, 
	0xfdc5, 0xc3c5, 0xd9c7, 0xdcb9, 0xa1e8, 0xf9e7, 0xfbe7, 0xfbc9, 
	0xd5cb, 0xadbb, 0xacb7, 0xa1c1, 0xb4cd, 0xebf0, 0xb7be, 0xbbb6, 
	0xa6c6, 0xa1a1, 0xc7b5, 0xa2b7, 0xeecd, 0xa9f0, 0xe5f1, 0xc1b5, 
	0xa7c0, 0xccb6, 0xf5cf, 0xb2d3, 0xe2d1, 0xd4c9, 0xd1b8, 0xccb3, 
	0xb0cb, 0xa1cf, 0xbdbe, 0x7fb5, 0x80b5, 0x81b5, 0x82b5, 0x83b5, 
	0x84b5, 0x85b5, 0x86b5, 0x87b5, 0x88b5, 0x89b5, 0x8ab5, 0x8bb5, 
	0x8cb5, 0x8db5, 0x8eb5, 0x8fb5, 0x90b5, 0x91b5, 0x92b5, 0x93b5, 
	0x94b5, 0x95b5, 0x96b5, 0x97b5, 0x98b5, 0x99b5, 0x9ab5, 0x9bb5, 
	0x9cb5, 0x9db5, 0x9eb5, 0x9fb5, 0xa0b5, 0xb0b4, 0xd1bd, 0xafcd, 
	0xa2bf, 0xc8b5, 0xdfb2, 0xcab1, 0xf0bf, 0xb2cd, 0xf0b4, 0xf1cb, 
	0xeebd, 0xa4b7, 0xfed6, 0xdacb, 0xe0d6, 0xcabd, 0xe1bd, 0xdec8, 
	0xf8be, 0xcfd7, 0xf5d0, 0xbfcb, 0xe7c2, 0xf8b8, 0xa4d1, 0xa1a1, 
	0xade7, 0xc6c9, 0xe8cf, 0xe2f4, 0xf3f1, 0xf8f1, 0xe0cb, 0xf3cd, 
	0xbbc7, 0xb8d2, 0xadb8, 0xf6c9, 0xcdd5, 0xf3cc, 0xa2c6, 0xe7eb, 
	0xe8eb, 0xe9eb, 0xe6ca, 0xb4cb, 0xd0c6, 0xcddd, 0xcedd, 0xbcc6, 
	0xa4b2, 0xd1dd, 0xc2dd, 0xbcdd, 0xaabb, 0xe2c1, 0xd6e2, 0xf8d6, 
	0xb3c0, 0xd4dd, 0xc8c3, 0xfabe, 0xc4dd, 0xc6b7, 0xd5be, 0xc7dd, 
	0xaece, 0xd1cc, 0xcbb2, 0xc9dc, 0xcadd, 0xcbdd, 0xe9d0, 0xd4f2, 
	0xdccd, 0xcef2, 0xd7bb, 0xebd6, 0xf2b8, 0xd0f2, 0xd2f2, 0xd6bd, 
	0xc3b2, 0xd1c1, 0xa4b8, 0xfbf1, 0xd3ca, 0xa2d7, 0xbdd3, 0xc0c6, 
	0xcab4, 0xa4d6, 0xacda, 0xafda, 0xe7d7, 0xa9d5, 0xaeda, 0xdfcb, 
	0xefd5, 0xadda, 0xa1a1, 0xf3cf, 0xf5f5, 0xfcd6, 0xf9cc, 0xa1b7, 
	0xddea, 0xdaea, 0xd1b7, 0xd8ba, 0xf3b9, 0xf2c2, 0xe1b1, 0xb3c3, 
	0xfbb4, 0xbdd4, 0xacb3, 0xc3b3, 0xc9f5, 0xe0be, 0xcfb0, 0xc7f5, 
	0xdcc5, 0xf8b5, 0xcbf5, 0xccf5, 0xf0e9, 0xe1d6, 0xf3e9, 0xbcb9, 
	0xfeb4, 0xd3e5, 0xdcd6, 0xddd2, 0xf8bd, 0xd4e5, 0xf5b6, 0xcad3, 
	0xe7cf, 0xb1db, 0xa8ba, 0xd6cb, 0xbfc1, 0xaeb3, 0xa5c5, 0xc6b8, 
	0xc6c4, 0xfbbe, 0xdbb6, 0xd4ee, 0xa1a1, 0xd3ee, 0xc9e3, 0xf2c8, 
	0xaabf, 0xd0cf, 0x7fb6, 0x80b6, 0x81b6, 0x82b6, 0x83b6, 0x84b6, 
	0x85b6, 0x86b6, 0x87b6, 0x88b6, 0x89b6, 0x8ab6, 0x8bb6, 0x8cb6, 
	0x8db6, 0x8eb6, 0x8fb6, 0x90b6, 0x91b6, 0x92b6, 0x93b6, 0x94b6, 
	0x95b6, 0x96b6, 0x97b6, 0x98b6, 0x99b6, 0x9ab6, 0x9bb6, 0x9cb6, 
	0x9db6, 0x9eb6, 0x9fb6, 0xa0b6, 0xe4bc, 0xd0cf, 0xc8e3, 0xd3b6, 
	0xd7bd, 0xe5cb, 0xf4d1, 0xe7d3, 0xa1c2, 0xf2da, 0xefda, 0xccb5, 
	0xe3d1, 0xc5d1, 0xdbd0, 0xafbc, 0xcdb9, 0xa9f6, 0xc6d4, 0xcdc8, 
	0xeecf, 0xb3cb, 0xebd0, 0xb8e2, 0xbfe2, 0xb9b7, 0xbde2, 0xfbd2, 
	0xc1e2, 0xebb7, 0xa6d4, 0xc6bb, 0xf2ca, 0xdaba, 0xd2c2, 0xb6d3, 
	0xaed5, 0xc1b0, 0xabb4, 0xf6bd, 0xe3c7, 0xdfb4, 0xcbc9, 0xb5c9, 
	0xccd9, 0xa1a1, 0xcbbd, 0xf9b2, 0xe2d8, 0xbcc4, 0xcbbd, 0xdac7, 
	0xc6ca, 0xa8bc, 0xe3bb, 0xb5e0, 0xcbe0, 0xa4c9, 0xc2e0, 0xf0c2, 
	0xc8ca, 0xc4d8, 0xbee0, 0xc3cb, 0xcde0, 0xc5e0, 0xd8ce, 0xcbce, 
	0xe1d0, 0xbac7, 0xc6e0, 0xbce0, 0xb0d4, 0xb2d4, 0xfbc8, 0xdccb, 
	0xc1cc, 0xbfcd, 0xa3da, 0xfecb, 0xeecc, 0xfacb, 0xa1a1, 0xe9bf, 
	0xebce, 0xf5db, 0xe3dc, 0xc2b0, 0xdebc, 0xb5bc, 0xd3cf, 0xc5e6, 
	0xe8c2, 0xc1e6, 0xb1cf, 0xa9c9, 0xc7e6, 0xd4e1, 0xcfe1, 0xcfbb, 
	0xc9b8, 0xaec1, 0xc3cf, 0xb1df, 0xe3bb, 0xdde1, 0xa2ce, 0xded3, 
	0xe2d2, 0xc8b4, 0xd0b8, 0xebcf, 0xaeb0, 0xc7c8, 0xeeb3, 0xfad3, 
	0xf7c9, 0xc5bb, 0xf5c0, 0xb3e3, 0xe9e2, 0xebe2, 0xa2c0, 0xaaed, 
	0xa9ed, 0xfde2, 0xacea, 0xabea, 0xeab4, 0xa5d5, 0xe3b8, 0xc2cc, 
	0xeeb4, 0xebb2, 0xe1b0, 0xabb2, 0xd1cb, 0xa6c9, 0xf0cb, 0xc0c7, 
	0xa1d2, 0xb7b5, 0xb9b9, 0xb4be, 0xe5d5, 0xc2d0, 0xb5b0, 0xcdea, 
	0xbecf, 0xced4, 0xafc5, 0xd1ea, 0xa1a1, 0xa1a1, 0xe1bb, 0xc6c0, 
	0xb5d2, 0x7fb7, 0x80b7, 0x81b7, 0x82b7, 0x83b7, 0x84b7, 0x85b7, 
	0x86b7, 0x87b7, 0x88b7, 0x89b7, 0x8ab7, 0x8bb7, 0x8cb7, 0x8db7, 
	0x8eb7, 0x8fb7, 0x90b7, 0x91b7, 0x92b7, 0x93b7, 0x94b7, 0x95b7, 
	0x96b7, 0x97b7, 0x98b7, 0x99b7, 0x9ab7, 0x9bb7, 0x9cb7, 0x9db7, 
	0x9eb7, 0x9fb7, 0xa0b7, 0xfeb3, 0xacbf, 0xaae9, 0xa8d0, 0xabbc, 
	0xacd2, 0xc5b8, 0xeed1, 0xe5e8, 0xaee9, 0xe3c0, 0xe3b7, 0xbae9, 
	0xdcd3, 0xace9, 0xb9e9, 0xa1a1, 0xaad0, 0xeacb, 0xd9bb, 0xeeb5, 
	0xb9d8, 0xa6eb, 0xe7d2, 0xddcb, 0xd2d7, 0xdcc8, 0xe8e4, 0xb4d4, 
	0xb5b9, 0xe1b5, 0xf0c3, 0xdfe4, 0xdbe4, 0xaaca, 0xe7c4, 0xc2ce, 
	0xacbb, 0xbcd7, 0xefc1, 0xd7b2, 0xcfcc, 0xaacf, 0xe0e4, 0xe5e4, 
	0xe5bc, 0xccd1, 0xb3b7, 0xbac3, 0xb6c1, 0xd5d5, 0xcfec, 0xbeec, 
	0xe3ec, 0xcdbb, 0xc0bb, 0xb7c9, 0xa1a1, 0xd0ec, 0xafc5, 0xafd2, 
	0xbaeb, 0xe0e9, 0xa8ca, 0xb3d4, 0xabbb, 0xc5c0, 0xf7ba, 0xa6e8, 
	0xaac9, 0xf0c8, 0xa3e8, 0xf5e7, 0xa7e8, 0xf8e7, 0xa4e8, 0xb1b5, 
	0xfbbb, 0xf6f0, 0xb5cc, 0xe1b4, 0xa1a1, 0xf2f0, 0xd4b1, 0xf4f0, 
	0xd5b3, 0xe9c2, 0xb5d5, 0xcbc3, 0xa6be, 0xdebd, 0xc0c4, 0xf9ed, 
	0xbdb6, 0xc3b6, 0xbad8, 0xc7b2, 0xf6d5, 0xa2ee, 0xfeed, 0xa1ee, 
	0xabb0, 0xe9cb, 0xf6c5, 0xebcd, 0xe2b5, 0xb5c2, 0xefb5, 0xf0c5, 
	0xaeb1, 0xd4ed, 0xa1a1, 0xf7ec, 0xbbc2, 0xfbbd, 0xf2cd, 0xddc7, 
	0xa1a1, 0xc9d6, 0xedb3, 0xfeef, 0xf7d9, 0xfdef, 0xdfbf, 0xbdf1, 
	0xeabf, 0xdabd, 0xdef3, 0xdff3, 0xc5e8, 0xbbc1, 0xacbe, 0xc1d4, 
	0xadbe, 0xeebe, 0xa6c0, 0xf3b0, 0xe7cb, 0xf5cc, 0xc3d6, 0xd6d5, 
	0xefd7, 0xf0ca, 0xe5d2, 0xdbcf, 0xbac8, 0xa5ca, 0xb8c6, 0xc1cb, 
	0xded2, 0xeceb, 0xfcd1, 0xa6b3, 0xc8d0, 0xf9c8, 0xc5bd, 0xd7d6, 
	0x7fb8, 0x80b8, 0x81b8, 0x82b8, 0x83b8, 0x84b8, 0x85b8, 0x86b8, 
	0x87b8, 0x88b8, 0x89b8, 0x8ab8, 0x8bb8, 0x8cb8, 0x8db8, 0x8eb8, 
	0x8fb8, 0x90b8, 0x91b8, 0x92b8, 0x93b8, 0x94b8, 0x95b8, 0x96b8, 
	0x97b8, 0x98b8, 0x99b8, 0x9ab8, 0x9bb8, 0x9cb8, 0x9db8, 0x9eb8, 
	0x9fb8, 0xa0b8, 0xb9b8, 0xd9cf, 0xd4c4, 0xcbbe, 0xa7cd, 0xd9b5, 
	0xe7bb, 0xe4c2, 0xe6dd, 0xfbbf, 0xadce, 0xf9ba, 0xb6d2, 0xe1d4, 
	0xf0b8, 0xe0dd, 0xabdd, 0xcfc6, 0xadb6, 0xe2dd, 0xe7dd, 0xe1dd, 
	0xddd3, 0xb2c2, 0xc5ba, 0xbcd3, 0xd1f2, 0xdaf2, 0xd8f2, 0xf1ca, 
	0xeab6, 0xc9cd, 0xe4b7, 0xd7f2, 0xb9f2, 0xdbf2, 0xc3d1, 0xc4f4, 
	0xe1d2, 0xb9c8, 0xb9b2, 0xc3f4, 0xb0d7, 0xefc0, 0xc1f4, 0xa3d4, 
	0xf6d9, 0xa1a1, 0xe2bd, 0xefb2, 0xc3b8, 0xeacf, 0xd4ca, 0xabca, 
	0xb5da, 0xe4bf, 0xb6da, 0xe8d2, 0xcfb3, 0xb0bb, 0xefd6, 0xeeb9, 
	0xafd1, 0xb9da, 0xb8da, 0xb2d5, 0xa1a1, 0xa4f6, 0xa1a1, 0xbfbb, 
	0xf6f5, 0xd1ba, 0xf4d4, 0xcad7, 0xd6bc, 0xdfbb, 0xdfea, 0xdec1, 
	0xb8c2, 0xe0ea, 0xa3bc, 0xfab8, 0xe7bf, 0xb7c2, 0xf8cc, 0xe5b6, 
	0xf2b9, 0xd3f5, 0xa1a1, 0xe3b6, 0xcfbd, 0xd8d4, 0xf8e9, 0xf9e9, 
	0xd9b1, 0xa9c5, 0xcbd4, 0xced3, 0xc0b5, 0xeccb, 0xefb4, 0xc6b1, 
	0xa5ce, 0xdae5, 0xf6d3, 0xf4b6, 0xfdb9, 0xe9b1, 0xd8e5, 0xe2d3, 
	0xddb6, 0xded7, 0xa1a1, 0xeab3, 0xd2c0, 0xa4f5, 0xd4d3, 0xdcee, 
	0xafc7, 0xe0ee, 0xdfee, 0xd8bc, 0xcbd3, 0xa6c7, 0xd9c5, 0xb3b9, 
	0xacb2, 0xe5c1, 0xe7ee, 0xe9ee, 0xd2ee, 0xebee, 0xe4ee, 0xadc3, 
	0xa2d5, 0xafb0, 0xf4b8, 0xc9d4, 0xbad3, 0xc1f6, 0xf4ef, 0xa1a1, 
	0xd7c0, 0xe7b5, 0xa2b1, 0xe3c1, 0xb8be, 0xa5d1, 0xd0b0, 0xa4d4, 
	0xe7cd, 0xd9b6, 0xefe7, 0xe4b0, 0xcccb, 0xc7cb, 0xc2e2, 0x7fb9, 
	0x80b9, 0x81b9, 0x82b9, 0x83b9, 0x84b9, 0x85b9, 0x86b9, 0x87b9, 
	0x88b9, 0x89b9, 0x8ab9, 0x8bb9, 0x8cb9, 0x8db9, 0x8eb9, 0x8fb9, 
	0x90b9, 0x91b9, 0x92b9, 0x93b9, 0x94b9, 0x95b9, 0x96b9, 0x97b9, 
	0x98b9, 0x99b9, 0x9ab9, 0x9bb9, 0x9cb9, 0x9db9, 0x9eb9, 0x9fb9, 
	0xa0b9, 0xa5b1, 0xceca, 0xdbb3, 0xd4cd, 0xb1d1, 0xd5f7, 0xaff0, 
	0xe4f7, 0xa6b6, 0xc4b9, 0xf3ca, 0xaec9, 0xd7d9, 0xc4bd, 0xd2d9, 
	0xd4d9, 0xc5c1, 0xcdc6, 0xf1cf, 0xc8c7, 0xcdb9, 0xa1a1, 0xa1a1, 
	0xa4be, 0xcab5, 0xaebb, 0xe3d8, 0xd1d8, 0xe1d1, 0xd5e0, 0xd6e0, 
	0xefc2, 0xa2b3, 0xd4cb, 0xbbc5, 0xbecc, 0xcebc, 0xb6e0, 0xc2b8, 
	0xbbe0, 0xf5df, 0xbde0, 0xd0e0, 0xa1a1, 0xd9df, 0xc5cd, 0xbccd, 
	0xbeb3, 0xd3db, 0xb3be, 0xb9c4, 0xe6b5, 0xb5c7, 0xfbca, 0xa1a1, 
	0xd9ca, 0xb7e2, 0xcec3, 0xb9e2, 0xe1b6, 0xc6de, 0xd5b5, 0xcfe6, 
	0xdbc4, 0xfde5, 0xcee6, 0xd0e6, 0xcce6, 0xf5b7, 0xafc4, 0xfec4, 
	0xd1b9, 0xc8c1, 0xb5ca, 0xafd5, 0xdec7, 0xbbe5, 0xecb2, 0xd4b6, 
	0xc5c2, 0xb8d5, 0xabe1, 0xa4e1, 0xd2b1, 0xbbc4, 0xfee0, 0xa3e1, 
	0xaac0, 0xcec1, 0xd7b1, 0xa1a1, 0xc3d5, 0xb9b3, 0xa1a1, 0xb8d4, 
	0xaccc, 0xb6bf, 0xfdc2, 0xdfb9, 0xfae2, 0xd1b2, 0xd2b2, 0xbce3, 
	0xd8bd, 0xb2c6, 0xaad5, 0xa4cb, 0xb7b3, 0xfec3, 0xa7c2, 0xa1df, 
	0xe2de, 0xddb4, 0xbae5, 0xfdde, 0xf4b2, 0xc3c7, 0xd3ce, 0xecc6, 
	0xbdec, 0xa9b3, 0xdff4, 0xd4ea, 0xf1b0, 0xa5d5, 0xc5e9, 0xc2e9, 
	0xd9c8, 0xdcb8, 0xb9b9, 0xbbe9, 0xb6c8, 0xbde9, 0xbee9, 0xf1c1, 
	0xb1bb, 0xb9c7, 0xbfe9, 0xb3e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xb8c7, 
	0xe8b8, 0xb5eb, 0xc4d5, 0xddd1, 0xf6b9, 0xecc0, 0xceb5, 0xf6e4, 
	0xfad1, 0xaec4, 0xd5d7, 0xa9c2, 0xafc6, 0xbaba, 0x7fba, 0x80ba, 
	0x81ba, 0x82ba, 0x83ba, 0x84ba, 0x85ba, 0x86ba, 0x87ba, 0x88ba, 
	0x89ba, 0x8aba, 0x8bba, 0x8cba, 0x8dba, 0x8eba, 0x8fba, 0x90ba, 
	0x91ba, 0x92ba, 0x93ba, 0x94ba, 0x95ba, 0x96ba, 0x97ba, 0x98ba, 
	0x99ba, 0x9aba, 0x9bba, 0x9cba, 0x9dba, 0x9eba, 0x9fba, 0xa0ba, 
	0xfac2, 0xcdd6, 0xe1c6, 0xfeca, 0xa5bd, 0xc7d5, 0xb0c1, 0xeee4, 
	0xfec2, 0xf0e4, 0xbab3, 0xf4e4, 0xa6bb, 0xe6d3, 0xf8c9, 0xd3b5, 
	0xb1c2, 0xdbc8, 0xf5ce, 0xbfc9, 0xdcd0, 0xa8cf, 0xabd3, 0xfbb6, 
	0xfbea, 0xfddc, 0xfcd3, 0xafe2, 0xfed1, 0xf6cb, 0xeac2, 0xe5b9, 
	0xa9e8, 0xe7d5, 0xc9d2, 0xb1c5, 0xf1d1, 0xe8b7, 0xfad3, 0xbebb, 
	0xa1be, 0xe0bc, 0xe9c3, 0xa5ee, 0xa3ee, 0xafcb, 0xc5b4, 0xfab5, 
	0xccb1, 0xbccc, 0xb6cb, 0xd9ed, 0xf5ec, 0xa3b8, 0xf6bb, 0xd6d6, 
	0xc6b3, 0xddcd, 0xd1ce, 0xdfbd, 0xcbb6, 0xdcb9, 0xfebb, 0xe3bc, 
	0xdbf3, 0xe3cb, 0xe9f3, 0xadb2, 0xddf3, 0xe7f3, 0xf6b8, 0xa1a1, 
	0xe2b4, 0xd5f4, 0xabbe, 0xc0d5, 0xbae7, 0xdbd7, 0xc2b4, 0xb1e7, 
	0xccc2, 0xf4bd, 0xbad7, 0xf8cd, 0xd9b8, 0xb2e7, 0xf1b3, 0xe0c3, 
	0xa1a1, 0xdac2, 0xacce, 0xf7d0, 0xbbe7, 0xb7e7, 0xa3b7, 0xe4b4, 
	0xe4f4, 0xd4b5, 0xc5ce, 0xdbbe, 0xd8d5, 0xafb8, 0xf2b0, 0xe0b8, 
	0xf5eb, 0xb2b2, 0xc8cd, 0xf6eb, 0xb0ea, 0xa8cc, 0xebd3, 0xf2cc, 
	0xe8ce, 0xbbf4, 0xd8c8, 0xefdd, 0xafcf, 0xeed0, 0xc9c3, 0xb0dd, 
	0xd1c6, 0xe2cb, 0xc7b8, 0xf4d5, 0xa5dd, 0xeddd, 0xa1a1, 0xd4b2, 
	0xf2cb, 0xeedd, 0xeaf2, 0xdbc3, 0xdff2, 0xecf2, 0xe1f2, 0xe6f2, 
	0xa9d6, 0xb4ca, 0xe9f2, 0xe8f2, 0xd1c9, 0xd3b9, 0xe1c5, 0xfcb9, 
	0xe3c2, 0xc6d6, 0xd4f1, 0xd2f1, 0xa1a1, 0xd0cb, 0xbed6, 0xefd3, 
	0xdcce, 0xcfc8, 0xebbd, 0xc4ca, 0xf3ce, 0x7fbb, 0x80bb, 0x81bb, 
	0x82bb, 0x83bb, 0x84bb, 0x85bb, 0x86bb, 0x87bb, 0x88bb, 0x89bb, 
	0x8abb, 0x8bbb, 0x8cbb, 0x8dbb, 0x8ebb, 0x8fbb, 0x90bb, 0x91bb, 
	0x92bb, 0x93bb, 0x94bb, 0x95bb, 0x96bb, 0x97bb, 0x98bb, 0x99bb, 
	0x9abb, 0x9bbb, 0x9cbb, 0x9dbb, 0x9ebb, 0x9fbb, 0xa0bb, 0xb5cb, 
	0xbeda, 0xe5bb, 0xd5d3, 0xbfda, 0xbdda, 0xa1a1, 0xc0ba, 0xa1a1, 
	0xb2c3, 0xf6b1, 0xe2ea, 0xdec9, 0xd5ba, 0xd4d5, 0xcfb8, 0xa1a1, 
	0xa8b8, 0xfce9, 0xe1c7, 0xeccd, 0xb1c0, 0xb6d4, 0xdce5, 0xb7d1, 
	0xb2c7, 0xa3d2, 0xddb5, 0xdde5, 0xa1a1, 0xdee5, 0xbbac, 0xeaf7, 
	0xcce9, 0xebb3, 0xf5fe, 0xe6bb, 0xa4e7, 0xa7b7, 0xa1b5, 0xa1a1, 
	0xa1a1, 0xd6a1, 0xa1b6, 0xa1a1, 0xa1a1, 0xa1a1, 0xdba1, 0xdea4, 
	0xa1db, 0xa1a1, 0xdda1, 0xa1c0, 0xeda1, 0xa1cf, 0xa1a1, 0xa1a1, 
	0xcabc, 0xa1fb, 0xa1a1, 0xe8d0, 0xa1a1, 0xdda1, 0xeeda, 0xa1e2, 
	0xecc1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd6f7, 0xf3a1, 0xeabb, 0xa1a1, 0xa1a1, 0xf7a1, 
	0xb4c3, 0xc7b1, 0xebc6, 0xf6a1, 0xa1b6, 0xa1a1, 0xa1a1, 0xdbbc, 
	0xa1f9, 0xa1a1, 0xa1a1, 0xa1a1, 0xddc1, 0xe7be, 0xfcc5, 0xf5c1, 
	0xa3bd, 0xf4b9, 0xc4db, 0xf7c0, 0xebdf, 0xfbce, 0xdae0, 0xb0b3, 
	0xd9ba, 0xecd7, 0xa9bb, 0xead0, 0xadd2, 0xdbe0, 0xe7c5, 0xbbcb, 
	0xa5d0, 0xb4df, 0xafdc, 0xe6d0, 0xf6d4, 0xd8b7, 0xb9d7, 0xe9b6, 
	0xd5b6, 0xa1a1, 0xa1a1, 0xd2e6, 0xb5e6, 0xbfe6, 0xfce5, 0xbfbd, 
	0xace6, 0xbce5, 0xedbf, 0xf3c9, 0xb4d0, 0xe3b2, 0xc4c2, 0xd8e1, 
	0xa1a1, 0xb1b4, 0xc4d6, 0xa6e1, 0xcfb7, 0xf8b3, 0xedc3, 0xcbd8, 
	0xe3b9, 0xa7b3, 0xafb5, 0xb0d3, 0xc2b5, 0xe7e1, 0xecc7, 0xdbbb, 
	0xc7c2, 0xabed, 0xbdc4, 0xc7d3, 0x7fbc, 0x80bc, 0x81bc, 0x82bc, 
	0x83bc, 0x84bc, 0x85bc, 0x86bc, 0x87bc, 0x88bc, 0x89bc, 0x8abc, 
	0x8bbc, 0x8cbc, 0x8dbc, 0x8ebc, 0x8fbc, 0x90bc, 0x91bc, 0x92bc, 
	0x93bc, 0x94bc, 0x95bc, 0x96bc, 0x97bc, 0x98bc, 0x99bc, 0x9abc, 
	0x9bbc, 0x9cbc, 0x9dbc, 0x9ebc, 0x9fbc, 0xa0bc, 0xa1a1, 0xbfce, 
	0xcbcb, 0xfbd3, 0xbfe3, 0xafc1, 0xf5c3, 0xf7d4, 0xbde3, 0xacb5, 
	0xdfb7, 0xbee3, 0xe4e2, 0xbec2, 0xa6c4, 0xbfd6, 0xa1c4, 0xb2d7, 
	0xcbc6, 0xccc0, 0xc5b3, 0xabd7, 0xa6b2, 0xd3c4, 0xbacb, 0xc3c1, 
	0xf6c8, 0xe9b4, 0xa5b2, 0xa7b8, 0xa1a1, 0xcbc7, 0xa4df, 0xa7b5, 
	0xecde, 0xd0b5, 0xf3b7, 0xfdca, 0xbac4, 0xddd4, 0xa9b1, 0xc7ea, 
	0xf9d1, 0xc1d5, 0xa4e9, 0xaed7, 0xe0ca, 0xeab1, 0xdbb2, 0xa3c4, 
	0xa5c2, 0xaeb7, 0xb0bd, 0xd6c0, 0xc8e8, 0xcae9, 0xbac1, 0xb7c5, 
	0xbecc, 0xe4e9, 0xe3d2, 0xb9c5, 0xacbd, 0xfce4, 0xceb3, 0xc3c6, 
	0xcac1, 0xe0bd, 0xbdbd, 0xb6cc, 0xb1c7, 0xfae4, 0xb1b3, 0xecc5, 
	0xfde4, 0xa3c0, 0xf3c8, 0xa7bd, 0xcbc5, 0xf8eb, 0xb1e4, 0xa1a1, 
	0xa1a1, 0xecca, 0xbeb0, 0xc8c8, 0xd9ec, 0xbbeb, 0xf3ea, 0xb1bd, 
	0xb1e2, 0xa8d3, 0xb0e8, 0xa7c1, 0xaae8, 0xade8, 0xdce7, 0xa4f1, 
	0xf1b4, 0xc1ce, 0xf6c1, 0xddca, 0xafb4, 0xa3f1, 0xa8b0, 0xe5d6, 
	0xccc5, 0xb9cf, 0xd0c3, 0xa7ee, 0xa8ee, 0xa1a1, 0xe8b4, 0xf5b0, 
	0xb7c8, 0xdac0, 0xebc4, 0xc4bf, 0xebc2, 0xcdc5, 0xe5b8, 0xdabc, 
	0xa1a1, 0xfcbb, 0xa2f0, 0xbeb5, 0xa4d2, 0xeec7, 0xfdbc, 0xe4cf, 
	0xb6b7, 0xf0f3, 0xadd7, 0xaac6, 0xf2f3, 0xa1a1, 0xf3f3, 0xfdba, 
	0xdeb5, 0xb7c1, 0xb3ce, 0xc2d6, 0xeabc, 0xe5c3, 0xa9bc, 0xe0b1, 
	0xb5d4, 0xdfcf, 0xd0b6, 0xbabb, 0xb6e7, 0xbce7, 0xbfe7, 0xbee7, 
	0xeec2, 0xd5b0, 0xc9f4, 0x7fbd, 0x80bd, 0x81bd, 0x82bd, 0x83bd, 
	0x84bd, 0x85bd, 0x86bd, 0x87bd, 0x88bd, 0x89bd, 0x8abd, 0x8bbd, 
	0x8cbd, 0x8dbd, 0x8ebd, 0x8fbd, 0x90bd, 0x91bd, 0x92bd, 0x93bd, 
	0x94bd, 0x95bd, 0x96bd, 0x97bd, 0x98bd, 0x99bd, 0x9abd, 0x9bbd, 
	0x9cbd, 0x9dbd, 0x9ebd, 0x9fbd, 0xa0bd, 0xe6f4, 0xeef1, 0xc5cc, 
	0xa4c4, 0xa5cf, 0xbabd, 0xf4b7, 0xecb1, 0xe1d5, 0xceb1, 0xb5ce, 
	0xabc1, 0xdfca, 0xf1d2, 0xfbc2, 0xefc3, 0xafbd, 0xccb2, 0xa1a1, 
	0xeec5, 0xd0b4, 0xa3de, 0xa1a1, 0xebf2, 0xfbba, 0xfbb5, 0xf0f2, 
	0xbacf, 0xcfce, 0xadca, 0xf9f2, 0xc8bb, 0xf2f2, 0xf5f2, 0xc0ce, 
	0xe5b3, 0xd6ba, 0xb4b8, 0xfdb0, 0xd9f1, 0xa1a1, 0xdbf1, 0xead2, 
	0xc2c1, 0xb8cc, 0xbbd7, 0xaeb5, 0xebc7, 0xeed6, 0xcebf, 0xc3da, 
	0xc6da, 0xf7b5, 0xadcb, 0xdbc2, 0xbada, 0xc7da, 0xccb7, 0xc4da, 
	0xe3cd, 0xfaca, 0xedd6, 0xe2c5, 0xcdc9, 0xb3b8, 0xfabc, 0xcbd5, 
	0xc4b6, 0xcdcf, 0xf4c2, 0xcdb4, 0xcad6, 0xd9e2, 0xf7f4, 0xcbcc, 
	0xa4c8, 0xa1a1, 0xf9bc, 0xd7f5, 0xdfcc, 0xa4cc, 0xc8b2, 0xd8f5, 
	0xe9f2, 0xe1be, 0xc9cc, 0xd4bb, 0xbec1, 0xa1ea, 0xb2b1, 0xfde9, 
	0xd6c2, 0xa2ea, 0xfee9, 0xf5b9, 0xcaca, 0xdad5, 0xdbe5, 0xe2d4, 
	0xa8c7, 0xdac1, 0xa3d6, 0xcbb5, 0xb6db, 0xbcb4, 0xedd7, 0xd7b4, 
	0xa1a1, 0xbfd0, 0xe0cc, 0xfacf, 0xccc6, 0xedee, 0xfab3, 0xc1c2, 
	0xf1c8, 0xb1ef, 0xe6b7, 0xb5b1, 0xaeef, 0xb8ba, 0xcce3, 0xc4d4, 
	0xf6cf, 0xaaf6, 0xf0d5, 0xb9c3, 0xbfbf, 0xb0b0, 0xacd0, 0xaeb9, 
	0xa1f2, 0xa9b8, 0xa2f2, 0xceb9, 0xf8d1, 0xf6b6, 0xd9c4, 0xc5e2, 
	0xd5cd, 0xa4d7, 0xe1e6, 0xbbca, 0xe5e6, 0xddbc, 0xd4be, 0xe2e6, 
	0xbcf7, 0xa2b7, 0xd7f7, 0xd6c4, 0xc8f7, 0xc7c6, 0xcff6, 0xb3c2, 
	0xb2f0, 0xbbd1, 0x7fbe, 0x80be, 0x81be, 0x82be, 0x83be, 0x84be, 
	0x85be, 0x86be, 0x87be, 0x88be, 0x89be, 0x8abe, 0x8bbe, 0x8cbe, 
	0x8dbe, 0x8ebe, 0x8fbe, 0x90be, 0x91be, 0x92be, 0x93be, 0x94be, 
	0x95be, 0x96be, 0x97be, 0x98be, 0x99be, 0x9abe, 0x9bbe, 0x9cbe, 
	0x9dbe, 0x9ebe, 0x9fbe, 0xa0be, 0xa1a1, 0xeff4, 0xe2f7, 0xe8c0, 
	0xabc4, 0xddb3, 0xe5c8, 0xa1be, 0xb1d9, 0xcfd9, 0xadd9, 0xbdbc, 
	0xddc3, 0xfdc4, 0xc1bc, 0xe6d8, 0xabd1, 0xdfe0, 0xe6e0, 0xb1b5, 
	0xacd8, 0xe4e0, 0xd6b6, 0xebd4, 0xf7c6, 0xe6df, 0xe5e0, 0xc8e0, 
	0xc9ca, 0xdee0, 0xc1b8, 0xdab1, 0xd1bf, 0xb3cc, 0xd5db, 0xdcb7, 
	0xc1f4, 0xf8d9, 0xa7d1, 0xbee5, 0xbcb5, 0xbfc7, 0xdccf, 0xbec6, 
	0xaced, 0xb9b1, 0xc1e3, 0xe4d2, 0xb6ba, 0xc3b0, 0xb8d0, 0xbdd5, 
	0xc3c9, 0xb5d3, 0xb2b5, 0xa2cc, 0xb3ba, 0xddbe, 0xb0c2, 0xf1d4, 
	0xdec0, 0xd9b2, 0xf1bc, 0xdcc7, 0xa3b5, 0xcece, 0xfbd5, 0xfac0, 
	0xfecf, 0xdfe5, 0xcaea, 0xbcea, 0xa1a1, 0xd7e9, 0xd3c6, 0xebe8, 
	0xc8b3, 0xe1ba, 0xd9e9, 0xf7ca, 0xcfe9, 0xd6cd, 0xf0cf, 0xc5c7, 
	0xc1c7, 0xd4e9, 0xfabb, 0xe3e8, 0xa8ec, 0xfac0, 0xa9eb, 0xa5e5, 
	0xedb5, 0xe8d4, 0xa8c5, 0xf3d4, 0xc7d7, 0xa2e5, 0xc4b0, 0xa4bc, 
	0xa3e5, 0xa4e5, 0xa1a1, 0xc5e4, 0xa1a1, 0xe3b3, 0xc0ec, 0xd7c1, 
	0xd5c9, 0xc6b5, 0xe0d1, 0xe4ec, 0xc7c1, 0xcccc, 0xcbec, 0xbcc8, 
	0xe6d1, 0xc0b6, 0xabe8, 0xe1e7, 0xa1a1, 0xa1a1, 0xb1e8, 0xb0c6, 
	0xb1ea, 0xf9dd, 0xced5, 0xb3c8, 0xfcf0, 0xacc2, 0xc2ee, 0xaaee, 
	0xf7c2, 0xa9ee, 0xb3c6, 0xa5c4, 0xa9d7, 0xe0ed, 0xd3ed, 0xf9d3, 
	0xfdbb, 0xb1d3, 0xc2c4, 0xd5f6, 0xa1a1, 0xfabf, 0xddb8, 0xf2cb, 
	0xfed6, 0xc6f3, 0xa1a1, 0xdbb4, 0xb8c9, 0xf7f3, 0xe2b8, 0xc7cc, 
	0xcbe7, 0x7fbf, 0x80bf, 0x81bf, 0x82bf, 0x83bf, 0x84bf, 0x85bf, 
	0x86bf, 0x87bf, 0x88bf, 0x89bf, 0x8abf, 0x8bbf, 0x8cbf, 0x8dbf, 
	0x8ebf, 0x8fbf, 0x90bf, 0x91bf, 0x92bf, 0x93bf, 0x94bf, 0x95bf, 
	0x96bf, 0x97bf, 0x98bf, 0x99bf, 0x9abf, 0x9bbf, 0x9cbf, 0x9dbf, 
	0x9ebf, 0x9fbf, 0xa0bf, 0xcce7, 0xd3dd, 0xbfb8, 0xd8cf, 0xc9e7, 
	0xc7e7, 0xc6e7, 0xa7e7, 0xbeee, 0xcbf4, 0xb2ba, 0xbfb0, 0xe7f4, 
	0xf1f1, 0xc5c9, 0xe5c4, 0xf2c5, 0xe9d5, 0xcbd0, 0xd2cb, 0xd5b2, 
	0xefc8, 0xa5de, 0xa6de, 0xa7de, 0xb4b5, 0xacde, 0xb6bd, 0xf4cf, 
	0xdfce, 0xa9de, 0xa6f3, 0xf8c3, 0xecc2, 0xa9d3, 0xdac8, 0xe2ba, 
	0xcacd, 0xe3bf, 0xecc8, 0xddf1, 0xd7f1, 0xd7c7, 0xecea, 0xd0da, 
	0xe8d1, 0xc9da, 0xe4bb, 0xb1c4, 0xfdb5, 0xb3d0, 0xd1da, 0xb5c5, 
	0xcbda, 0xbdce, 0xedb7, 0xcdda, 0xcfda, 0xc8da, 0xceda, 0xa5d4, 
	0xa1a1, 0xa8c3, 0xb5c0, 0xe3cc, 0xe2f5, 0xbbd3, 0xe5f5, 0xdff5, 
	0xe0f5, 0xf8b7, 0xadbc, 0xe4ca, 0xa3ea, 0xe6b1, 0xecb0, 0xf1d7, 
	0xe0e5, 0xa1d1, 0xd9b3, 0xc9c1, 0xc5d2, 0xfeda, 0xd1d0, 0xa7b6, 
	0xedb1, 0xe2be, 0xccc3, 0xedb4, 0xaec7, 0xd6b8, 0xfdce, 0xbcc2, 
	0xa3ef, 0xb6d7, 0xf5bd, 0xa1a1, 0xbfef, 0xc0ef, 0xc5ef, 0xd6d1, 
	0xedcb, 0xe6cb, 0xd5cf, 0xf1b5, 0xaef6, 0xa1a1, 0xd8c1, 0xf4bb, 
	0xdec4, 0xadf6, 0xe5b5, 0xb2be, 0xefeb, 0xcac7, 0xd5bc, 0xb1be, 
	0xb5c6, 0xa5f2, 0xb7cd, 0xc7cd, 0xc3d2, 0xcdb2, 0xddb9, 0xa4bd, 
	0xc6e2, 0xdacf, 0xcae2, 0xa7ba, 0xe9e6, 0xe6c2, 0xa1ba, 0xc0f7, 
	0xd9f7, 0xdaf7, 0xa1a1, 0xabb1, 0xd2cd, 0xb3f0, 0xecd1, 0xbcd1, 
	0xa1a1, 0xa7d4, 0xacc4, 0xadc7, 0xfac1, 0xeab9, 0xc5d3, 0xa5b3, 
	0xdcc0, 0xa2b4, 0xf8c0, 0xbfba, 0xccdf, 0xa2b3, 0xe9e0, 0xc5cf, 
	0x7fc0, 0x80c0, 0x81c0, 0x82c0, 0x83c0, 0x84c0, 0x85c0, 0x86c0, 
	0x87c0, 0x88c0, 0x89c0, 0x8ac0, 0x8bc0, 0x8cc0, 0x8dc0, 0x8ec0, 
	0x8fc0, 0x90c0, 0x91c0, 0x92c0, 0x93c0, 0x94c0, 0x95c0, 0x96c0, 
	0x97c0, 0x98c0, 0x99c0, 0x9ac0, 0x9bc0, 0x9cc0, 0x9dc0, 0x9ec0, 
	0x9fc0, 0xa0c0, 0xe7cc, 0xbeba, 0xb9d1, 0xd6db, 0xa1a1, 0xa4d3, 
	0xc9e6, 0xd6e6, 0xe6c8, 0xcfde, 0xf0e5, 0xecd3, 0xebc1, 0xc0d4, 
	0xc9e1, 0xefb0, 0xd6c3, 0xd5bb, 0xa6d3, 0xaeb6, 0xd2bf, 0xb3c5, 
	0xaeed, 0xb7cf, 0xf7b4, 0xe6c7, 0xf7bb, 0xa2eb, 0xb7bc, 0xa1c5, 
	0xc1b2, 0xe2c4, 0xe9b8, 0xaadf, 0xa1a1, 0xb2c1, 0xd0b1, 0xefca, 
	0xd3ea, 0xb4cc, 0xb5b5, 0xadcf, 0xecbc, 0xede8, 0xcee8, 0xc9e9, 
	0xa1a1, 0xdee9, 0xdce9, 0xd1e9, 0xa1a1, 0xe7e9, 0xa1a1, 0xb1d5, 
	0xa2c5, 0xf5b1, 0xc3bc, 0xa9e5, 0xa1a1, 0xcecc, 0xc4c0, 0xaae5, 
	0xacc9, 0xa1a1, 0xa6e5, 0xa3bf, 0xaaca, 0xa7e5, 0xabce, 0xddec, 
	0xaad3, 0xc6db, 0xd3b2, 0xefd4, 0xf2d6, 0xd9bb, 0xe2bb, 0xdbec, 
	0xf4be, 0xbdc7, 0xfcc4, 0xf1bb, 0xb3e8, 0xb7bb, 0xa8e8, 0xb2e8, 
	0xecf0, 0xc6c1, 0xa9b0, 0xb4b5, 0xabcd, 0xc9b5, 0xabee, 0xb2cb, 
	0xc6c7, 0xcbc1, 0xc3bd, 0xd7c1, 0xc7bb, 0xe3ed, 0xb6ed, 0xb8bd, 
	0xfbec, 0xf8ec, 0xebcb, 0xfec1, 0xd8b4, 0xa8c2, 0xfaf3, 0xf1c5, 
	0xf9f3, 0xa1a1, 0xb7bf, 0xd3c3, 0xe0b7, 0xa1a1, 0xe3d4, 0xdab2, 
	0xd6f4, 0xf5cb, 0xa8bc, 0xd1e7, 0xc6c2, 0xd0e7, 0xc1b1, 0xecb7, 
	0xdcd7, 0xddd7, 0xd2e7, 0xb1b7, 0xcbcf, 0xcee7, 0xdff1, 0xcfe7, 
	0xa1a1, 0xa1a1, 0xc0f3, 0xe8f4, 0xedd2, 0xfaf1, 0xf9c9, 0xcfb4, 
	0xaac1, 0xcacb, 0xdcd2, 0xb7d3, 0xdfe2, 0xdbb1, 0xcecd, 0xa7c5, 
	0xa8b5, 0xb3c1, 0xdaeb, 0xd9c1, 0xd9be, 0xe8bc, 0xbdd0, 0x7fc1, 
	0x80c1, 0x81c1, 0x82c1, 0x83c1, 0x84c1, 0x85c1, 0x86c1, 0x87c1, 
	0x88c1, 0x89c1, 0x8ac1, 0x8bc1, 0x8cc1, 0x8dc1, 0x8ec1, 0x8fc1, 
	0x90c1, 0x91c1, 0x92c1, 0x93c1, 0x94c1, 0x95c1, 0x96c1, 0x97c1, 
	0x98c1, 0x99c1, 0x9ac1, 0x9bc1, 0x9cc1, 0x9dc1, 0x9ec1, 0x9fc1, 
	0xa0c1, 0xa1b1, 0xd9c0, 0xb5de, 0xa1a1, 0xbec7, 0xedca, 0xa6d1, 
	0xb1de, 0xb0de, 0xbbbc, 0xf7bf, 0xb0f3, 0xaff3, 0xabf3, 0xfef2, 
	0xa1f3, 0xa7f3, 0xf7f2, 0xddc2, 0xe5f2, 0xacf3, 0xf4d9, 0xdef1, 
	0xe5cf, 0xdaf1, 0xa1a1, 0xe9ea, 0xd5c3, 0xf9b0, 0xabc7, 0xb2bd, 
	0xd1bb, 0xa5d2, 0xbbd0, 0xdccc, 0xd7da, 0xedbb, 0xa1a1, 0xd9e1, 
	0xacd7, 0xfcc8, 0xbab9, 0xa3ca, 0xe7ea, 0xf7c7, 0xe3f5, 0xa3cc, 
	0xb8b5, 0xe8f5, 0xbdcf, 0xb7d5, 0xefe9, 0xafd4, 0xdfd3, 0xdcb1, 
	0xe1e5, 0xb9bb, 0xf5c2, 0xe2e5, 0xfbd1, 0xb8db, 0xb1f5, 0xcdd4, 
	0xf3b3, 0xc6b6, 0xbec3, 0xaac3, 0xfcbc, 0xb4c1, 0xc6ef, 0xf8b9, 
	0xb8b4, 0xf1ef, 0xc2c7, 0xcdb6, 0xccef, 0xa1a1, 0xc9ef, 0xabc0, 
	0xd7e3, 0xbbc0, 0xc7e3, 0xe5b0, 0xfed2, 0xa5c1, 0xe4cb, 0xaacb, 
	0xbccf, 0xcfbe, 0xabba, 0xc5bf, 0xabec, 0xb9ce, 0xd2b3, 0xa5bf, 
	0xcacf, 0xdef6, 0xdbf6, 0xd9f6, 0xe8ba, 0xebb8, 0xe7f7, 0xa4f0, 
	0xe3b5, 0xedf7, 0xeef7, 0xecf7, 0xfdf7, 0xabd5, 0xd4b4, 0xe0e0, 
	0xf2cf, 0xdbdb, 0xddc0, 0xf4c9, 0xcdd2, 0xafed, 0xc1b4, 0xa9c0, 
	0xc0d6, 0xc5c8, 0xecc4, 0xdab0, 0xd3cb, 0xa2df, 0xcfb6, 0xd7ea, 
	0xfceb, 0xc4e9, 0xcac3, 0xf1b9, 0xf7bc, 0xfbc4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa3ec, 0xe9b9, 0xebe9, 0xbad0, 0xc9e4, 0xcbc2, 0xc2e4, 
	0xa6bd, 0xd9c6, 0xafe4, 0xacd1, 0xfdbd, 0xe2ec, 0xa1a1, 0xeee1, 
	0xd4c1, 0xb5e8, 0xa1a1, 0xcdce, 0xb1f1, 0xddf0, 0x7fc2, 0x80c2, 
	0x81c2, 0x82c2, 0x83c2, 0x84c2, 0x85c2, 0x86c2, 0x87c2, 0x88c2, 
	0x89c2, 0x8ac2, 0x8bc2, 0x8cc2, 0x8dc2, 0x8ec2, 0x8fc2, 0x90c2, 
	0x91c2, 0x92c2, 0x93c2, 0x94c2, 0x95c2, 0x96c2, 0x97c2, 0x98c2, 
	0x99c2, 0x9ac2, 0x9bc2, 0x9cc2, 0x9dc2, 0x9ec2, 0x9fc2, 0xa0c2, 
	0xfad3, 0xadee, 0xc4f6, 0xb0d5, 0xfaed, 0xa1b4, 0xf1c0, 0xa3f0, 
	0xe0bb, 0xa1a1, 0xdcb4, 0xcfc7, 0xeff3, 0xc9bb, 0xa2f4, 0xecf3, 
	0xf1f3, 0xf2bc, 0xb8c1, 0xafd6, 0xc9c9, 0xc6c8, 0xd4e7, 0xe5d0, 
	0xd5e7, 0xa1a1, 0xb3cc, 0xccc7, 0xadb7, 0xb0d6, 0xf4c4, 0xeac6, 
	0xf7eb, 0xc9be, 0xd8b2, 0xf8c8, 0xb6c0, 0xeac3, 0xe5bd, 0xb9de, 
	0xf9dc, 0xb7de, 0xa1a1, 0xcdf2, 0xf5b2, 0xe6b3, 0xb4f3, 0xb2b8, 
	0xeeea, 0xfcf5, 0xd3da, 0xf7bd, 0xfdc3, 0xd8da, 0xe1b7, 0xb8d7, 
	0xbef5, 0xe7f5, 0xc4b1, 0xd9d7, 0xa3bc, 0xcff5, 0xfbc7, 0xaad7, 
	0xded5, 0xc7e5, 0xe4e5, 0xe3e5, 0xbdd2, 0xb4bd, 0xa1a1, 0xa1a1, 
	0xf7b0, 0xf8cb, 0xd9ce, 0xf8c4, 0xf2d5, 0xe4b8, 0xd7ef, 0xd3ef, 
	0xb8b4, 0xb9c7, 0xd8e3, 0xb3b4, 0xd9e3, 0xdae3, 0xebc0, 0xd3d4, 
	0xabcb, 0xfbb3, 0xa6bc, 0xa1a1, 0xb7f7, 0xa1a1, 0xdeb1, 0xa1a1, 
	0xeeb6, 0xd5d1, 0xe2cc, 0xa6f2, 0xa7f2, 0xa1a1, 0xf3c1, 0xc8e2, 
	0xa1c0, 0xd1f7, 0xa5f0, 0xefc6, 0xc1f7, 0xd7d7, 0xc9cb, 0xbace, 
	0xcbf7, 0xcdf7, 0xe8f6, 0xf0c0, 0xeaf6, 0xa1a1, 0xe7f6, 0xe9be, 
	0xecb6, 0xc0f0, 0xeff7, 0xacb6, 0xf8f7, 0xa1a1, 0xcad1, 0xb5bb, 
	0xa2c2, 0xdedb, 0xe8b3, 0xd3c5, 0xaec2, 0xcdb3, 0xb3bb, 0xc1c0, 
	0xc2e3, 0xcac5, 0xa3c2, 0xf5bf, 0xd8c6, 0xf7b3, 0xfce8, 0xb5e9, 
	0xd6e9, 0xade5, 0xece4, 0xfee4, 0xabe5, 0xa4c1, 0xf4b1, 0xf2e3, 
	0xacb1, 0xb8cb, 0xb9eb, 0xbfb6, 0xdeca, 0x7fc3, 0x80c3, 0x81c3, 
	0x82c3, 0x83c3, 0x84c3, 0x85c3, 0x86c3, 0x87c3, 0x88c3, 0x89c3, 
	0x8ac3, 0x8bc3, 0x8cc3, 0x8dc3, 0x8ec3, 0x8fc3, 0x90c3, 0x91c3, 
	0x92c3, 0x93c3, 0x94c3, 0x95c3, 0x96c3, 0x97c3, 0x98c3, 0x99c3, 
	0x9ac3, 0x9bc3, 0x9cc3, 0x9dc3, 0x9ec3, 0x9fc3, 0xa0c3, 0xa1cc, 
	0xf4e7, 0xedc7, 0xeab0, 0xebb3, 0xaebd, 0xf1b1, 0xd5b3, 0xa1a1, 
	0xadb0, 0xbbb5, 0xf1bb, 0xc8ce, 0xb1c1, 0xbeb2, 0xa4f4, 0xa9c7, 
	0xdce9, 0xa6f4, 0xb5cf, 0xebbc, 0xefd2, 0xfec9, 0xe6bb, 0xdec2, 
	0xc9bd, 0xfeeb, 0xfeb8, 0xfad9, 0xb0c0, 0xaab7, 0xd5d2, 0xb4de, 
	0xbac5, 0xd9cc, 0xa9d2, 0xedca, 0xcfd2, 0xacd3, 0xabd0, 0xb7d0, 
	0xb8f3, 0xc9f1, 0xf3bd, 0xc0b0, 0xc5f4, 0xa9bb, 0xd7c6, 0xb6ca, 
	0xa4d6, 0xb7cc, 0xdcda, 0xa5bc, 0xfbce, 0xdbda, 0xf9d4, 0xded4, 
	0xebf5, 0xd7b6, 0xf9b3, 0xeaf5, 0xc5b5, 0xcef5, 0xedf5, 0xa5ea, 
	0xcebd, 0xc7b4, 0xdfb1, 0xe5e5, 0xa2b7, 0xb4f5, 0xb5be, 0xe1ef, 
	0xf9b2, 0xdfef, 0xb4c1, 0xdbef, 0xdcef, 0xe9f7, 0xdaef, 0xddef, 
	0xcfef, 0xceef, 0xacef, 0xc9f6, 0xd8b9, 0xa4c2, 0xd1c4, 0xaff6, 
	0xedce, 0xd2c3, 0xbae8, 0xcfd4, 0xe0c0, 0xb8d4, 0xdfb5, 0xacec, 
	0xf8c2, 0xcbe2, 0xf0e6, 0xadc6, 0xfaba, 0xa8be, 0xf0f6, 0xebf6, 
	0xf4f6, 0xc8f0, 0xc4f0, 0xb5c8, 0xc6f0, 0xf4c5, 0xe8f7, 0xf6c0, 
	0xb4c2, 0xf0f4, 0xb0c8, 0xfcc1, 0xc2c8, 0xd3e0, 0xcfd1, 0xc0bd, 
	0xc0c8, 0xd7e6, 0xefc4, 0xf5c4, 0xa6b1, 0xa1a1, 0xfcd0, 0xe3e2, 
	0xc1c8, 0xb9c0, 0xf3b2, 0xd8ea, 0xcaeb, 0xb4e9, 0xbdc0, 0xd6c3, 
	0xf2e4, 0xafc2, 0xd7cf, 0xe7e7, 0xf7d1, 0xa2d6, 0xf3bf, 0xc2ed, 
	0xafb7, 0xf9c0, 0xbcf1, 0xbabe, 0xefb3, 0xbac0, 0xaebc, 0xb4c5, 
	0xc5cd, 0xe8b1, 0xcde7, 0xccbc, 0x7fc4, 0x80c4, 0x81c4, 0x82c4, 
	0x83c4, 0x84c4, 0x85c4, 0x86c4, 0x87c4, 0x88c4, 0x89c4, 0x8ac4, 
	0x8bc4, 0x8cc4, 0x8dc4, 0x8ec4, 0x8fc4, 0x90c4, 0x91c4, 0x92c4, 
	0x93c4, 0x94c4, 0x95c4, 0x96c4, 0x97c4, 0x98c4, 0x99c4, 0x9ac4, 
	0x9bc4, 0x9cc4, 0x9dc4, 0x9ec4, 0x9fc4, 0xa0c4, 0xebd7, 0xbff3, 
	0xabd2, 0xcdeb, 0xa2bd, 0xe5d4, 0xaab0, 0xa2c4, 0xfedd, 0xabc2, 
	0xbbc6, 0xd5cb, 0xccd4, 0xbaf2, 0xe4c8, 0xdcf1, 0xf5be, 0xa5b4, 
	0xe9d2, 0xa9c6, 0xafbe, 0xebd2, 0xebd4, 0xdeda, 0xaed3, 0xc4c9, 
	0xbbf5, 0xead4, 0xeef5, 0xa1a1, 0xb7f5, 0xcdca, 0xd3d6, 0xf3ee, 
	0xe2d0, 0xfbb2, 0xb1f6, 0xaec6, 0xc4c8, 0xa2bc, 0xb0dc, 0xb9e5, 
	0xdacc, 0xa7c9, 0xa1a1, 0xfac8, 0xfaf6, 0xefbc, 0xe6c3, 0xb3b5, 
	0xf9f7, 0xb4f6, 0xf6b3, 0xe4c1, 0xb3d9, 0xdec2, 0xbfe0, 0xf9df, 
	0xf9cf, 0xe7d9, 0xf4ca, 0xa1ce, 0xe5be, 0xe5c9, 0xe3c9, 0xafd0, 
	0xb5ec, 0xd9ea, 0xa3d3, 0xb8c0, 0xf9e8, 0xdfbc, 0xe0b9, 0xc3c0, 
	0xfece, 0xa1a1, 0xace8, 0xaef1, 0xa1a1, 0xd9cc, 0xf8b2, 0xf8d0, 
	0xf1e5, 0xc1de, 0xbcc0, 0xbade, 0xc3f2, 0xc0b4, 0xbbf3, 0xafc0, 
	0xe0cd, 0xdab0, 0xc0c0, 0xb4c7, 0xa4bb, 0xfed3, 0xdfd4, 0xecb3, 
	0xbed4, 0xd2f5, 0xe4ba, 0xe7b1, 0xb8f5, 0xadc1, 0xd8c0, 0xfacc, 
	0xf5ee, 0xecee, 0xedef, 0xd4ef, 0xd9b1, 0xd4b0, 0xf9c5, 0xb6c2, 
	0xeccf, 0xcbb9, 0xabf2, 0xcff7, 0xfdc7, 0xf4e6, 0xebdd, 0xe2c2, 
	0xc3f7, 0xa7c4, 0xcef7, 0xa2f7, 0xa4f7, 0xbadd, 0xd7ba, 0xcef0, 
	0xa1a1, 0xeaf7, 0xf6f7, 0xb1dc, 0xb7f6, 0xb8f6, 0xf6c4, 0xb2d9, 
	0xced9, 0xbddf, 0xd2c4, 0xdec2, 0xcfc2, 0xdbe1, 0xcdc2, 0xe4cd, 
	0xb2dc, 0xafcc, 0xa8c8, 0xb6bb, 0xf7c8, 0xb2cc, 0xa4e2, 0xbfc8, 
	0xfeb5, 0xabf1, 0xa2d1, 0x7fc5, 0x80c5, 0x81c5, 0x82c5, 0x83c5, 
	0x84c5, 0x85c5, 0x86c5, 0x87c5, 0x88c5, 0x89c5, 0x8ac5, 0x8bc5, 
	0x8cc5, 0x8dc5, 0x8ec5, 0x8fc5, 0x90c5, 0x91c5, 0x92c5, 0x93c5, 
	0x94c5, 0x95c5, 0x96c5, 0x97c5, 0x98c5, 0x99c5, 0x9ac5, 0x9bc5, 
	0x9cc5, 0x9dc5, 0x9ec5, 0x9fc5, 0xa0c5, 0xfcec, 0xfdc1, 0xa5f4, 
	0xfbc1, 0xfdcc, 0xe0d4, 0xaecf, 0xc4b3, 0xa1a1, 0xc1b6, 0xeaca, 
	0xcdd8, 0xdcf5, 0xd9f5, 0xcee0, 0xaadb, 0xfdd6, 0xf8bc, 0xf8bc, 
	0xabf6, 0xb2f6, 0xb2f7, 0xd6e7, 0xfcb2, 0xd2f7, 0xbebd, 0xe7e6, 
	0xe0d4, 0xebd0, 0xeeb1, 0xe3f6, 0xa7f7, 0xa9f7, 0xd1f0, 0xb8c5, 
	0xfaf7, 0xb9f6, 0xbaf6, 0xa8b9, 0xa1a1, 0xd2d1, 0xb5c1, 0xcec2, 
	0xf0be, 0xc1bd, 0xa1a1, 0xa1a1, 0xb6e8, 0xd4c7, 0xa9c7, 0xa1a1, 
	0xa1a1, 0xa7d3, 0xcbcf, 0xc5b2, 0xa1a1, 0xbad5, 0xdcc2, 0xc6b9, 
	0xe4b1, 0xcee5, 0xdfc2, 0xf0ef, 0xe5ee, 0xd9c5, 0xccd8, 0xd4cf, 
	0xd0f7, 0xaabe, 0xe4e6, 0xe9d1, 0xe8cb, 0xe5cc, 0xc7f7, 0xadf7, 
	0xdbc1, 0xacf7, 0xb8f0, 0xebf7, 0xb9c3, 0xf6d6, 0xd3b0, 0xbfc0, 
	0xb1e5, 0xb1cc, 0xb2f1, 0xa3b4, 0xdeb9, 0xbfee, 0xcfb2, 0xbcf3, 
	0xe9e1, 0xc3c8, 0xf7b2, 0xdfda, 0xded1, 0xd3b8, 0xf0c4, 0xafc2, 
	0xa8f6, 0xe9c1, 0xb0f6, 0xa7c7, 0xadf2, 0xe8d6, 0xdef7, 0xcaf7, 
	0xd7f6, 0xa5d3, 0xd8f0, 0xefbc, 0xced1, 0xa1f7, 0xbbf6, 0xa3c8, 
	0xfccc, 0xade9, 0xe5cd, 0xe9c0, 0xe1c2, 0xf9c2, 0xdbb9, 0xe6f5, 
	0xc6d0, 0xe2cf, 0xbfd4, 0xadc2, 0xf6b2, 0xc5f7, 0xe0f7, 0xe4d9, 
	0xd0c2, 0xf5d6, 0xded4, 0xf7c4, 0xb5f7, 0xbfc2, 0xf7e6, 0xc2c0, 
	0xd4da, 0xeff5, 0xa6f5, 0xead7, 0xc7f6, 0xe0c2, 0xf9f6, 0xd4f6, 
	0xf2f7, 0xded1, 0xe4d4, 0xd0f0, 0xe0ec, 0xeae6, 0xf4d3, 0xd9f0, 
	0xbdf0, 0xf5d3, 0x7fc6, 0x80c6, 0x81c6, 0x82c6, 0x83c6, 0x84c6, 
	0x85c6, 0x86c6, 0x87c6, 0x88c6, 0x89c6, 0x8ac6, 0x8bc6, 0x8cc6, 
	0x8dc6, 0x8ec6, 0x8fc6, 0x90c6, 0x91c6, 0x92c6, 0x93c6, 0x94c6, 
	0x95c6, 0x96c6, 0x97c6, 0x98c6, 0x99c6, 0x9ac6, 0x9bc6, 0x9cc6, 
	0x9dc6, 0x9ec6, 0x9fc6, 0xa0c6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa9a1, 
	0xa1a4, 0xa2a4, 0xa3a4, 0xa4a4, 0xa5a4, 0xa6a4, 0xa7a4, 0xa8a4, 
	0xa9a4, 0xaaa4, 0xaba4, 0xaca4, 0xada4, 0xaea4, 0xafa4, 0xb0a4, 
	0xb1a4, 0xb2a4, 0xb3a4, 0xb4a4, 0xb5a4, 0xb6a4, 0xb7a4, 0xb8a4, 
	0xb9a4, 0xbaa4, 0xbba4, 0xbca4, 0xbda4, 0xbea4, 0xbfa4, 0xc0a4, 
	0xc1a4, 0xc2a4, 0xc3a4, 0xc4a4, 0xc5a4, 0xc6a4, 0xc7a4, 0xc8a4, 
	0xc9a4, 0xcaa4, 0xcba4, 0xcca4, 0xcda4, 0xcea4, 0xcfa4, 0xd0a4, 
	0xd1a4, 0xd2a4, 0xd3a4, 0xd4a4, 0xd5a4, 0xd6a4, 0xd7a4, 0xd8a4, 
	0xd9a4, 0xdaa4, 0xdba4, 0xdca4, 0xdda4, 0xdea4, 0xdfa4, 0xe0a4, 
	0xe1a4, 0xe2a4, 0xe3a4, 0xe4a4, 0xe5a4, 0xe6a4, 0xe7a4, 0xe8a4, 
	0xe9a4, 0xeaa4, 0xeba4, 0xeca4, 0xeda4, 0xeea4, 0xefa4, 0xf0a4, 
	0xf1a4, 0xf2a4, 0xf3a4, 0xa1a5, 0xa2a5, 0xa3a5, 0xa4a5, 0xa5a5, 
	0xa6a5, 0xa7a5, 0xa8a5, 0xa9a5, 0xaaa5, 0xaba5, 0xaca5, 0xada5, 
	0xaea5, 0xafa5, 0xb0a5, 0xb1a5, 0xb2a5, 0xb3a5, 0xb4a5, 0xb5a5, 
	0xb6a5, 0xb7a5, 0xb8a5, 0xb9a5, 0xbaa5, 0xbba5, 0xbca5, 0xbda5, 
	0xbea5, 0xbfa5, 0xc0a5, 0xc1a5, 0xc2a5, 0xc3a5, 0xc4a5, 0xc5a5, 
	0xc6a5, 0xc7a5, 0xc8a5, 0xc9a5, 0xcaa5, 0xcba5, 0xcca5, 0xcda5, 
	0xcea5, 0xcfa5, 0xd0a5, 0xd1a5, 0xd2a5, 0xd3a5, 0xd4a5, 0xd5a5, 
	0xd6a5, 0xd7a5, 0xd8a5, 0xd9a5, 0xdaa5, 0xdba5, 0xdca5, 0xdda5, 
	0xdea5, 0xdfa5, 0xe0a5, 0xe1a5, 0xe2a5, 0xe3a5, 0xe4a5, 0xe5a5, 
	0xe6a5, 0x7fc7, 0x80c7, 0x81c7, 0x82c7, 0x83c7, 0x84c7, 0x85c7, 
	0x86c7, 0x87c7, 0x88c7, 0x89c7, 0x8ac7, 0x8bc7, 0x8cc7, 0x8dc7, 
	0x8ec7, 0x8fc7, 0x90c7, 0x91c7, 0x92c7, 0x93c7, 0x94c7, 0x95c7, 
	0x96c7, 0x97c7, 0x98c7, 0x99c7, 0x9ac7, 0x9bc7, 0x9cc7, 0x9dc7, 
	0x9ec7, 0x9fc7, 0xa0c7, 0xe7a5, 0xe8a5, 0xe9a5, 0xeaa5, 0xeba5, 
	0xeca5, 0xeda5, 0xeea5, 0xefa5, 0xf0a5, 0xf1a5, 0xf2a5, 0xf3a5, 
	0xf4a5, 0xf5a5, 0xf6a5, 0xa5a7, 0xa6a7, 0xa7a7, 0xa8a7, 0xa9a7, 
	0xaaa7, 0xaba7, 0xaca7, 0xada7, 0xaea7, 0xb5a7, 0xb6a7, 0xb7a7, 
	0xb8a7, 0xb9a7, 0xbaa7, 0xbba7, 0xbca7, 0xbda7, 0xbea7, 0xbfa7, 
	0xc0a7, 0xc1a7, 0xd1a7, 0xd2a7, 0xd3a7, 0xd4a7, 0xd5a7, 0xd6a7, 
	0xd7a7, 0xd8a7, 0xd9a7, 0xdaa7, 0xdba7, 0xdca7, 0xdda7, 0xdea7, 
	0xdfa7, 0xe0a7, 0xe1a7, 0xe2a7, 0xe3a7, 0xe4a7, 0xe5a7, 0xe6a7, 
	0xe7a7, 0xe8a7, 0xe9a7, 0xeaa7, 0xeba7, 0xeca7, 0xeda7, 0xeea7, 
	0xefa7, 0xf0a7, 0xf1a7, 0xd9a2, 0xdaa2, 0xdba2, 0xdca2, 0xdda2, 
	0xdea2, 0xdfa2, 0xe0a2, 0xe1a2, 0xe2a2, 0xc5a2, 0xc6a2, 0xc7a2, 
	0xc8a2, 0xc9a2, 0xcaa2, 0xcba2, 0xcca2, 0xcda2, 0xcea2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0x7fc8, 0x80c8, 0x81c8, 0x82c8, 0x83c8, 0x84c8, 0x85c8, 0x86c8, 
	0x87c8, 0x88c8, 0x89c8, 0x8ac8, 0x8bc8, 0x8cc8, 0x8dc8, 0x8ec8, 
	0x8fc8, 0x90c8, 0x91c8, 0x92c8, 0x93c8, 0x94c8, 0x95c8, 0x96c8, 
	0x97c8, 0x98c8, 0x99c8, 0x9ac8, 0x9bc8, 0x9cc8, 0x9dc8, 0x9ec8, 
	0x9fc8, 0xa0c8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xbfd8, 0xc9db, 0xced8, 0xa7b3, 0xf2cd, 0xa2d8, 0xb1d8, 
	0xa1d8, 0xede0, 0xa1a1, 0xf8e5, 0xdce1, 0xa1a1, 0xa1a1, 0xebd3, 
	0xa1a1, 0xc1d8, 0xecd8, 0xebd8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafec, 
	0xe3ce, 0xf8c6, 0xdde3, 0xa1a1, 0xa1a1, 0xedd8, 0xa1a1, 0xa1a1, 
	0xeed8, 0xdad9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5ca, 
	0xa1a1, 0xbbba, 0xfec4, 0xb3e5, 0xa1a1, 0xeae5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcfe2, 0xa1a1, 0xe1e2, 0xa1a1, 0xa1a1, 0xadeb, 0x7fc9, 
	0x80c9, 0x81c9, 0x82c9, 0x83c9, 0x84c9, 0x85c9, 0x86c9, 0x87c9, 
	0x88c9, 0x89c9, 0x8ac9, 0x8bc9, 0x8cc9, 0x8dc9, 0x8ec9, 0x8fc9, 
	0x90c9, 0x91c9, 0x92c9, 0x93c9, 0x94c9, 0x95c9, 0x96c9, 0x97c9, 
	0x98c9, 0x99c9, 0x9ac9, 0x9bc9, 0x9cc9, 0x9dc9, 0x9ec9, 0x9fc9, 
	0xa0c9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xece1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbfbc, 0xc5d3, 0xa1a1, 0xf5d8, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdbbc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3d8, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfcd9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xefe0, 0xb6d8, 
	0xdcdb, 0xd9db, 0xa1a1, 0xc5de, 0xa1a1, 0xb1e6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd8e6, 0xcdde, 0xa1a1, 0xa8e1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafd7, 0xecd2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe3e2, 0xa1a1, 0xa6df, 0xa1a1, 0xa1a1, 0xa4c7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9ea, 0xb8ea, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfabb, 0xa1a1, 0xa1a1, 0xa1a1, 0xaeeb, 
	0xe0d9, 0xa1a1, 0xe1e3, 0xa1a1, 0xe2e3, 0xe0e3, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xede1, 0xa1a1, 0xe0e7, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf8cd, 0xa1a1, 0xa1a1, 0xa1a1, 0xb4dc, 0xb5dc, 0xaef2, 0xa1a1, 
	0xf8da, 0xf5da, 0xa1a1, 0xf6da, 0xa1a1, 0xe3da, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe5cc, 0xf4d8, 0xa1a1, 
	0xfbd8, 0xa1a1, 0xa1d9, 0xa1a1, 0xdcd9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbfdb, 0xa1a1, 
	0xa1a1, 0xd5d8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbedf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7fca, 0x80ca, 
	0x81ca, 0x82ca, 0x83ca, 0x84ca, 0x85ca, 0x86ca, 0x87ca, 0x88ca, 
	0x89ca, 0x8aca, 0x8bca, 0x8cca, 0x8dca, 0x8eca, 0x8fca, 0x90ca, 
	0x91ca, 0x92ca, 0x93ca, 0x94ca, 0x95ca, 0x96ca, 0x97ca, 0x98ca, 
	0x99ca, 0x9aca, 0x9bca, 0x9cca, 0x9dca, 0x9eca, 0x9fca, 0xa0ca, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd6b6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd0db, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1e6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa9e1, 
	0xa1a1, 0xace1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd1e2, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafdf, 0xfdec, 0xfeec, 
	0xede2, 0xa1a1, 0xe5e2, 0xa1a1, 0xe7e2, 0xe8e2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc3d0, 0xb3bb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc5c8, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbaea, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbbe8, 0xbee8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafeb, 
	0xb0eb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3c6, 0xa1a1, 
	0xa1a1, 0xe9e3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf0e1, 0xa1a1, 0xf1e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xaeee, 0xa1a1, 0xdbf0, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc0eb, 0xd0dc, 
	0xb6dc, 0xa1a1, 0xbadc, 0xbbdc, 0xa1a1, 0x7fcb, 0x80cb, 0x81cb, 
	0x82cb, 0x83cb, 0x84cb, 0x85cb, 0x86cb, 0x87cb, 0x88cb, 0x89cb, 
	0x8acb, 0x8bcb, 0x8ccb, 0x8dcb, 0x8ecb, 0x8fcb, 0x90cb, 0x91cb, 
	0x92cb, 0x93cb, 0x94cb, 0x95cb, 0x96cb, 0x97cb, 0x98cb, 0x99cb, 
	0x9acb, 0x9bcb, 0x9ccb, 0x9dcb, 0x9ecb, 0x9fcb, 0xa0cb, 0xb7dc, 
	0xa1a1, 0xb9dc, 0xf4f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xfada, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xaed9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5d9, 0xa6d9, 
	0xa8d9, 0xb6d6, 0xa1a1, 0xa1a1, 0xb1b6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb0d9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfed9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xdad8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc6df, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcfdf, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcadf, 0xa1a1, 0xf2e0, 
	0xf7c5, 0xa1a1, 0xe8db, 0xe3db, 0xa1a1, 0xa1a1, 0xe9db, 0xa1a1, 
	0xa1a1, 0xe6db, 0xeadb, 0xa1a1, 0xa1a1, 0xe7db, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa7e6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdfe6, 0xdbe6, 0xb5e5, 0xb4e5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb2e1, 0xa1a1, 0xa1a1, 0xb5e1, 0xa1a1, 
	0xb8e1, 0xebc1, 0xb3e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfae0, 0xf9e0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdee1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf1e2, 0xefe2, 0xa1a1, 0xa1a1, 0x7fcc, 0x80cc, 0x81cc, 0x82cc, 
	0x83cc, 0x84cc, 0x85cc, 0x86cc, 0x87cc, 0x88cc, 0x89cc, 0x8acc, 
	0x8bcc, 0x8ccc, 0x8dcc, 0x8ecc, 0x8fcc, 0x90cc, 0x91cc, 0x92cc, 
	0x93cc, 0x94cc, 0x95cc, 0x96cc, 0x97cc, 0x98cc, 0x99cc, 0x9acc, 
	0x9bcc, 0x9ccc, 0x9dcc, 0x9ecc, 0x9fcc, 0xa0cc, 0xa1a1, 0xf7e2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4e2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafc1, 0xa7ea, 0xe6ec, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4de, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd3de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbeea, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3eb, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc4e8, 0xa1a1, 
	0xb9b9, 0xc7cf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xabbc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb3ed, 0xa1a1, 0xf9e3, 0xfae3, 0xa1a1, 
	0xa1a1, 0xefe3, 0xf0e3, 0xa1a1, 0xa1a1, 0xeee3, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa2c5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb2c8, 0xa1a1, 0xc1ea, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc0ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4e1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3e7, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xafee, 0xdee7, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xeced, 0xa1a1, 0xa1a1, 0xb7ed, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7fcd, 0x80cd, 0x81cd, 0x82cd, 0x83cd, 
	0x84cd, 0x85cd, 0x86cd, 0x87cd, 0x88cd, 0x89cd, 0x8acd, 0x8bcd, 
	0x8ccd, 0x8dcd, 0x8ecd, 0x8fcd, 0x90cd, 0x91cd, 0x92cd, 0x93cd, 
	0x94cd, 0x95cd, 0x96cd, 0x97cd, 0x98cd, 0x99cd, 0x9acd, 0x9bcd, 
	0x9ccd, 0x9dcd, 0x9ecd, 0x9fcd, 0xa0cd, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb6f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4f1, 0xa1a1, 
	0xb9b0, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7eb, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbedc, 0xa1a1, 0xc5dc, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7dc, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xccdc, 0xb8dc, 0xcddc, 0xcbdc, 0xa1a1, 0xd2dc, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb0f2, 0xa1a1, 0xb1f2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc2e5, 0xa1a1, 0xa1a1, 0xc3e5, 0xa1a1, 0xa1a1, 
	0xfbda, 0xaaba, 0xfcda, 0xa2db, 0xa1a1, 0xe7da, 0xe8da, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb4d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6d9, 
	0xa1a1, 0xb7d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd9d8, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7d8, 0xe5c0, 0xa1a1, 0xa1a1, 
	0xd2df, 0xa1a1, 0xe7df, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdfb6, 0xa1a1, 0xa1a1, 0xc3df, 0xa1a1, 0xa1a1, 
	0xdadf, 0xe8df, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xecdb, 0xeddb, 
	0xa1a1, 0xa1a1, 0xe2b6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb4b8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xade6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbee1, 0xa1a1, 0x7fce, 0x80ce, 0x81ce, 0x82ce, 0x83ce, 0x84ce, 
	0x85ce, 0x86ce, 0x87ce, 0x88ce, 0x89ce, 0x8ace, 0x8bce, 0x8cce, 
	0x8dce, 0x8ece, 0x8fce, 0x90ce, 0x91ce, 0x92ce, 0x93ce, 0x94ce, 
	0x95ce, 0x96ce, 0x97ce, 0x98ce, 0x99ce, 0x9ace, 0x9bce, 0x9cce, 
	0x9dce, 0x9ece, 0x9fce, 0xa0ce, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe1da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3e2, 0xa1a1, 0xa1a1, 
	0xe8e5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfee2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe7ec, 0xc3c4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe6bf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd9de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc6ea, 0xa1a1, 
	0xa1a1, 0xc7ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc3ea, 0xc4ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4eb, 0xdee8, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf1b9, 0xa1a1, 0xa1a1, 0xcfe8, 0xa1a1, 0xcfbc, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5e8, 0xa1a1, 0xd7e8, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcde8, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xdae8, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3e9, 
	0xe5e9, 0xa1a1, 0xd1b1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb1eb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xddcd, 0xa1a1, 0xf7c8, 0xa1a1, 0xa1a1, 
	0xb2e4, 0xa7e4, 0xa8e4, 0xa1a1, 0xaee4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0x7fcf, 0x80cf, 0x81cf, 0x82cf, 0x83cf, 0x84cf, 0x85cf, 
	0x86cf, 0x87cf, 0x88cf, 0x89cf, 0x8acf, 0x8bcf, 0x8ccf, 0x8dcf, 
	0x8ecf, 0x8fcf, 0x90cf, 0x91cf, 0x92cf, 0x93cf, 0x94cf, 0x95cf, 
	0x96cf, 0x97cf, 0x98cf, 0x99cf, 0x9acf, 0x9bcf, 0x9ccf, 0x9dcf, 
	0x9ecf, 0x9fcf, 0xa0cf, 0xe0bd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa6e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc4ec, 0xa1a1, 0xa1a1, 0xc6ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf0ea, 
	0xa1a1, 0xa1a1, 0xf5e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe6e7, 0xece7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcdce, 0xa1a1, 0xa1a1, 0xb2ee, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf1ed, 0xa1a1, 0xeded, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf2ef, 0xa1a1, 0xa1a1, 0xbced, 0xf8c5, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb9ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xecec, 0xa1a1, 0xf5ef, 0xd6d6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb8f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfde6, 0xa1a1, 0xb7ee, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe8f1, 0xc7de, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcfeb, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd1eb, 0xa4ca, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3eb, 0xa1a1, 
	0xd2eb, 0xa1a1, 0xadf4, 0xa1a1, 0xa1a1, 0xa1a1, 0xbbc6, 0xd8dc, 
	0xa1a1, 0xa1a1, 0xe6dc, 0xfbdc, 0xbbc9, 0xa1a1, 0xdadc, 0xa1a1, 
	0xd3dc, 0xa1a1, 0xa1a1, 0xdddc, 0xdedc, 0xa1a1, 0xa1a1, 0xa1a1, 
	0x7fd0, 0x80d0, 0x81d0, 0x82d0, 0x83d0, 0x84d0, 0x85d0, 0x86d0, 
	0x87d0, 0x88d0, 0x89d0, 0x8ad0, 0x8bd0, 0x8cd0, 0x8dd0, 0x8ed0, 
	0x8fd0, 0x90d0, 0x91d0, 0x92d0, 0x93d0, 0x94d0, 0x95d0, 0x96d0, 
	0x97d0, 0x98d0, 0x99d0, 0x9ad0, 0x9bd0, 0x9cd0, 0x9dd0, 0x9ed0, 
	0x9fd0, 0xa0d0, 0xd6dc, 0xe5dc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb4f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3f1, 
	0xa1a1, 0xa1a1, 0xead9, 0xa1a1, 0xa1a1, 0xa1a1, 0xc5e5, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4db, 0xa5db, 0xa8db, 0xa1a1, 
	0xa1a1, 0xc6ee, 0xc5ee, 0xebda, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbed9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc3d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb3b5, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe0c6, 0xa1a1, 0xa1a1, 0xa1a1, 0xdfd8, 
	0xa1a1, 0xa1a1, 0xded8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeadf, 0xeedf, 0xa1a1, 
	0xa1a1, 0xc1db, 0xc2df, 0xa1a1, 0xa1a1, 0xf2df, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4db, 0xf8db, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf9db, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xabd8, 0xc2bb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf2e5, 0xa1a1, 0xa1a1, 0xa2ed, 0xa3ed, 0xa4ed, 0x7fd1, 
	0x80d1, 0x81d1, 0x82d1, 0x83d1, 0x84d1, 0x85d1, 0x86d1, 0x87d1, 
	0x88d1, 0x89d1, 0x8ad1, 0x8bd1, 0x8cd1, 0x8dd1, 0x8ed1, 0x8fd1, 
	0x90d1, 0x91d1, 0x92d1, 0x93d1, 0x94d1, 0x95d1, 0x96d1, 0x97d1, 
	0x98d1, 0x99d1, 0x9ad1, 0x9bd1, 0x9cd1, 0x9dd1, 0x9ed1, 0x9fd1, 
	0xa0d1, 0xa5ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8e3, 0xa1a1, 0xa6e3, 
	0xa7e3, 0xa1a1, 0xaae3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb1cd, 0xa1a1, 
	0xdcde, 0xa1a1, 0xdade, 0xdbde, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb7ec, 
	0xb9ec, 0xb8ec, 0xa1a1, 0xa1a1, 0xc9ea, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf1e8, 0xe0e8, 0xe1e8, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdcc6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe4e8, 0xe6e8, 0xa1a1, 0xe9e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbde4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb4e4, 0xb3e4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb7e4, 0xa1a1, 0xbbe4, 0xa1a1, 
	0xa1a1, 0xbce4, 0xa1a1, 0xbfcd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7fd2, 0x80d2, 
	0x81d2, 0x82d2, 0x83d2, 0x84d2, 0x85d2, 0x86d2, 0x87d2, 0x88d2, 
	0x89d2, 0x8ad2, 0x8bd2, 0x8cd2, 0x8dd2, 0x8ed2, 0x8fd2, 0x90d2, 
	0x91d2, 0x92d2, 0x93d2, 0x94d2, 0x95d2, 0x96d2, 0x97d2, 0x98d2, 
	0x99d2, 0x9ad2, 0x9bd2, 0x9cd2, 0x9dd2, 0x9ed2, 0x9fd2, 0xa0d2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfee1, 0xf9e1, 0xa1a1, 0xa1a1, 0xfce1, 
	0xa1e2, 0xfbe1, 0xa1a1, 0xeee7, 0xede7, 0xa1a1, 0xa1a1, 0xf2e7, 
	0xa1a1, 0xf1e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xacf0, 0xa1a1, 0xb2ea, 0xa1a1, 0xa1a1, 
	0xb3ee, 0xa1a1, 0xe6f0, 0xa1a1, 0xa1a1, 0xe4f0, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4ed, 0xf2ed, 0xf3ed, 0xa1a1, 
	0xc8ed, 0xc7ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc9ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeeec, 0xa1a1, 
	0xefec, 0xf0ec, 0xa1a1, 0xa1a1, 0xf8ef, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf6ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9f1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7f3, 0xa1a1, 
	0xa1a1, 0xcbf3, 0xc5f3, 0xc9f3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xcef4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa3e7, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb8ee, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe9f1, 0xa1a1, 0xa1a1, 0xb7b0, 
	0xdceb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8f4, 0xb1f4, 
	0xa1a1, 0xfcdc, 0xfadc, 0xa1a1, 0xa1a1, 0xe8dc, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe7dc, 0xa1a1, 0x7fd3, 0x80d3, 0x81d3, 
	0x82d3, 0x83d3, 0x84d3, 0x85d3, 0x86d3, 0x87d3, 0x88d3, 0x89d3, 
	0x8ad3, 0x8bd3, 0x8cd3, 0x8dd3, 0x8ed3, 0x8fd3, 0x90d3, 0x91d3, 
	0x92d3, 0x93d3, 0x94d3, 0x95d3, 0x96d3, 0x97d3, 0x98d3, 0x99d3, 
	0x9ad3, 0x9bd3, 0x9cd3, 0x9dd3, 0x9ed3, 0x9fd3, 0xa0d3, 0xa1a1, 
	0xa1a1, 0xa2dd, 0xa1a1, 0xebdc, 0xeddc, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf2dc, 0xa1a1, 0xf4dc, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe7b2, 0xa1a1, 0xebbc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb6f2, 0xa1a1, 0xb7f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb8f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbbf2, 0xcfb2, 
	0xa1a1, 0xa1a1, 0xbaf2, 0xa1a1, 0xa1a1, 0xa1a1, 0xacf4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc4f1, 0xa1a1, 0xa1a1, 0xc6f1, 0xa1a1, 0xa1a1, 
	0xc0f4, 0xa1a1, 0xa1a1, 0xa1a1, 0xf8f4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcfb8, 0xa1a1, 0xc0f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcaca, 0xa1a1, 0xa1a1, 0xcce5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa3db, 0xa1a1, 0xa1a1, 
	0xaedb, 0xaddb, 0xacdb, 0xa7db, 0xfbf4, 0xfcf4, 0xfdf4, 0xc9ee, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xecda, 0xc0f6, 0xa1a1, 0xd4f7, 0xcbdb, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcad9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xd0b9, 0xc3db, 0xa1a1, 0xd0d8, 0xa1a1, 0xa3e0, 0xa4e0, 
	0xfedf, 0xa1a1, 0xfddf, 0xa1a1, 0xf4df, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa7e0, 0xa1a1, 0xa1a1, 0x7fd4, 0x80d4, 0x81d4, 0x82d4, 
	0x83d4, 0x84d4, 0x85d4, 0x86d4, 0x87d4, 0x88d4, 0x89d4, 0x8ad4, 
	0x8bd4, 0x8cd4, 0x8dd4, 0x8ed4, 0x8fd4, 0x90d4, 0x91d4, 0x92d4, 
	0x93d4, 0x94d4, 0x95d4, 0x96d4, 0x97d4, 0x98d4, 0x99d4, 0x9ad4, 
	0x9bd4, 0x9cd4, 0x9dd4, 0x9ed4, 0x9fd4, 0xa0d4, 0xa1a1, 0xa1a1, 
	0xb6c9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf5e0, 0xf0e0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfadb, 
	0xa5dc, 0xa4dc, 0xa3dc, 0xa1a1, 0xfcdb, 0xa1dc, 0xa1a1, 0xefdb, 
	0xc0dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xebdb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa2dc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbce6, 0xbae6, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xabe6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xede5, 0xc6e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb4e1, 0xa1a1, 0xa1a1, 
	0xc1e1, 0xc3e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc4e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5e2, 0xa1a1, 0xd8e2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xaee3, 0xa1a1, 0xade3, 0xa1a1, 0xa1a1, 0xaabe, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfdea, 0xa1a1, 0xe5de, 0xe0b5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xddde, 0xa1a1, 0xa1a1, 0xa1a1, 0xe1de, 0xa1a1, 0xdeb6, 
	0xfec6, 0xddbe, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3de, 0xe7de, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7fd5, 0x80d5, 0x81d5, 0x82d5, 0x83d5, 
	0x84d5, 0x85d5, 0x86d5, 0x87d5, 0x88d5, 0x89d5, 0x8ad5, 0x8bd5, 
	0x8cd5, 0x8dd5, 0x8ed5, 0x8fd5, 0x90d5, 0x91d5, 0x92d5, 0x93d5, 
	0x94d5, 0x95d5, 0x96d5, 0x97d5, 0x98d5, 0x99d5, 0x9ad5, 0x9bd5, 
	0x9cd5, 0x9dd5, 0x9ed5, 0x9fd5, 0xa0d5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xceea, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf5e8, 0xa1a1, 0xf4e8, 0xf6e8, 0xa1a1, 0xa1a1, 
	0xf8e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4ec, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe8e9, 0xa1a1, 0xa1a1, 0xb4eb, 0xedb5, 0xcae4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb5e4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc4e4, 0xa1a1, 0xd7c4, 0xa1a1, 0xcbe4, 0xa1a1, 0xc7e4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe9cd, 0xa1a1, 0xfecc, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc9ec, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xcaec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf5ea, 
	0xa1a1, 0xa1a1, 0xf6ea, 0xa7e2, 0xa2e2, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa5e2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa6e2, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf7d1, 0xa1a1, 0x7fd6, 0x80d6, 0x81d6, 0x82d6, 0x83d6, 0x84d6, 
	0x85d6, 0x86d6, 0x87d6, 0x88d6, 0x89d6, 0x8ad6, 0x8bd6, 0x8cd6, 
	0x8dd6, 0x8ed6, 0x8fd6, 0x90d6, 0x91d6, 0x92d6, 0x93d6, 0x94d6, 
	0x95d6, 0x96d6, 0x97d6, 0x98d6, 0x99d6, 0x9ad6, 0x9bd6, 0x9cd6, 
	0x9dd6, 0x9ed6, 0x9fd6, 0xa0d6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd0c3, 0xf5ed, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf6ed, 0xa1a1, 0xf7ed, 0xa1a1, 0xf8ce, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd1ed, 0xceed, 0xe8b9, 0xcfed, 0xa1a1, 
	0xf6ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xebc0, 0xa1a1, 
	0xd5bd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xccf3, 
	0xa1a1, 0xd3f3, 0xa1a1, 0xa1a1, 0xd4f3, 0xd5f3, 0xa1a1, 0xcef3, 
	0xa1a1, 0xd1f3, 0xcaf3, 0xa1a1, 0xa1a1, 0xa1a1, 0xcdf3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb3d5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa4e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xaae7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc6f4, 
	0xa1a1, 0xb4f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf5f1, 0xa1a1, 0xe4eb, 
	0xa1a1, 0xa1a1, 0xd6eb, 0xa1a1, 0xa1a1, 0xe3eb, 0xe2eb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb4f4, 0xb6f4, 0xa1a1, 
	0xb7f4, 0xa1a1, 0xf5e5, 0xa1a1, 0xa1a1, 0xb9dd, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb3dd, 0xb4dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xf0dc, 0xaddd, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8dd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0x7fd7, 0x80d7, 0x81d7, 0x82d7, 0x83d7, 0x84d7, 0x85d7, 
	0x86d7, 0x87d7, 0x88d7, 0x89d7, 0x8ad7, 0x8bd7, 0x8cd7, 0x8dd7, 
	0x8ed7, 0x8fd7, 0x90d7, 0x91d7, 0x92d7, 0x93d7, 0x94d7, 0x95d7, 
	0x96d7, 0x97d7, 0x98d7, 0x99d7, 0x9ad7, 0x9bd7, 0x9cd7, 0x9dd7, 
	0x9ed7, 0x9fd7, 0xa0d7, 0xa1a1, 0xa1a1, 0xa1a1, 0xc5f2, 0xc4f2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcaf2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc8f1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf3d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xfbf5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc2f5, 0xa1a1, 0xa1a1, 0xc1f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcde5, 0xcfe5, 0xa1a1, 
	0xa1a1, 0xd2e5, 0xb0db, 0xa1a1, 0xa1a1, 0xbbb3, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xafdb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3b7, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xccee, 0xcfee, 
	0xa1a1, 0xa1a1, 0xcaee, 0xa1a1, 0xa1a1, 0xa1a1, 0xc6e3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb8d8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf6b4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb3e0, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa5e0, 0xa1a1, 0xa1a1, 0xa1a1, 0xaee0, 
	0xf6df, 0xf7df, 0xafe0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0x7fd8, 0x80d8, 0x81d8, 0x82d8, 0x83d8, 0x84d8, 0x85d8, 0x86d8, 
	0x87d8, 0x88d8, 0x89d8, 0x8ad8, 0x8bd8, 0x8cd8, 0x8dd8, 0x8ed8, 
	0x8fd8, 0x90d8, 0x91d8, 0x92d8, 0x93d8, 0x94d8, 0x95d8, 0x96d8, 
	0x97d8, 0x98d8, 0x99d8, 0x9ad8, 0x9bd8, 0x9cd8, 0x9dd8, 0x9ed8, 
	0x9fd8, 0xa0d8, 0xa7dc, 0xa6dc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeebc, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa3e6, 0xa1a1, 
	0xa1a1, 0xc4e6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd1e1, 0xa1a1, 0xd2e1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcee1, 0xa1a1, 0xa1a1, 0xcce1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa2e1, 0xa1a1, 0xe9e5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa2e3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfeea, 0xefde, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xebde, 0xdede, 0xa1a1, 
	0xe9de, 0xa1a1, 0xa1a1, 0xa1a1, 0xf2de, 0xa1a1, 0xa1a1, 0xedde, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf3b1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbcec, 0x7fd9, 
	0x80d9, 0x81d9, 0x82d9, 0x83d9, 0x84d9, 0x85d9, 0x86d9, 0x87d9, 
	0x88d9, 0x89d9, 0x8ad9, 0x8bd9, 0x8cd9, 0x8dd9, 0x8ed9, 0x8fd9, 
	0x90d9, 0x91d9, 0x92d9, 0x93d9, 0x94d9, 0x95d9, 0x96d9, 0x97d9, 
	0x98d9, 0x99d9, 0x9ad9, 0x9bd9, 0x9cd9, 0x9dd9, 0x9ed9, 0x9fd9, 
	0xa0d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe2c0, 0xe2e8, 0xc7e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa7e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xfbe8, 0xa1a1, 0xa3e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa5ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5eb, 0xe8c7, 0xb5ed, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xede3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcde4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5e4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd0e4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd4e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xccec, 0xa1a1, 0xa1a1, 0xcdec, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf8ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa9e2, 
	0xaee2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xfde7, 0xfee7, 0xfce7, 0xa1a1, 0xa1a1, 0x7fda, 0x80da, 
	0x81da, 0x82da, 0x83da, 0x84da, 0x85da, 0x86da, 0x87da, 0x88da, 
	0x89da, 0x8ada, 0x8bda, 0x8cda, 0x8dda, 0x8eda, 0x8fda, 0x90da, 
	0x91da, 0x92da, 0x93da, 0x94da, 0x95da, 0x96da, 0x97da, 0x98da, 
	0x99da, 0x9ada, 0x9bda, 0x9cda, 0x9dda, 0x9eda, 0x9fda, 0xa0da, 
	0xa2e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb8e5, 0xa1a1, 0xb4ee, 0xf0f0, 0xa1a1, 
	0xa1a1, 0xedf0, 0xa1a1, 0xa1a1, 0xeef0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfbed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf3ef, 0xa1a1, 0xcced, 0xa1a1, 0xa1a1, 
	0xcbed, 0xa1a1, 0xd2ed, 0xb7c8, 0xa1a1, 0xa1a1, 0xbaed, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfcef, 0xa1a1, 0xfbef, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb5f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdcf3, 0xa1a1, 0xa1a1, 0xd8f3, 0xdaf3, 0xd2f4, 0xd1f4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xace7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdbcf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb0c0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xaaf4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcaba, 0xcfdd, 0xa1a1, 0xd2dd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbddd, 0xc3dd, 0xbedd, 0xbfdd, 0xa1a1, 
	0xd5dd, 0xa1a1, 0xa1a1, 0xc5dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xccdd, 
	0xa1a1, 0xc8dd, 0xc9dd, 0xa1a1, 0xa1a1, 0x7fdb, 0x80db, 0x81db, 
	0x82db, 0x83db, 0x84db, 0x85db, 0x86db, 0x87db, 0x88db, 0x89db, 
	0x8adb, 0x8bdb, 0x8cdb, 0x8ddb, 0x8edb, 0x8fdb, 0x90db, 0x91db, 
	0x92db, 0x93db, 0x94db, 0x95db, 0x96db, 0x97db, 0x98db, 0x99db, 
	0x9adb, 0x9bdb, 0x9cdb, 0x9ddb, 0x9edb, 0x9fdb, 0xa0db, 0xa1a1, 
	0xa1a1, 0xbdb9, 0xa1a1, 0xd0dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5f2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcbf2, 0xa1a1, 0xa1a1, 0xd6f2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xcbf1, 0xcaf1, 0xa1a1, 0xa1a1, 0xa1a1, 0xccf1, 0xa1a1, 0xe8ea, 
	0xa1a1, 0xa1a1, 0xfdf5, 0xa1a1, 0xaada, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb0da, 0xa1a1, 0xa1a1, 0xb1da, 0xbaee, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdcea, 0xa1a1, 
	0xdbea, 0xa1a1, 0xa1a1, 0xf2f4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc5f5, 0xa1a1, 0xcaf5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc6f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xf7e9, 0xf2e9, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4e9, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd5e5, 0xa1a1, 0xd6e5, 0xa9db, 0xa1a1, 0xb2db, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa2f5, 0xfef4, 0xa1a1, 0xa1f5, 0xa1a1, 0xd5ee, 
	0xa1a1, 0xd8ee, 0xb4ef, 0xa1a1, 0xd1ee, 0xa1a1, 0xa1a1, 0xd9ee, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7ee, 0xd6ee, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbfd4, 0xa1a1, 0xa1a1, 0xa1a1, 0xcae3, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf1da, 0x7fdc, 0x80dc, 0x81dc, 0x82dc, 
	0x83dc, 0x84dc, 0x85dc, 0x86dc, 0x87dc, 0x88dc, 0x89dc, 0x8adc, 
	0x8bdc, 0x8cdc, 0x8ddc, 0x8edc, 0x8fdc, 0x90dc, 0x91dc, 0x92dc, 
	0x93dc, 0x94dc, 0x95dc, 0x96dc, 0x97dc, 0x98dc, 0x99dc, 0x9adc, 
	0x9bdc, 0x9cdc, 0x9ddc, 0x9edc, 0x9fdc, 0xa0dc, 0xedda, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xfcf1, 0xa1a1, 0xc0e2, 0xa1a1, 0xe9ed, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf1d8, 
	0xa1a1, 0xcdd9, 0xa1a1, 0xa1a1, 0xd1d9, 0xa1a1, 0xa1a1, 0xddd9, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc9e0, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3e0, 0xa1a1, 0xc1e0, 
	0xc4e0, 0xefdf, 0xa1a1, 0xaae0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb2e0, 0xa1a1, 0xc7e0, 0xcae0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf7db, 0xeedb, 0xf3eb, 0xd5e1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xaadc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcae6, 0xa1a1, 0xf4eb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd3e1, 0xa1a1, 0xa1a1, 0xcde1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcfdb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb0ec, 0xa1a1, 
	0xe6e1, 0xa1a1, 0xa1a1, 0xbbe3, 0xbae3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xadea, 0xaeea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4b4, 0xa1a1, 
	0xa1a1, 0xf7de, 0xa1a1, 0x7fdd, 0x80dd, 0x81dd, 0x82dd, 0x83dd, 
	0x84dd, 0x85dd, 0x86dd, 0x87dd, 0x88dd, 0x89dd, 0x8add, 0x8bdd, 
	0x8cdd, 0x8ddd, 0x8edd, 0x8fdd, 0x90dd, 0x91dd, 0x92dd, 0x93dd, 
	0x94dd, 0x95dd, 0x96dd, 0x97dd, 0x98dd, 0x99dd, 0x9add, 0x9bdd, 
	0x9cdd, 0x9ddd, 0x9edd, 0x9fdd, 0xa0dd, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf8de, 0xf9de, 
	0xa1a1, 0xa1a1, 0xf4de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf5de, 0xa1a1, 0xf6de, 0xa1a1, 0xfade, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd2ea, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb8e9, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa8e9, 0xbbb4, 0xa1a1, 0xa1a1, 0xa9e9, 0xabe9, 
	0xa5e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xaab4, 0xa1a1, 0xa2e9, 0xb1e9, 0xb2e9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa7ec, 0xa1a1, 0xa6ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xeae9, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa8eb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe7e4, 0xa1a1, 0xe9e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xdae4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe1e4, 0xfcb3, 0xa1a1, 0xa1a1, 0xe3e4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe6e4, 0xd1e4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbfec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe4dc, 0xd2ec, 0xd4ec, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0x7fde, 0x80de, 0x81de, 0x82de, 0x83de, 0x84de, 
	0x85de, 0x86de, 0x87de, 0x88de, 0x89de, 0x8ade, 0x8bde, 0x8cde, 
	0x8dde, 0x8ede, 0x8fde, 0x90de, 0x91de, 0x92de, 0x93de, 0x94de, 
	0x95de, 0x96de, 0x97de, 0x98de, 0x99de, 0x9ade, 0x9bde, 0x9cde, 
	0x9dde, 0x9ede, 0x9fde, 0xa0de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf9ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf8e1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe2e7, 0xa1a1, 
	0xa1a1, 0xa5e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb3ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xb5ee, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf1f0, 0xa1a1, 0xe2f0, 0xf3f0, 
	0xd4b1, 0xa1a1, 0xf5f0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xaaf0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xfded, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd6ed, 
	0xd5ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd8ed, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdeb0, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe2f3, 0xa1a1, 0xa1a1, 
	0xe3f3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3f4, 0xa1a1, 
	0xa1a1, 0xb0e7, 0xaee7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xafe7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbbee, 0xa1a1, 0xa1a1, 0xc7f4, 
	0xc8f4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xedeb, 0xa1a1, 0xa1a1, 
	0xeeeb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf2eb, 
	0xa1a1, 0x7fdf, 0x80df, 0x81df, 0x82df, 0x83df, 0x84df, 0x85df, 
	0x86df, 0x87df, 0x88df, 0x89df, 0x8adf, 0x8bdf, 0x8cdf, 0x8ddf, 
	0x8edf, 0x8fdf, 0x90df, 0x91df, 0x92df, 0x93df, 0x94df, 0x95df, 
	0x96df, 0x97df, 0x98df, 0x99df, 0x9adf, 0x9bdf, 0x9cdf, 0x9ddf, 
	0x9edf, 0x9fdf, 0xa0df, 0xa1a1, 0xe1eb, 0xa1a1, 0xbaf4, 0xb9f4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3dd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd7dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd8dd, 0xd9dd, 0xa1a1, 0xdadd, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdddd, 0xa1a1, 0xdfdd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa6dd, 0xa1a1, 0xa1a1, 0xe5dd, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xccf2, 0xa1a1, 0xa1a1, 0xa1a1, 0xd9f2, 0xa1a1, 0xddf2, 0xa1a1, 
	0xa1a1, 0xdcf2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcef1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1f6, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfef5, 0xa5b4, 0xa1a1, 0xb2da, 
	0xb4da, 0xbcda, 0xa1a1, 0xa1a1, 0xa1a1, 0xb3da, 0xb7da, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf7f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xf4f4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3f4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcdf5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd0f5, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbcf5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xfbe9, 0xfae9, 0xa1a1, 0xa1a1, 0xa1a1, 0xd9e5, 0xa1a1, 
	0x7fe0, 0x80e0, 0x81e0, 0x82e0, 0x83e0, 0x84e0, 0x85e0, 0x86e0, 
	0x87e0, 0x88e0, 0x89e0, 0x8ae0, 0x8be0, 0x8ce0, 0x8de0, 0x8ee0, 
	0x8fe0, 0x90e0, 0x91e0, 0x92e0, 0x93e0, 0x94e0, 0x95e0, 0x96e0, 
	0x97e0, 0x98e0, 0x99e0, 0x9ae0, 0x9be0, 0x9ce0, 0x9de0, 0x9ee0, 
	0x9fe0, 0xa0e0, 0xd7e5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc7d4, 0xf9da, 0xa1a1, 0xa1a1, 0xaacd, 0xa5f5, 0xe8ee, 
	0xa1a1, 0xe6ee, 0xdaee, 0xdbee, 0xddee, 0xa1a1, 0xe1ee, 0xa5ef, 
	0xeaee, 0xa1a1, 0xead7, 0xe3ee, 0xe2ee, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3da, 0xc2f6, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf9bd, 0xa1a1, 0xa1a1, 0xa1a1, 0xfef1, 
	0xa1a1, 0xfdf1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xecd9, 0xa1a1, 
	0xbcf6, 0xd6d9, 0xa1a1, 0xa1a1, 0xc7d9, 0xdbcb, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd5d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe4d8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7e0, 
	0xa1a1, 0xd1e0, 0xa1a1, 0xa1a1, 0xc5d8, 0xe9df, 0xd2e0, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xaddc, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xacdc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcac9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd1e6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc6e6, 0xa1a1, 0xa1a1, 0xcbe6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7fe1, 
	0x80e1, 0x81e1, 0x82e1, 0x83e1, 0x84e1, 0x85e1, 0x86e1, 0x87e1, 
	0x88e1, 0x89e1, 0x8ae1, 0x8be1, 0x8ce1, 0x8de1, 0x8ee1, 0x8fe1, 
	0x90e1, 0x91e1, 0x92e1, 0x93e1, 0x94e1, 0x95e1, 0x96e1, 0x97e1, 
	0x98e1, 0x99e1, 0x9ae1, 0x9be1, 0x9ce1, 0x9de1, 0x9ee1, 0x9fe1, 
	0xa0e1, 0xa1a1, 0xefe5, 0xd6e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd0e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfde0, 0xa1a1, 0xa1a1, 0xa1a1, 0xdbe2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdae2, 0xfcf0, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa8ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5e3, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe6e2, 0xa1a1, 0xa1a1, 0xafea, 0xa8ea, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd2de, 0xa1a1, 
	0xd9bf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xccc1, 0xfbde, 
	0xe8de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1eb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc1e9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb6e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbce9, 0xc7e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xe7e8, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc0e9, 0xa1a1, 0xc3e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbfe8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe6e9, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfedc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb0e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xf5e4, 0xa1a1, 0xa1a1, 0xbdc5, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7fe2, 0x80e2, 
	0x81e2, 0x82e2, 0x83e2, 0x84e2, 0x85e2, 0x86e2, 0x87e2, 0x88e2, 
	0x89e2, 0x8ae2, 0x8be2, 0x8ce2, 0x8de2, 0x8ee2, 0x8fe2, 0x90e2, 
	0x91e2, 0x92e2, 0x93e2, 0x94e2, 0x95e2, 0x96e2, 0x97e2, 0x98e2, 
	0x99e2, 0x9ae2, 0x9be2, 0x9ce2, 0x9de2, 0x9ee2, 0x9fe2, 0xa0e2, 
	0xf1e4, 0xa1a1, 0xefe4, 0xa1a1, 0xdde4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd3e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xacd1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc1ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xb8f7, 
	0xa1a1, 0xa1a1, 0xb0e2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf8f0, 0xfdf0, 0xa1a1, 0xfaf0, 0xa1a1, 
	0xe4f1, 0xa1a1, 0xa1a1, 0xf2b3, 0xa1a1, 0xa1a1, 0xa6ee, 0xa1a1, 
	0xbad8, 0xa1a1, 0xdaed, 0xa1a1, 0xeab2, 0xb8ed, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdced, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf9ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbff1, 0xa1a1, 0xbef1, 0xa1a1, 0xa1a1, 0xedf3, 0xa1a1, 0xa1a1, 
	0xe4f3, 0xa1a1, 0xbfb9, 0xa1a1, 0xa1a1, 0xa1a1, 0xebf3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4f4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb9e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb5e7, 0xa1a1, 0xb3e7, 0xa1a1, 0xa1a1, 0xb8e7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xebf4, 0xecf4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe3f4, 0xa1a1, 0x7fe3, 0x80e3, 0x81e3, 
	0x82e3, 0x83e3, 0x84e3, 0x85e3, 0x86e3, 0x87e3, 0x88e3, 0x89e3, 
	0x8ae3, 0x8be3, 0x8ce3, 0x8de3, 0x8ee3, 0x8fe3, 0x90e3, 0x91e3, 
	0x92e3, 0x93e3, 0x94e3, 0x95e3, 0x96e3, 0x97e3, 0x98e3, 0x99e3, 
	0x9ae3, 0x9be3, 0x9ce3, 0x9de3, 0x9ee3, 0x9fe3, 0xa0e3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf0eb, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf5dd, 0xa1a1, 0xf2dd, 0xa1a1, 0xf0dd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3dd, 0xf4dd, 0xe8dd, 0xe9dd, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xeadd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xaadd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe1d8, 0xa1a1, 0xcdb1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdef2, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe2f2, 0xe0f2, 0xafc0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe4f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd1d1, 
	0xa1a1, 0xe7f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe3f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd1f1, 0xa1a1, 0xa1a1, 
	0xd5f1, 0xa1a1, 0xd3f1, 0xa1a1, 0xa1a1, 0xd6f1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xeaea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa2f6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc0da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe4ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4f5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd5f5, 0xbbd3, 0xa1a1, 0xa1a1, 0xa1a1, 0xbdf5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb5db, 0xa1a1, 0xa1a1, 
	0xb3db, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa8f5, 0xaaf5, 0xa1a1, 0xbfd2, 0xa1a1, 0xa1a1, 0xa1a1, 0xefee, 
	0xeeee, 0xa1a1, 0xf0ee, 0xa1a1, 0x7fe4, 0x80e4, 0x81e4, 0x82e4, 
	0x83e4, 0x84e4, 0x85e4, 0x86e4, 0x87e4, 0x88e4, 0x89e4, 0x8ae4, 
	0x8be4, 0x8ce4, 0x8de4, 0x8ee4, 0x8fe4, 0x90e4, 0x91e4, 0x92e4, 
	0x93e4, 0x94e4, 0x95e4, 0x96e4, 0x97e4, 0x98e4, 0x99e4, 0x9ae4, 
	0x9be4, 0x9ce4, 0x9de4, 0x9ee4, 0x9fe4, 0xa0e4, 0xf7ee, 0xa2ef, 
	0xa4ef, 0xa1a1, 0xa1a1, 0xfeee, 0xa8ef, 0xf1ee, 0xc6f6, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfbee, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa9ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbaf7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3d9, 
	0xd8d9, 0xa1a1, 0xa1a1, 0xd9d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xdbd8, 0xbddb, 0xa1a1, 0xa1a1, 0xa1a1, 0xe1e0, 0xd8df, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd9e0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xdce0, 0xa1a1, 0xbcdf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7e1, 0xc0e1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbde1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdce2, 
	0xa1a1, 0xd0e2, 0xa1a1, 0xa1a1, 0xefb1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7fe5, 0x80e5, 0x81e5, 0x82e5, 0x83e5, 
	0x84e5, 0x85e5, 0x86e5, 0x87e5, 0x88e5, 0x89e5, 0x8ae5, 0x8be5, 
	0x8ce5, 0x8de5, 0x8ee5, 0x8fe5, 0x90e5, 0x91e5, 0x92e5, 0x93e5, 
	0x94e5, 0x95e5, 0x96e5, 0x97e5, 0x98e5, 0x99e5, 0x9ae5, 0x9be5, 
	0x9ce5, 0x9de5, 0x9ee5, 0x9fe5, 0xa0e5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb4e3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfede, 0xa1a1, 0xefbe, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa7b5, 0xd8de, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xcbe9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc8e9, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcee9, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfde8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcce9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa7eb, 0xa1a1, 0xa3f2, 0xa1a1, 0xa1a1, 
	0xd4c0, 0xa1a1, 0xa1a1, 0xf8e4, 0xf7e4, 0xf9e4, 0xeae4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfbe4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6e4, 
	0xa1a1, 0xa1a1, 0xe4e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdaec, 0xa1a1, 0xa1a1, 0xd8ec, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7ec, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe1e9, 0xa1a1, 0xa1a1, 0xb2e2, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0x7fe6, 0x80e6, 0x81e6, 0x82e6, 0x83e6, 0x84e6, 
	0x85e6, 0x86e6, 0x87e6, 0x88e6, 0x89e6, 0x8ae6, 0x8be6, 0x8ce6, 
	0x8de6, 0x8ee6, 0x8fe6, 0x90e6, 0x91e6, 0x92e6, 0x93e6, 0x94e6, 
	0x95e6, 0x96e6, 0x97e6, 0x98e6, 0x99e6, 0x9ae6, 0x9be6, 0x9ce6, 
	0x9de6, 0x9ee6, 0x9fe6, 0xa0e6, 0xa1a1, 0xafe8, 0xf6e7, 0xa1a1, 
	0xa1a1, 0xaee8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfbf0, 0xf9f0, 0xfef0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4ee, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdded, 0xa1a1, 0xa1a1, 0xa1a1, 0xdfed, 0xfaec, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1f0, 0xa1a1, 0xa1a1, 
	0xc1f1, 0xa1a1, 0xe6f3, 0xa1a1, 0xe8f3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdbf4, 0xdaf4, 0xd8f4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xbde7, 0xc5e7, 0xa1a1, 0xa1a1, 0xc1e7, 0xc2e7, 
	0xc3e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcaf4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe5f4, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf9eb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbcf4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa2de, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfddd, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe8c4, 0xa1a1, 0xa1a1, 0xf8dd, 0xbbdd, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4de, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xeadc, 0xe4dd, 0xa1a1, 0xa1a1, 0xe0dc, 0xa1a1, 0xa1a1, 0xfbdd, 
	0xcadc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbdeb, 0x7fe7, 0x80e7, 0x81e7, 0x82e7, 0x83e7, 0x84e7, 0x85e7, 
	0x86e7, 0x87e7, 0x88e7, 0x89e7, 0x8ae7, 0x8be7, 0x8ce7, 0x8de7, 
	0x8ee7, 0x8fe7, 0x90e7, 0x91e7, 0x92e7, 0x93e7, 0x94e7, 0x95e7, 
	0x96e7, 0x97e7, 0x98e7, 0x99e7, 0x9ae7, 0x9be7, 0x9ce7, 0x9de7, 
	0x9ee7, 0x9fe7, 0xa0e7, 0xa1a1, 0xf6f2, 0xf8f2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xabd0, 0xace2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf3f2, 0xa1a1, 0xfaf2, 0xa1a1, 0xeff2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd8f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc1da, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc2da, 0xa1a1, 0xa1a1, 0xa1a1, 0xc5da, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe3ea, 0xa1a1, 
	0xe6ea, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdbf5, 0xa1a1, 
	0xa1a1, 0xdaf5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xd6f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb7db, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa6b5, 0xa1a1, 0xa1a1, 0xacf5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6ef, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf2ee, 0xabef, 0xb7ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xb2ef, 
	0xfaee, 0xafef, 0xa1a1, 0xb0ef, 0xa1a1, 0xc8f6, 0xa1a1, 0xb8ef, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe9ef, 0xa1a1, 
	0x7fe8, 0x80e8, 0x81e8, 0x82e8, 0x83e8, 0x84e8, 0x85e8, 0x86e8, 
	0x87e8, 0x88e8, 0x89e8, 0x8ae8, 0x8be8, 0x8ce8, 0x8de8, 0x8ee8, 
	0x8fe8, 0x90e8, 0x91e8, 0x92e8, 0x93e8, 0x94e8, 0x95e8, 0x96e8, 
	0x97e8, 0x98e8, 0x99e8, 0x9ae8, 0x9be8, 0x9ce8, 0x9de8, 0x9ee8, 
	0x9fe8, 0xa0e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcfe3, 
	0xcde3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xacf6, 
	0xa1a1, 0xa6f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa4f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc4e2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe0e6, 0xa1a1, 0xa1a1, 0xa1a1, 0xe6e6, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd8f7, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc9f7, 0xa1a1, 0xd0f6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb1f0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf3e1, 0xa1a1, 0xa1a1, 0xbed8, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5df, 0xa1a1, 
	0xa1a1, 0xdcdf, 0xa1a1, 0xa1a1, 0xe0df, 0xa1a1, 0xa1a1, 0xf7e0, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4db, 
	0xa1a1, 0xd3e6, 0xcde6, 0xa1a1, 0xc8e6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd4e6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xbbe1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xaee1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdee2, 
	0xa1a1, 0xa1a1, 0xdde2, 0xa1a1, 0xa1a1, 0xe8e1, 0xaded, 0xa9ba, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf8e2, 0xa1a1, 0x7fe9, 
	0x80e9, 0x81e9, 0x82e9, 0x83e9, 0x84e9, 0x85e9, 0x86e9, 0x87e9, 
	0x88e9, 0x89e9, 0x8ae9, 0x8be9, 0x8ce9, 0x8de9, 0x8ee9, 0x8fe9, 
	0x90e9, 0x91e9, 0x92e9, 0x93e9, 0x94e9, 0x95e9, 0x96e9, 0x97e9, 
	0x98e9, 0x99e9, 0x9ae9, 0x9be9, 0x9ce9, 0x9de9, 0x9ee9, 0x9fe9, 
	0xa0e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8df, 0xa1a1, 0xa7df, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5ea, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd0e9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd3e9, 0xa1a1, 0xd8e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xd2e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xece9, 0xe9e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xabeb, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbac2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xabe4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1e5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7ec, 0xa1a1, 0xdcec, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb3e2, 0xa1a1, 0xfde1, 
	0xf6e1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa6f1, 0xa1a1, 0xa1a1, 0xacf1, 0xa2f1, 
	0xa9f1, 0xa1a1, 0xa7f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xabde, 0xa1a1, 0xa1a1, 0xa1a1, 0x7fea, 0x80ea, 
	0x81ea, 0x82ea, 0x83ea, 0x84ea, 0x85ea, 0x86ea, 0x87ea, 0x88ea, 
	0x89ea, 0x8aea, 0x8bea, 0x8cea, 0x8dea, 0x8eea, 0x8fea, 0x90ea, 
	0x91ea, 0x92ea, 0x93ea, 0x94ea, 0x95ea, 0x96ea, 0x97ea, 0x98ea, 
	0x99ea, 0x9aea, 0x9bea, 0x9cea, 0x9dea, 0x9eea, 0x9fea, 0xa0ea, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7ed, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc0f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf4f3, 0xa1a1, 0xf6f3, 0xf5f3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf8f3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xdcf4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc8e7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd0cc, 0xa1a1, 0xc4e7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5c5, 0xf0f1, 0xa1a1, 0xa1a1, 
	0xa2ec, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1de, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe9dc, 0xa1a1, 0xdbdd, 
	0xa8de, 0xa1dd, 0xdedd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf1dc, 0xa1a1, 0xa1a1, 0xb5dd, 0xa1a1, 0xa1a1, 
	0xbcdc, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa5f3, 0xfbf2, 0xa1a1, 0xa2f3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa3f3, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcff2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xbde5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7feb, 0x80eb, 0x81eb, 
	0x82eb, 0x83eb, 0x84eb, 0x85eb, 0x86eb, 0x87eb, 0x88eb, 0x89eb, 
	0x8aeb, 0x8beb, 0x8ceb, 0x8deb, 0x8eeb, 0x8feb, 0x90eb, 0x91eb, 
	0x92eb, 0x93eb, 0x94eb, 0x95eb, 0x96eb, 0x97eb, 0x98eb, 0x99eb, 
	0x9aeb, 0x9beb, 0x9ceb, 0x9deb, 0x9eeb, 0x9feb, 0xa0eb, 0xbbda, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcada, 0xccda, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xd2da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdef5, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe1f5, 0xe4f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa6db, 0xa1a1, 0xadf5, 
	0xaff5, 0xaef5, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3ef, 0xc4ef, 0xbaef, 
	0xa1a1, 0xe0d5, 0xaaef, 0xa1a1, 0xbcef, 0xa1a1, 0xb9ef, 0xbeef, 
	0xcdee, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd5e3, 0xa1a1, 0xd0e3, 0xcbd1, 0xa1a1, 
	0xd1e3, 0xd3e3, 0xa1a1, 0xd4e3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb4f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdbf7, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd6f6, 0xd8f6, 0xa1a1, 0xa1a1, 0x7fec, 0x80ec, 0x81ec, 0x82ec, 
	0x83ec, 0x84ec, 0x85ec, 0x86ec, 0x87ec, 0x88ec, 0x89ec, 0x8aec, 
	0x8bec, 0x8cec, 0x8dec, 0x8eec, 0x8fec, 0x90ec, 0x91ec, 0x92ec, 
	0x93ec, 0x94ec, 0x95ec, 0x96ec, 0x97ec, 0x98ec, 0x99ec, 0x9aec, 
	0x9bec, 0x9cec, 0x9dec, 0x9eec, 0x9fec, 0xa0ec, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6f0, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb7f0, 0xe6f7, 0xa1a1, 0xe5f7, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfcf7, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeae0, 0xe2df, 0xa1a1, 
	0xe3e0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd5e6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdae1, 
	0xfce0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xfbe2, 0xa1a1, 0xf7b1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa9df, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdde9, 0xa1a1, 0xdfe8, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd5e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xdbe9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb4ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa8e5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb4e2, 0xa1a1, 0xa1a1, 0xa1a1, 0xb4e8, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb5ea, 0xa1a1, 0xb4ea, 0xa1a1, 
	0xaaf1, 0xa1a1, 0xf7f0, 0xeff0, 0xabf0, 0xa1a1, 0xacee, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7fed, 0x80ed, 0x81ed, 0x82ed, 0x83ed, 
	0x84ed, 0x85ed, 0x86ed, 0x87ed, 0x88ed, 0x89ed, 0x8aed, 0x8bed, 
	0x8ced, 0x8ded, 0x8eed, 0x8fed, 0x90ed, 0x91ed, 0x92ed, 0x93ed, 
	0x94ed, 0x95ed, 0x96ed, 0x97ed, 0x98ed, 0x99ed, 0x9aed, 0x9bed, 
	0x9ced, 0x9ded, 0x9eed, 0x9fed, 0xa0ed, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcded, 0xa1a1, 0xa1a1, 0xa1a1, 0xe1ed, 0xe2ed, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfcf3, 
	0xa1a1, 0xe5f3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfef3, 0xd9f3, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe9c0, 0xa1a1, 0xa1a1, 0xddf4, 0xcae7, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xedf4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe3f7, 0xeaf4, 0xa1a1, 0xa1a1, 0xc1f3, 0xb6ee, 0xc0ee, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xeff1, 0xfeeb, 0xa1a1, 0xfbeb, 0xfdeb, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbdf4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb2de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xaede, 
	0xafde, 0xa1a1, 0xa1a1, 0xf7dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xaade, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf6dc, 0xb6de, 0xb3de, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb2dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa4f3, 0xa1a1, 0xa1a1, 
	0xa9f3, 0xa1a1, 0xaaf3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfcf2, 0xddd5, 0xb1f3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xaef3, 0xa1a1, 0xa1a1, 0xa1a1, 0xcdf1, 0xa1a1, 0xa1a1, 
	0xdff1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xedea, 0xa1a1, 0xa1a1, 
	0xb2ec, 0xa1a1, 0x7fee, 0x80ee, 0x81ee, 0x82ee, 0x83ee, 0x84ee, 
	0x85ee, 0x86ee, 0x87ee, 0x88ee, 0x89ee, 0x8aee, 0x8bee, 0x8cee, 
	0x8dee, 0x8eee, 0x8fee, 0x90ee, 0x91ee, 0x92ee, 0x93ee, 0x94ee, 
	0x95ee, 0x96ee, 0x97ee, 0x98ee, 0x99ee, 0x9aee, 0x9bee, 0x9cee, 
	0x9dee, 0x9eee, 0x9fee, 0xa0ee, 0xa1a1, 0xd5da, 0xa1a1, 0xdfd6, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc0e5, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xd6da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf9f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc4f5, 0xbfe5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd1c3, 0xb0f5, 0xa9c8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcaef, 0xa1a1, 0xc7ef, 0xa1a1, 0xa1d5, 
	0xa1a1, 0xc8ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xccf6, 
	0xa1a1, 0xcaf6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd6e3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf4da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xbce2, 0xa1a1, 0xa1a1, 0xa1a1, 0xd7f4, 0xa1a1, 0xe5d9, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xccf7, 0xdaf6, 0xa1a1, 0xdcf6, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9f0, 0xa1a1, 0xa1a1, 
	0xbbf0, 0x7fef, 0x80ef, 0x81ef, 0x82ef, 0x83ef, 0x84ef, 0x85ef, 
	0x86ef, 0x87ef, 0x88ef, 0x89ef, 0x8aef, 0x8bef, 0x8cef, 0x8def, 
	0x8eef, 0x8fef, 0x90ef, 0x91ef, 0x92ef, 0x93ef, 0x94ef, 0x95ef, 
	0x96ef, 0x97ef, 0x98ef, 0x99ef, 0x9aef, 0x9bef, 0x9cef, 0x9def, 
	0x9eef, 0x9fef, 0xa0ef, 0xa1a1, 0xbcf0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xeaed, 0xbdf6, 0xa1a1, 0xa1a1, 0xf7f7, 0xb3f6, 
	0xdfd9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3de, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa3df, 0xa1a1, 0xa1a1, 0xa1a1, 0xd6ea, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdee4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf3e4, 
	0xf8e3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdeec, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb6ea, 0xb0f1, 0xdcf0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe4ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1f4, 0xa1a1, 
	0xa1a1, 0xa3f4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc0e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf9f1, 0xa1a1, 0xa1a1, 
	0x7ff0, 0x80f0, 0x81f0, 0x82f0, 0x83f0, 0x84f0, 0x85f0, 0x86f0, 
	0x87f0, 0x88f0, 0x89f0, 0x8af0, 0x8bf0, 0x8cf0, 0x8df0, 0x8ef0, 
	0x8ff0, 0x90f0, 0x91f0, 0x92f0, 0x93f0, 0x94f0, 0x95f0, 0x96f0, 
	0x97f0, 0x98f0, 0x99f0, 0x9af0, 0x9bf0, 0x9cf0, 0x9df0, 0x9ef0, 
	0x9ff0, 0xa0f0, 0xa1a1, 0xbef4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa3dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb8de, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb2f3, 
	0xa1a1, 0xb3f3, 0xa8f3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xadf3, 0xa1a1, 0xa1a1, 0xb1f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa5e1, 
	0xa1a1, 0xa1a1, 0xd0f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa9da, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1c3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa5f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf8f5, 0xa1a1, 0xd3d8, 0xdeea, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfea1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbff5, 0xa1a1, 0xa4ea, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf7da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xb5f5, 0xb2f5, 0xd8ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf8ee, 0xa1a1, 0xcbef, 0xa1a1, 0xd6ef, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf6dd, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc4e3, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6f7, 0xa1a1, 0xa1a1, 0x7ff1, 
	0x80f1, 0x81f1, 0x82f1, 0x83f1, 0x84f1, 0x85f1, 0x86f1, 0x87f1, 
	0x88f1, 0x89f1, 0x8af1, 0x8bf1, 0x8cf1, 0x8df1, 0x8ef1, 0x8ff1, 
	0x90f1, 0x91f1, 0x92f1, 0x93f1, 0x94f1, 0x95f1, 0x96f1, 0x97f1, 
	0x98f1, 0x99f1, 0x9af1, 0x9bf1, 0x9cf1, 0x9df1, 0x9ef1, 0x9ff1, 
	0xa0f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb8e8, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbee2, 0xa1a1, 0xebe6, 
	0xa1a1, 0xa1a1, 0xece6, 0xa1a1, 0xa1a1, 0xa1a1, 0xede6, 0xa1a1, 
	0xa1a1, 0xc2f7, 0xa1a1, 0xdcf7, 0xa1a1, 0xa1a1, 0xd2e3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe9f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3f0, 
	0xbef0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc1f0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf0f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc9d8, 
	0xa1a1, 0xa1a1, 0xbfdf, 0xa1a1, 0xa1a1, 0xa1a1, 0xe4db, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xabdf, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc6e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdde8, 0xa1a1, 0xa1a1, 0xcde9, 0xa1a1, 0xa1a1, 0xdae9, 
	0xa1a1, 0xa1a1, 0xaaeb, 0xa1a1, 0xf1e3, 0xebe4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xace5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ff2, 0x80f2, 
	0x81f2, 0x82f2, 0x83f2, 0x84f2, 0x85f2, 0x86f2, 0x87f2, 0x88f2, 
	0x89f2, 0x8af2, 0x8bf2, 0x8cf2, 0x8df2, 0x8ef2, 0x8ff2, 0x90f2, 
	0x91f2, 0x92f2, 0x93f2, 0x94f2, 0x95f2, 0x96f2, 0x97f2, 0x98f2, 
	0x99f2, 0x9af2, 0x9bf2, 0x9cf2, 0x9df2, 0x9ef2, 0x9ff2, 0xa0f2, 
	0xe7ed, 0xa1a1, 0xa1a1, 0xe6ed, 0xf2ec, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xd8e7, 0xa1a1, 0xd9e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbcee, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xaff4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbcde, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf9d9, 0xc9f2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xb2f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9f3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcff1, 
	0xa1a1, 0xa1a1, 0xefea, 0xa1a1, 0xa3f6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xdada, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe4b2, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xecf5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb3f5, 0xdeef, 0xe0ef, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeaef, 0xa1a1, 0xd2ef, 0xa1a1, 
	0xa1ef, 0xa1a1, 0xcbf6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ff3, 0x80f3, 0x81f3, 
	0x82f3, 0x83f3, 0x84f3, 0x85f3, 0x86f3, 0x87f3, 0x88f3, 0x89f3, 
	0x8af3, 0x8bf3, 0x8cf3, 0x8df3, 0x8ef3, 0x8ff3, 0x90f3, 0x91f3, 
	0x92f3, 0x93f3, 0x94f3, 0x95f3, 0x96f3, 0x97f3, 0x98f3, 0x99f3, 
	0x9af3, 0x9bf3, 0x9cf3, 0x9df3, 0x9ef3, 0x9ff3, 0xa0f3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xb9e8, 0xa1a1, 0xa1a1, 0xaaf2, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcae2, 0xa1a1, 0xc9e2, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc4f7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xecf6, 0xedf6, 0xa1a1, 
	0xa1a1, 0xeff6, 0xa1a1, 0xf2f6, 0xd3f6, 0xf6f6, 0xdff6, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xeef6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc7f0, 0xa1a1, 0xa1a1, 0xb4f0, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xebed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb7e0, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xfcde, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd0e8, 0xa1a1, 0xc0e8, 0xd3e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xafe5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xaee5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa8e2, 0xb7e8, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc7db, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe5ed, 0xa1a1, 0x7ff4, 0x80f4, 0x81f4, 0x82f4, 
	0x83f4, 0x84f4, 0x85f4, 0x86f4, 0x87f4, 0x88f4, 0x89f4, 0x8af4, 
	0x8bf4, 0x8cf4, 0x8df4, 0x8ef4, 0x8ff4, 0x90f4, 0x91f4, 0x92f4, 
	0x93f4, 0x94f4, 0x95f4, 0x96f4, 0x97f4, 0x98f4, 0x99f4, 0x9af4, 
	0x9bf4, 0x9cf4, 0x9df4, 0x9ef4, 0x9ff4, 0xa0f4, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf9ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd7e7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf7f1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbff4, 0xa1a1, 0xd7dc, 0xbdde, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xc2dc, 0xadde, 0xa1a1, 0xbfde, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xd3f2, 0xeef2, 0xa1a1, 0xb7f3, 0xb6f3, 0xa1a1, 
	0xe0f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9db, 0xa1a1, 
	0xa1a1, 0xb6f5, 0xa1a1, 0xa1a1, 0xa6ef, 0xe6ef, 0xa1a1, 0xe8ef, 
	0xa1a1, 0xe2ef, 0xa1a1, 0xa1a1, 0xcdc1, 0xd0ef, 0xebef, 0xa1a1, 
	0xfcee, 0xa1a1, 0xe4ef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa9ef, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xdbe3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa9f2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcde2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe3e6, 0xa1a1, 0xf2e6, 0xf3e6, 0xefe6, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf8f6, 
	0xa1a1, 0xa1a1, 0xfbf6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xfcf6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7ff5, 0x80f5, 0x81f5, 0x82f5, 0x83f5, 
	0x84f5, 0x85f5, 0x86f5, 0x87f5, 0x88f5, 0x89f5, 0x8af5, 0x8bf5, 
	0x8cf5, 0x8df5, 0x8ef5, 0x8ff5, 0x90f5, 0x91f5, 0x92f5, 0x93f5, 
	0x94f5, 0x95f5, 0x96f5, 0x97f5, 0x98f5, 0x99f5, 0x9af5, 0x9bf5, 
	0x9cf5, 0x9df5, 0x9ef5, 0x9ff5, 0xa0f5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcaf0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xcbf0, 0xcdf0, 0xa1a1, 0xc9f0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf4f7, 0xa1a1, 0xf3f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb6f6, 
	0xa1a1, 0xa1a1, 0xb5f6, 0xa1a1, 0xd0d9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf9bf, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa5df, 0xa1a1, 0xa1a1, 0xa1a1, 0xb7e9, 0xa1a1, 0xe3e3, 
	0xdce4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdfec, 0xa1a1, 
	0xa1a1, 0xb5e2, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc3ed, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xcff4, 0xfee6, 0xa1a1, 0xd3e7, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbede, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xc0de, 0xfcdd, 0xa1a1, 0xa1a1, 0xa1a1, 0xbaf3, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe1ea, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbadb, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xeeef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcdef, 0xa1a1, 0xcbe3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xadec, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf5e6, 0xeee6, 
	0xf1e6, 0xa1a1, 0x7ff6, 0x80f6, 0x81f6, 0x82f6, 0x83f6, 0x84f6, 
	0x85f6, 0x86f6, 0x87f6, 0x88f6, 0x89f6, 0x8af6, 0x8bf6, 0x8cf6, 
	0x8df6, 0x8ef6, 0x8ff6, 0x90f6, 0x91f6, 0x92f6, 0x93f6, 0x94f6, 
	0x95f6, 0x96f6, 0x97f6, 0x98f6, 0x99f6, 0x9af6, 0x9bf6, 0x9cf6, 
	0x9df6, 0x9ef6, 0x9ff6, 0xa0f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe5f6, 0xa3f7, 0xa5f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xcff0, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbdf7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb9e5, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbaf5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe5ea, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe6d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xaee6, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdcd4, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xaceb, 0xecc0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8f1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa6f0, 0xa1a1, 0xa1a1, 0xeaf3, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xe1d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb5f4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xebea, 0xa1a1, 0xa1a1, 
	0xd9da, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe9f5, 
	0xf0f5, 0xd1f5, 0xc8f5, 0xf1f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xf6e9, 
	0xa1a1, 0xd9ef, 0xa1a1, 0xecef, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xd3f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf6e6, 0xa1a1, 
	0xe8e6, 0x7ff7, 0x80f7, 0x81f7, 0x82f7, 0x83f7, 0x84f7, 0x85f7, 
	0x86f7, 0x87f7, 0x88f7, 0x89f7, 0x8af7, 0x8bf7, 0x8cf7, 0x8df7, 
	0x8ef7, 0x8ff7, 0x90f7, 0x91f7, 0x92f7, 0x93f7, 0x94f7, 0x95f7, 
	0x96f7, 0x97f7, 0x98f7, 0x99f7, 0x9af7, 0x9bf7, 0x9cf7, 0x9df7, 
	0x9ef7, 0x9ff7, 0xa0f7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xf7e5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8f7, 0xa1a1, 0xa1a1, 
	0xe4f6, 0xa6f7, 0xa1a1, 0xa1a1, 0xe6f6, 0xa1a1, 0xa1f7, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd2f0, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbaf0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xe8ed, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xacdf, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xefe8, 0xa1e9, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xb8d3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xf1dd, 0xa1a1, 0xa1a1, 0xc2de, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc3ee, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xc5f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xf1e9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdff7, 0xa1a1, 
	0xaef7, 0xe0f6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd3f0, 0xa1a1, 0xa1a1, 0xd6f0, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd4f0, 0xd5f0, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xc2f0, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf5f7, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xfbf7, 0xa1a1, 0xa1a1, 0xa1a1, 
	0x7ff8, 0x80f8, 0x81f8, 0x82f8, 0x83f8, 0x84f8, 0x85f8, 0x86f8, 
	0x87f8, 0x88f8, 0x89f8, 0x8af8, 0x8bf8, 0x8cf8, 0x8df8, 0x8ef8, 
	0x8ff8, 0x90f8, 0x91f8, 0x92f8, 0x93f8, 0x94f8, 0x95f8, 0x96f8, 
	0x97f8, 0x98f8, 0x99f8, 0x9af8, 0x9bf8, 0x9cf8, 0x9df8, 0x9ef8, 
	0x9ff8, 0xa0f8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xb0e5, 0xede4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xfdf3, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xbec0, 0xf3f5, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xcef6, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xc6f7, 0xa1a1, 0xa1a1, 
	0xaff7, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xf1eb, 0xece0, 
	0xaddf, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xd6f3, 0xa1a1, 0xd0f4, 0xa1a1, 
	0xdae7, 0xeef4, 0xa1a1, 0xf5d9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe1f1, 0xa1a1, 0xa1a1, 0xa1a1, 0xdab4, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xe7ef, 0xa5c7, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xcee2, 0xa1a1, 0xa1a1, 0xddf6, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbef6, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xbdf3, 0xf5f4, 
	0xf2f5, 0xa7f5, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ff9, 
	0x80f9, 0x81f9, 0x82f9, 0x83f9, 0x84f9, 0x85f9, 0x86f9, 0x87f9, 
	0x88f9, 0x89f9, 0x8af9, 0x8bf9, 0x8cf9, 0x8df9, 0x8ef9, 0x8ff9, 
	0x90f9, 0x91f9, 0x92f9, 0x93f9, 0x94f9, 0x95f9, 0x96f9, 0x97f9, 
	0x98f9, 0x99f9, 0x9af9, 0x9bf9, 0x9cf9, 0x9df9, 0x9ef9, 0x9ff9, 
	0xa0f9, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xddda, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa8f2, 0xa7c8, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xf8e6, 0xa1a1, 0xa1a1, 0xa1a1, 0xb5f0, 
	0xa1a1, 0xa1a1, 0xb0ed, 0xf9e8, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xe3ef, 0xa1a1, 0xa1a1, 0xcee3, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xe2f6, 0xbff0, 
	0xd9e4, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ffa, 0x80fa, 
	0x81fa, 0x82fa, 0x83fa, 0x84fa, 0x85fa, 0x86fa, 0x87fa, 0x88fa, 
	0x89fa, 0x8afa, 0x8bfa, 0x8cfa, 0x8dfa, 0x8efa, 0x8ffa, 0x90fa, 
	0x91fa, 0x92fa, 0x93fa, 0x94fa, 0x95fa, 0x96fa, 0x97fa, 0x98fa, 
	0x99fa, 0x9afa, 0x9bfa, 0x9cfa, 0x9dfa, 0x9efa, 0x9ffa, 0xa0fa, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ffb, 0x80fb, 0x81fb, 
	0x82fb, 0x83fb, 0x84fb, 0x85fb, 0x86fb, 0x87fb, 0x88fb, 0x89fb, 
	0x8afb, 0x8bfb, 0x8cfb, 0x8dfb, 0x8efb, 0x8ffb, 0x90fb, 0x91fb, 
	0x92fb, 0x93fb, 0x94fb, 0x95fb, 0x96fb, 0x97fb, 0x98fb, 0x99fb, 
	0x9afb, 0x9bfb, 0x9cfb, 0x9dfb, 0x9efb, 0x9ffb, 0xa0fb, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0x7ffc, 0x80fc, 0x81fc, 0x82fc, 
	0x83fc, 0x84fc, 0x85fc, 0x86fc, 0x87fc, 0x88fc, 0x89fc, 0x8afc, 
	0x8bfc, 0x8cfc, 0x8dfc, 0x8efc, 0x8ffc, 0x90fc, 0x91fc, 0x92fc, 
	0x93fc, 0x94fc, 0x95fc, 0x96fc, 0x97fc, 0x98fc, 0x99fc, 0x9afc, 
	0x9bfc, 0x9cfc, 0x9dfc, 0x9efc, 0x9ffc, 0xa0fc, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0x7ffd, 0x80fd, 0x81fd, 0x82fd, 0x83fd, 
	0x84fd, 0x85fd, 0x86fd, 0x87fd, 0x88fd, 0x89fd, 0x8afd, 0x8bfd, 
	0x8cfd, 0x8dfd, 0x8efd, 0x8ffd, 0x90fd, 0x91fd, 0x92fd, 0x93fd, 
	0x94fd, 0x95fd, 0x96fd, 0x97fd, 0x98fd, 0x99fd, 0x9afd, 0x9bfd, 
	0x9cfd, 0x9dfd, 0x9efd, 0x9ffd, 0xa0fd, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0x7ffe, 0x80fe, 0x81fe, 0x82fe, 0x83fe, 0x84fe, 
	0x85fe, 0x86fe, 0x87fe, 0x88fe, 0x89fe, 0x8afe, 0x8bfe, 0x8cfe, 
	0x8dfe, 0x8efe, 0x8ffe, 0x90fe, 0x91fe, 0x92fe, 0x93fe, 0x94fe, 
	0x95fe, 0x96fe, 0x97fe, 0x98fe, 0x99fe, 0x9afe, 0x9bfe, 0x9cfe, 
	0x9dfe, 0x9efe, 0x9ffe, 0xa0fe, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 0xa1a1, 
	0xa1a1, 0xa1a1, 
};



const WORD pGBTable[]=
{
    0x40a1, 0x42a1, 0x43a1, 0x45a1, 0xbca3, 0xbea3, 0xd8c6, 0xb2a1, 0xa4c6, 
    0x58a1, 0xe3a1, 0xfca1, 0x4ba1, 0xa5a1, 0xa6a1, 0xa7a1, 0xa8a1, 0x65a1, 
    0x66a1, 0x71a1, 0x72a1, 0x6da1, 0x6ea1, 0x75a1, 0x76a1, 0x79a1, 0x7aa1, 
    0x40a1, 0x40a1, 0x69a1, 0x6aa1, 0xd3a1, 0xd1a1, 0xd2a1, 0x4aa1, 0x40a1, 
    0x40a1, 0x55a3, 0x53a3, 0xe5a1, 0xe4a1, 0x40a1, 0x40a1, 0xd4a1, 0xe6a1, 
    0xfca1, 0xe7a1, 0x40a1, 0xf3a1, 0xeca1, 0xeda1, 0xdda1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0xdaa1, 0x40a1, 0x40a1, 0xd8a1, 0xd9a1, 0xdba1, 0xeea1, 
    0xefa1, 0xf1a1, 0xf0a1, 0x58a2, 0xaca1, 0xb2a1, 0x4aa2, 0x43a2, 0x40a1, 
    0x46a2, 0x47a2, 0x40a1, 0xb1a1, 0xd2c8, 0xb8a1, 0xb9a1, 0xb3a1, 0xb4a1, 
    0xb7a1, 0xbaa1, 0xbba1, 0xbca1, 0xbda1, 0xb5a1, 0xb6a1, 0xb0a1, 0xf7a1, 
    0xf6a1, 0xf4a1, 0xf5a1, 0xfef9, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0xf3c7, 0xf4c7, 0xf5c7, 0xf6c7, 0xf7c7, 
    0xf8c7, 0xf9c7, 0xfac7, 0xfbc7, 0xfcc7, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0xe9c7, 0xeac7, 0xebc7, 
    0xecc7, 0xedc7, 0xeec7, 0xefc7, 0xf0c7, 0xf1c7, 0xf2c7, 0x40a1, 0x40a1, 
    0x40a4, 0x47a4, 0x54a4, 0x7ca5, 0xada4, 0xbba4, 0x43a4, 0x4ba4, 0x45a4, 
    0x51a4, 0x40a1, 0x40a1, 0xb9a2, 0xbaa2, 0xbba2, 0xbca2, 0xbda2, 0xbea2, 
    0xbfa2, 0xc0a2, 0xc1a2, 0xc2a2, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x49a1, 
    0xa8a1, 0xada1, 0x44a2, 0x48a2, 0xaea1, 0xa6a1, 0x5da1, 0x5ea1, 0xafa1, 
    0xcfa1, 0x41a1, 0xd0a1, 0x44a1, 0x41a2, 0xafa2, 0xb0a2, 0xb1a2, 0xb2a2, 
    0xb3a2, 0xb4a2, 0xb5a2, 0xb6a2, 0xb7a2, 0xb8a2, 0x47a1, 0x46a1, 0xd5a1, 
    0xd7a1, 0xd6a1, 0x48a1, 0x49a2, 0xcfa2, 0xd0a2, 0xd1a2, 0xd2a2, 0xd3a2, 
    0xd4a2, 0xd5a2, 0xd6a2, 0xd7a2, 0xd8a2, 0xd9a2, 0xdaa2, 0xdba2, 0xdca2, 
    0xdda2, 0xdea2, 0xdfa2, 0xe0a2, 0xe1a2, 0xe2a2, 0xe3a2, 0xe4a2, 0xe5a2, 
    0xe6a2, 0xe7a2, 0xe8a2, 0x65a1, 0x42a2, 0x66a1, 0x73a1, 0xc4a1, 0xa5a1, 
    0xe9a2, 0xeaa2, 0xeba2, 0xeca2, 0xeda2, 0xeea2, 0xefa2, 0xf0a2, 0xf1a2, 
    0xf2a2, 0xf3a2, 0xf4a2, 0xf5a2, 0xf6a2, 0xf7a2, 0xf8a2, 0xf9a2, 0xfaa2, 
    0xfba2, 0xfca2, 0xfda2, 0xfea2, 0x40a3, 0x41a3, 0x42a3, 0x43a3, 0x61a1, 
    0x55a1, 0x62a1, 0xc3a1, 0xa5c6, 0xa6c6, 0xa7c6, 0xa8c6, 0xa9c6, 0xaac6, 
    0xabc6, 0xacc6, 0xadc6, 0xaec6, 0xafc6, 0xb0c6, 0xb1c6, 0xb2c6, 0xb3c6, 
    0xb4c6, 0xb5c6, 0xb6c6, 0xb7c6, 0xb8c6, 0xb9c6, 0xbac6, 0xbbc6, 0xbcc6, 
    0xbdc6, 0xbec6, 0xbfc6, 0xc0c6, 0xc1c6, 0xc2c6, 0xc3c6, 0xc4c6, 0xc5c6, 
    0xc6c6, 0xc7c6, 0xc8c6, 0xc9c6, 0xcac6, 0xcbc6, 0xccc6, 0xcdc6, 0xcec6, 
    0xcfc6, 0xd0c6, 0xd1c6, 0xd2c6, 0xd3c6, 0xd4c6, 0xd5c6, 0xd6c6, 0xd7c6, 
    0xd8c6, 0xd9c6, 0xdac6, 0xdbc6, 0xdcc6, 0xddc6, 0xdec6, 0xdfc6, 0xe0c6, 
    0xe1c6, 0xe2c6, 0xe3c6, 0xe4c6, 0xe5c6, 0xe6c6, 0xe7c6, 0xe8c6, 0xe9c6, 
    0xeac6, 0xebc6, 0xecc6, 0xedc6, 0xeec6, 0xefc6, 0xf0c6, 0xf1c6, 0xf2c6, 
    0xf3c6, 0xf4c6, 0xf5c6, 0xf6c6, 0xf7c6, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0xf8c6, 0xf9c6, 
    0xfac6, 0xfbc6, 0xfcc6, 0xfdc6, 0xfec6, 0x40c7, 0x41c7, 0x42c7, 0x43c7, 
    0x44c7, 0x45c7, 0x46c7, 0x47c7, 0x48c7, 0x49c7, 0x4ac7, 0x4bc7, 0x4cc7, 
    0x4dc7, 0x4ec7, 0x4fc7, 0x50c7, 0x51c7, 0x52c7, 0x53c7, 0x54c7, 0x55c7, 
    0x56c7, 0x57c7, 0x58c7, 0x59c7, 0x5ac7, 0x5bc7, 0x5cc7, 0x5dc7, 0x5ec7, 
    0x5fc7, 0x60c7, 0x61c7, 0x62c7, 0x63c7, 0x64c7, 0x65c7, 0x66c7, 0x67c7, 
    0x68c7, 0x69c7, 0x6ac7, 0x6bc7, 0x6cc7, 0x6dc7, 0x6ec7, 0x6fc7, 0x70c7, 
    0x71c7, 0x72c7, 0x73c7, 0x74c7, 0x75c7, 0x76c7, 0x77c7, 0x78c7, 0x79c7, 
    0x7ac7, 0x7bc7, 0x7cc7, 0x7dc7, 0x7ec7, 0xa1c7, 0xa2c7, 0xa3c7, 0xa4c7, 
    0xa5c7, 0xa6c7, 0xa7c7, 0xa8c7, 0xa9c7, 0xaac7, 0xabc7, 0xacc7, 0xadc7, 
    0xaec7, 0xafc7, 0xb0c7, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x44a3, 0x45a3, 0x46a3, 0x47a3, 0x48a3, 0x49a3, 0x4aa3, 
    0x4ba3, 0x4ca3, 0x4da3, 0x4ea3, 0x4fa3, 0x50a3, 0x51a3, 0x52a3, 0x53a3, 
    0x54a3, 0x55a3, 0x56a3, 0x57a3, 0x58a3, 0x59a3, 0x5aa3, 0x5ba3, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x5ca3, 0x5da3, 
    0x5ea3, 0x5fa3, 0x60a3, 0x61a3, 0x62a3, 0x63a3, 0x64a3, 0x65a3, 0x66a3, 
    0x67a3, 0x68a3, 0x69a3, 0x6aa3, 0x6ba3, 0x6ca3, 0x6da3, 0x6ea3, 0x6fa3, 
    0x70a3, 0x71a3, 0x72a3, 0x73a3, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x5fa1, 0x60a1, 0x67a1, 0x68a1, 0x73a1, 0x74a1, 0x6fa1, 
    0x70a1, 0x77a1, 0x78a1, 0x7ba1, 0x7ca1, 0x40a1, 0x40a1, 0x6ba1, 0x6ca1, 
    0x63a1, 0x64a1, 0x57a1, 0x40a1, 0x59a1, 0x5ba1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0xf3c7, 0xf4c7, 0xf5c7, 
    0xf6c7, 0xb1c7, 0xb2c7, 0xb3c7, 0xb4c7, 0xb5c7, 0xb6c7, 0xb7c7, 0xb8c7, 
    0xb9c7, 0xbac7, 0x42c8, 0x43c8, 0x44c8, 0x45c8, 0x46c8, 0x47c8, 0xbbc7, 
    0xbcc7, 0xbdc7, 0xbec7, 0xbfc7, 0xc0c7, 0xc1c7, 0xc2c7, 0xc3c7, 0xc4c7, 
    0xc5c7, 0xc6c7, 0xc7c7, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0xc8c7, 0xc9c7, 0xcac7, 0xcbc7, 0xccc7, 0xcdc7, 0xcec7, 0xcfc7, 0xd0c7, 
    0xd1c7, 0xd2c7, 0xd3c7, 0xd4c7, 0xd5c7, 0xd6c7, 0xd7c7, 0xd8c7, 0xd9c7, 
    0xdac7, 0xdbc7, 0xdcc7, 0xddc7, 0xdec7, 0xdfc7, 0xe0c7, 0xe1c7, 0xe2c7, 
    0xe3c7, 0xe4c7, 0xe5c7, 0xe6c7, 0xe7c7, 0xe8c7, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x74a3, 0x75a3, 0x76a3, 0x77a3, 0x78a3, 0x79a3, 0x7aa3, 0x7ba3, 
    0x7ca3, 0x7da3, 0x7ea3, 0xa1a3, 0xa2a3, 0xa3a3, 0xa4a3, 0xa5a3, 0xa6a3, 
    0xa7a3, 0xa8a3, 0xa9a3, 0xaaa3, 0xaba3, 0xaca3, 0xada3, 0xaea3, 0xafa3, 
    0xb0a3, 0xb1a3, 0xb2a3, 0xb3a3, 0xb4a3, 0xb5a3, 0xb6a3, 0xb7a3, 0xb8a3, 
    0xb9a3, 0xbaa3, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x77a2, 
    0x77a2, 0x78a2, 0x78a2, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x7aa2, 0x7aa2, 0x7aa2, 0x7aa2, 0x7ba2, 0x7ba2, 0x7ba2, 
    0x7ba2, 0x7ca2, 0x7ca2, 0x7ca2, 0x7ca2, 0x7da2, 0x7da2, 0x7da2, 0x7da2, 
    0x75a2, 0x75a2, 0x75a2, 0x75a2, 0x75a2, 0x75a2, 0x75a2, 0x75a2, 0x74a2, 
    0x74a2, 0x74a2, 0x74a2, 0x74a2, 0x74a2, 0x74a2, 0x74a2, 0x73a2, 0x73a2, 
    0x73a2, 0x73a2, 0x73a2, 0x73a2, 0x73a2, 0x73a2, 0x72a2, 0x72a2, 0x72a2, 
    0x72a2, 0x72a2, 0x72a2, 0x72a2, 0x72a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 
    0x71a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 0x71a2, 
    0x71a2, 0x71a2, 0x71a2, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0xdab0, 0xfcaa, 0x4aae, 0xc1ae, 0x75ab, 0xfcad, 0x73ab, 0x4abd, 0xf9c0, 
    0xa7c4, 0x47b8, 0xe3a6, 0xaac3, 0x52b7, 0x69b9, 0x62be, 0xf2ae, 0x77a6, 
    0xcdad, 0xf6ab, 0x74b7, 0xa4a9, 0x69d3, 0xd7ae, 0xaccd, 0xf9a9, 0x73af, 
    0x57a5, 0xceb1, 0xf5bc, 0xacbf, 0xcdc3, 0xc6b6, 0xf8b6, 0xd2be, 0x44bf, 
    0xddaa, 0xc3ae, 0xb6a5, 0x7aa5, 0x61a7, 0xb9af, 0x4ba4, 0xcdac, 0xdaa4, 
    0xdea9, 0x5bb6, 0x76b9, 0xe2a7, 0xd2af, 0xf2c5, 0x51c5, 0x7dbd, 0xa8aa, 
    0xd5a5, 0x66ac, 0xcaa6, 0x5cc2, 0xd5a8, 0xd1b1, 0xf4ab, 0xfbde, 0xb3b4, 
    0x5aaf, 0x68b7, 0xe6a7, 0xebaf, 0x7bb9, 0x4faa, 0xa9aa, 0xeaa7, 0xd5a9, 
    0xf1a6, 0xa4c3, 0x62a5, 0xecbf, 0xccb2, 0xb9a8, 0xb0c0, 0xf5b1, 0x5dba, 
    0x48bb, 0x6ab8, 0xceb4, 0x53bd, 0x46b0, 0xe9c2, 0xc4b3, 0xbdc1, 0x63ad, 
    0x4dad, 0x5da5, 0xc7bd, 0xe9ad, 0xa1c1, 0x72b9, 0x4fab, 0xf9b3, 0xa1b9, 
    0x5fc4, 0xeaa9, 0xf8b3, 0xc9bc, 0x5cb0, 0x6ac0, 0x7ac3, 0x4daa, 0x4fb8, 
    0x64b4, 0xf5a8, 0x5fa5, 0xfabd, 0x49ad, 0xa9a8, 0x58be, 0xbfad, 0x56af, 
    0xc6b3, 0xcebe, 0x48b5, 0x51b3, 0x62a9, 0x66ad, 0xbba5, 0xc2b2, 0x59b1, 
    0x5ec1, 0xc7ac, 0xa6ac, 0xdbc2, 0x6eb0, 0x47b9, 0xf3bb, 0xf1a4, 0xc0bb, 
    0xa7b5, 0xbca9, 0xd1ba, 0xc9e3, 0xaabd, 0xa6b2, 0xc5c0, 0x68cf, 0xf4b9, 
    0xc8a7, 0xcdde, 0xacb3, 0xcdb1, 0xfab9, 0xb2a5, 0x40b9, 0xc0be, 0x75c1, 
    0xd7c1, 0xa1b0, 0x40c3, 0xe4c3, 0x73bd, 0x53b6, 0xf3ab, 0x4bab, 0xdcc5, 
    0xcba4, 0xebbf, 0x47c5, 0x7cc4, 0x4db9, 0xd0bc, 0x43b3, 0xa8bd, 0xedaa, 
    0xbec5, 0x78e5, 0x4fa7, 0xa7c3, 0x6cb1, 0x79d9, 0x78c3, 0xd8c0, 0xabbb, 
    0xe8ec, 0x4ca7, 0x42a6, 0x60ac, 0xfea4, 0xc3aa, 0xe6bb, 0xb1ac, 0x66af, 
    0x7da6, 0xc1ac, 0xd4b5, 0xbdbc, 0xb7bc, 0xdab2, 0x69aa, 0xd5b3, 0x6bab, 
    0x69b7, 0x60b9, 0xe4ba, 0x42a7, 0xada9, 0xedb2, 0xe4b2, 0x4bbb, 0xf1b4, 
    0x79aa, 0xe9bb, 0xb7ae, 0x52a4, 0xf7ad, 0xc9b8, 0xf0b0, 0xa3a4, 0xaca5, 
    0x42a8, 0xafc3, 0xa1b3, 0xc6a9, 0xbfc0, 0x71b2, 0xf4b5, 0xf7a7, 0x7ea4, 
    0x5db0, 0x42b8, 0xf2bd, 0xf6aa, 0x6db1, 0xe6b5, 0xb2bd, 0x5cc0, 0xd1b0, 
    0xc9d3, 0xddb4, 0x46ba, 0x47ba, 0xe9c0, 0x61bb, 0xb5bf, 0xdcad, 0xc9b7, 
    0xc3c2, 0xdebe, 0x57c1, 0xd1bc, 0xe4b1, 0xf3af, 0x5ab4, 0xa6b5, 0xbcb0, 
    0x55a5, 0xfab4, 0x68bc, 0x4ff3, 0xa1b4, 0x65a4, 0xb3d3, 0xf9af, 0x64ac, 
    0xede2, 0x67b7, 0xeeb9, 0xc3a7, 0x74ae, 0xd2b8, 0xeea9, 0xe3ae, 0x5bb0, 
    0x65c4, 0x55ba, 0xcdc2, 0x61c6, 0xfec5, 0xf1c4, 0xeac3, 0xa3b2, 0xc4c4, 
    0xb8c5, 0xf7a9, 0x73b2, 0xf5b3, 0xc1b9, 0x60b1, 0xf8aa, 0x76c0, 0x7ab8, 
    0x44c9, 0xafb4, 0x5aba, 0xdbb0, 0xd2ad, 0x57b6, 0xdba7, 0x72b6, 0xc2b4, 
    0x4abc, 0xe9bc, 0x5fb1, 0x6ea7, 0xa3aa, 0xaea8, 0xe8a7, 0x4dba, 0x77b4, 
    0xfdb9, 0xabba, 0xebd7, 0xdaa6, 0xb0a8, 0xd0b9, 0xe1b1, 0xd5a7, 0x49a8, 
    0xafb3, 0x58b6, 0xa8c5, 0xb5bc, 0xd9ba, 0xb0ab, 0xedbe, 0xa8a6, 0x65a7, 
    0xbcad, 0x7bb5, 0x67c3, 0xe1bc, 0xdbb8, 0xd3a9, 0x78b3, 0xfec1, 0xafaf, 
    0x59a6, 0xf6b7, 0xf9ab, 0xcdb0, 0xc0a6, 0xf0bf, 0xa2a6, 0xa3b9, 0xa2ae, 
    0xa6be, 0xd7a8, 0xd8a4, 0xaaa8, 0xcdaf, 0xb8a5, 0x4bbf, 0x52a5, 0xc4bd, 
    0xe4a6, 0x52b1, 0x64c3, 0xe2a9, 0x53b9, 0xa5c3, 0x43c5, 0x59b8, 0x54b7, 
    0x77c4, 0xb3a4, 0xf7ba, 0xe4e2, 0xa1a4, 0xe4af, 0xecaa, 0x58a5, 0x6fc3, 
    0x70bc, 0xdcc3, 0x53be, 0xfac2, 0x51de, 0xa3b0, 0xa1b7, 0xa6c2, 0x78c0, 
    0xf7c5, 0x7add, 0x44e0, 0x42b3, 0xa2b4, 0x74a4, 0xefac, 0xdcdd, 0xc7b6, 
    0xeeb2, 0xddb3, 0xeaa6, 0x48bd, 0xa1b5, 0x6cbc, 0xc9a7, 0xf4c2, 0xd0b3, 
    0x6aa7, 0xa4aa, 0xa8b4, 0xe8c1, 0xabab, 0x4bac, 0xcfdd, 0x4abe, 0x42ae, 
    0x45b2, 0xc2af, 0xf8c4, 0x57c2, 0xefba, 0xabb2, 0xfdaf, 0xcfba, 0xdbbb, 
    0xe3c3, 0x4fb7, 0xa1b2, 0xfcb5, 0xb9a6, 0xeba8, 0xe7bd, 0xb8a6, 0x6fc1, 
    0xb5bd, 0x77a7, 0x5ea5, 0x71b1, 0x4fc2, 0xeab4, 0xcab2, 0x4cbe, 0x4cc1, 
    0x50ab, 0x4ff9, 0x79bf, 0xabc2, 0x52ba, 0x5ab1, 0xcab6, 0xdcaf, 0xf1b7, 
    0xe9ba, 0x66b2, 0x41bb, 0xf8a7, 0x73a6, 0x6fa4, 0x52bd, 0xbcbc, 0x62b7, 
    0xb9b1, 0xc0ae, 0xf9bf, 0x66b7, 0x46b9, 0xaab5, 0x44bd, 0xb4a5, 0x6aa4, 
    0x62a7, 0xefa4, 0x56d8, 0xb9c0, 0x61b1, 0x70ac, 0x4ea5, 0x55b6, 0x55b3, 
    0xddab, 0x65b6, 0xe5ab, 0xd4af, 0xe1be, 0xa6a4, 0xe6b3, 0x58e8, 0xb8e5, 
    0x78c1, 0xb9a5, 0xe1b4, 0xfda6, 0xaabc, 0x48b2, 0xcfbd, 0x75bc, 0x4ab3, 
    0xedb7, 0xd7be, 0xded0, 0xbabf, 0xc9c0, 0x4da4, 0x6fb7, 0xd0c1, 0xcbad, 
    0x71ae, 0xabc3, 0xc9be, 0xeca8, 0x5fbd, 0xa5b1, 0x44b9, 0x73b5, 0x77bc, 
    0x6fb1, 0xbaaa, 0xdec3, 0x4fbf, 0x6eb5, 0xa5b5, 0xfcc0, 0xb9b9, 0x48be, 
    0xf6b3, 0x43a7, 0x77ba, 0x7dad, 0xc4bc, 0xc3b2, 0x66a8, 0xb0ba, 0x43bb, 
    0xdeb9, 0xe8a9, 0xb3a9, 0x61a6, 0xa6b8, 0xc4b2, 0xd2ab, 0xcca7, 0xbcbb, 
    0x6cbd, 0x41c4, 0x69d5, 0xbfb7, 0x4bb8, 0x49c2, 0xe5a8, 0x51c0, 0xd4b9, 
    0x71b9, 0xfaa6, 0x6ca8, 0xb1a9, 0x7db1, 0xfeb3, 0xe0d5, 0xb5b7, 0x4db8, 
    0x70a5, 0x4ac0, 0xe4ad, 0x4ea4, 0xbcb1, 0x51a6, 0xa8b3, 0xd5bd, 0x5eb6, 
    0x52af, 0xd0ba, 0xbabd, 0xa1ad, 0xd2bf, 0x7cc5, 0x42a4, 0x6ea8, 0x6da5, 
    0x76b0, 0xbbb3, 0xa9b9, 0xf5bf, 0x77a9, 0x71ad, 0xe1a5, 0x46aa, 0x56a5, 
    0xb3b8, 0xb4c0, 0xcab0, 0xc9b4, 0xbecb, 0xf0ab, 0xe1ad, 0x7dac, 0xc2b0, 
    0xdda7, 0xe6a4, 0x7eb0, 0xa7a8, 0x72b3, 0x6bb5, 0xa3b3, 0xfeb7, 0x72ac, 
    0x7dc3, 0x57bf, 0xaac5, 0xf4b0, 0x40b8, 0xe4bd, 0xf9a7, 0xe1c1, 0x7ba8, 
    0xd7ab, 0xe7b4, 0xaaa7, 0xddba, 0x75b5, 0xebc1, 0x71ac, 0x5fc2, 0x76bd, 
    0xefb0, 0x49a7, 0xa4b6, 0xefb9, 0x5bbc, 0xa4ca, 0xdbc3, 0xb0b4, 0x79b9, 
    0x79a7, 0x77b6, 0xdeac, 0x50b9, 0x73d5, 0x47ce, 0x68a6, 0xdcb9, 0x57ce, 
    0xfab8, 0xb7a6, 0xf6b8, 0xebb2, 0xefa8, 0x6bb4, 0x5abc, 0xbfb8, 0x6fae, 
    0x5ac3, 0x58ab, 0x42c3, 0x5fb3, 0x5aae, 0x63b4, 0xcca4, 0xe3a7, 0x4bb9, 
    0x6bb6, 0x6abe, 0xa6ae, 0xd3a6, 0x49a4, 0xd5a6, 0xb8ba, 0xe7bb, 0x7cac, 
    0x47a4, 0x4cb6, 0x6fb5, 0x40bb, 0xadb5, 0xefa5, 0x46a5, 0xd6bb, 0x6baa, 
    0x57b5, 0xbfc3, 0x7ca6, 0x66b5, 0xbdc2, 0xd4bc, 0x73c4, 0xabb3, 0x63c1, 
    0x5aa4, 0xd0b7, 0xcfa4, 0xf0aa, 0x53ad, 0x63b3, 0xc7a5, 0xbab6, 0x78aa, 
    0x7ba7, 0xdaaa, 0xe8a4, 0xd5aa, 0xd0a9, 0xbea8, 0xaba7, 0xe9a5, 0x58b3, 
    0xbcaf, 0xf1a9, 0xe1b5, 0x44ab, 0xd8b0, 0xb8ad, 0xceaa, 0xeaad, 0xdabd, 
    0x70a7, 0xcdaa, 0x6fbc, 0x6daa, 0x4fb6, 0xe2aa, 0xf4d7, 0x68a7, 0x5eaa, 
    0xc0a4, 0xc9af, 0x58bc, 0x49b5, 0x57a8, 0xbbaf, 0xc4be, 0xf7a5, 0xc1a9, 
    0xabbc, 0x54c1, 0xa5a4, 0xcaab, 0xacb7, 0xc1b8, 0x70ae, 0x57be, 0xb7ad, 
    0xc6ba, 0x6cb2, 0x7bb3, 0xbeb6, 0x5fc1, 0xd8bf, 0x5ea9, 0xf1bb, 0xf2a6, 
    0x5fa7, 0xd2a4, 0xc5bc, 0xa7bd, 0xe5b9, 0xdfa7, 0xd8a9, 0xe7bf, 0x54b4, 
    0x74ac, 0xc5b2, 0xf1a5, 0x52ab, 0x41aa, 0x42af, 0x65b2, 0xd6ba, 0xf6b5, 
    0xb1a5, 0x6aa8, 0xbebc, 0xb2bb, 0xc1ad, 0x79b0, 0xf2a9, 0xe3b2, 0xc6b5, 
    0xb2a9, 0x47bb, 0x75ad, 0xc6b0, 0xd0c2, 0xe1bd, 0x60ce, 0xc5b3, 0x49a5, 
    0xfaaa, 0xf7a4, 0xa1b8, 0x74ad, 0x49b4, 0x72ad, 0xfeaa, 0xfcb0, 0xa3bf, 
    0x4aa9, 0xbfbe, 0xc7b9, 0xd3b8, 0xefa7, 0xa7b7, 0x74b6, 0x5cbb, 0x40b5, 
    0x7aa4, 0xcca5, 0xfda7, 0x61ac, 0xf1ac, 0x78a8, 0xe3d3, 0x50b7, 0x7ab5, 
    0xb1b4, 0x42c6, 0xa3a9, 0xe8ad, 0xfbbf, 0xfbac, 0x7aa8, 0xf5ba, 0x5eb1, 
    0xe4b4, 0xfea7, 0x74bf, 0x6faf, 0xaab0, 0x49bb, 0xccaf, 0x7cbf, 0x64b7, 
    0xeec2, 0x5abd, 0x69a7, 0xf4ad, 0x71ba, 0xc1c0, 0xe0a4, 0x46c2, 0xdeaf, 
    0xb8aa, 0xceb3, 0xb2ad, 0xafb8, 0xe6ae, 0xf0b5, 0xd5bb, 0x6ab9, 0xccbb, 
    0xd3ad, 0x55a6, 0xb9b5, 0xdaae, 0xf2b8, 0xd1af, 0xf3a7, 0xb0a9, 0xbcc3, 
    0x47ae, 0xd5af, 0xf0b1, 0x75a4, 0xf0a7, 0x5ca5, 0xa5ae, 0xc7c5, 0xd1a8, 
    0x60b0, 0xbda4, 0x63ae, 0x7da4, 0x64be, 0x45a8, 0xfdab, 0x5eb0, 0x40a6, 
    0x5fb9, 0xc4a4, 0xbeb7, 0x65ad, 0xafaa, 0xafab, 0xdbcc, 0xcac1, 0xf7b0, 
    0x64b6, 0xa3db, 0x42a9, 0x54e3, 0xf4a6, 0x66aa, 0x74a9, 0x68a9, 0xaab9, 
    0x6aa5, 0xdbc5, 0xa9b0, 0xa6a8, 0xd1aa, 0x47ac, 0x55c5, 0x54a9, 0xb1b6, 
    0xeda8, 0xcaa5, 0x6cd4, 0xe8b9, 0x45ac, 0x6fbb, 0xc4a8, 0xe4a9, 0xc7a9, 
    0xc3b4, 0xf6c3, 0x78a9, 0x61ab, 0x5bc6, 0xdeba, 0x5dc0, 0xf8c5, 0x44ba, 
    0xe9c4, 0x65b3, 0xfaa5, 0x73bc, 0x7db3, 0xc0ba, 0x57b3, 0x63a6, 0xbad6, 
    0x6bc2, 0x74c0, 0xd3bb, 0x79ad, 0xadb0, 0xdeb8, 0xd1ac, 0xdbae, 0x40cf, 
    0xf7b8, 0x51b6, 0x44bc, 0x40be, 0x75ba, 0xd2b4, 0xe7c1, 0xa2b3, 0xeab0, 
    0x47aa, 0x71bb, 0x4cb9, 0xa2ab, 0x65c0, 0xc4ab, 0xfcae, 0xf3ae, 0xe8a5, 
    0x60ae, 0x62c0, 0x6fb6, 0x77e9, 0xd5cd, 0xfac1, 0x74a7, 0x5bb2, 0x48b4, 
    0xe7a8, 0xdbb3, 0x75a8, 0xabbf, 0xd9be, 0xc2ae, 0xf2a7, 0xd1be, 0xabae, 
    0x6bb2, 0xbda6, 0x7eba, 0x71c9, 0x43aa, 0xe8af, 0xa2c0, 0x7ac0, 0xa8bb, 
    0x40b2, 0x71b0, 0x6ea6, 0xd3af, 0xb9b8, 0x45af, 0xfea8, 0xdcb3, 0xfcb2, 
    0x5ddb, 0xd6ae, 0xdda5, 0x4da9, 0xf3a6, 0x58a6, 0xb0b2, 0xe8b8, 0xd2bb, 
    0x65aa, 0x55b2, 0xaebb, 0xc5bd, 0x62c5, 0x50b6, 0x4bbc, 0xc2b6, 0xaab2, 
    0xdcab, 0xbdac, 0xebab, 0xf3ad, 0xeba6, 0xeebe, 0xc5bf, 0xdaf9, 0x46c5, 
    0xa1ab, 0x4daf, 0x69ad, 0x45c2, 0x78ac, 0xbba7, 0xb0a5, 0xf5ac, 0xefb3, 
    0x4aab, 0x55b5, 0x71a7, 0x70ab, 0xd4ad, 0x5aa6, 0x49a9, 0x47a5, 0xbfa9, 
    0xe4b7, 0xfdb3, 0xacb8, 0x4aad, 0xb9bd, 0xb0aa, 0x6bbd, 0xf2b4, 0xb7a9, 
    0xeaaa, 0xe4b0, 0x40c5, 0xaca4, 0xadba, 0xe1a4, 0xe1aa, 0x4dbc, 0xd8b5, 
    0xe2b7, 0xc6b7, 0x65b5, 0x45a6, 0xc6a4, 0xdcb8, 0x69ba, 0xdeab, 0xe4ca, 
    0x61b2, 0xa5a7, 0x77c5, 0xf4c0, 0xd9ae, 0xd9c1, 0x77bd, 0xabb4, 0x77b1, 
    0xeab3, 0xc8ba, 0xe6b8, 0xd8b7, 0x41b5, 0xc6ab, 0xdba4, 0xeeaf, 0x57b7, 
    0xc0b6, 0x44c1, 0xc0bd, 0xaec2, 0xd3ac, 0xc4b0, 0x71b4, 0xd7b7, 0xccae, 
    0x45b7, 0xe9ab, 0xc0c1, 0xc7a6, 0xa7b4, 0xf7bd, 0xb2c0, 0xecab, 0xeeb5, 
    0x5ea6, 0xb4b7, 0xacae, 0x7abc, 0x63a5, 0x66b4, 0xe2b1, 0xecb8, 0xa9c2, 
    0x7cb7, 0xedc0, 0xd7b6, 0xd0bf, 0xa3bb, 0xb8c3, 0xa7b8, 0xfca9, 0x42b1, 
    0xeebb, 0xfdb4, 0x56b2, 0xc5c1, 0xa1ac, 0xeba5, 0xf5a4, 0xf2c0, 0xcea9, 
    0x62b4, 0x4ec0, 0x66b3, 0xd7ba, 0xbbc0, 0xa3a7, 0xf2b0, 0xf3c9, 0xeeb7, 
    0x5dbd, 0x6ebf, 0xdfba, 0xd9a6, 0xa7b0, 0xf1b8, 0x45bf, 0xd5c3, 0xfbc2, 
    0x56ae, 0x5ac1, 0x72bd, 0x4ea6, 0xe5cc, 0xc6b4, 0xe8bf, 0x79c4, 0xb0b6, 
    0xcea4, 0xe6ab, 0x65af, 0x56a8, 0x59a7, 0xfab6, 0xc5af, 0xbdc0, 0x4ca4, 
    0xe1af, 0x76a4, 0xaac1, 0xdea7, 0xacbe, 0x75a9, 0xabc9, 0xbdb2, 0xafbe, 
    0xaab1, 0xd9c0, 0x48b1, 0x49b1, 0x70ad, 0x4fb0, 0x4aac, 0xd2a7, 0xdabb, 
    0xb2a7, 0x7ec4, 0xf6ac, 0xc5b9, 0x45cf, 0xa8a7, 0xcea8, 0x61ae, 0x5ba5, 
    0xf3b2, 0x55c0, 0xebb8, 0xd2a5, 0x5bb9, 0xb2b0, 0x5bbd, 0xb2c9, 0x5bac, 
    0x72be, 0xf9b6, 0xe8c4, 0xcaba, 0xedb0, 0x79a6, 0xe0ba, 0xa1b6, 0xceb7, 
    0xddad, 0xd3aa, 0x7dc1, 0x6ca6, 0x70bd, 0xb5d3, 0xcbc0, 0x5aac, 0x50c6, 
    0x50c6, 0x7ab4, 0xdfbe, 0xb2c2, 0xfcbb, 0xc5b0, 0xeeb4, 0xf2af, 0x65c2, 
    0xb3c5, 0xeebd, 0xe2bd, 0xa3a8, 0xe4c1, 0x62bd, 0xf3a5, 0xb7b0, 0xa5c4, 
    0x43bc, 0x5ec0, 0xa5ba, 0x71c2, 0xeebc, 0xd8ab, 0xf8bb, 0xb8ab, 0x4eb1, 
    0xdfbc, 0xbfa6, 0xa6c3, 0xb1bd, 0xd5bc, 0xfabc, 0xbfc1, 0x4ba6, 0xe6c2, 
    0xb0ad, 0xbcbf, 0xd4b4, 0x47c1, 0x4ab5, 0xa6bd, 0xe6a5, 0xa5ad, 0xe5bc, 
    0xbac5, 0x62bc, 0x5ac4, 0xcdc5, 0xc7bb, 0x42c1, 0xaeb9, 0x7db8, 0xbeac, 
    0xa4a8, 0xe5bb, 0xbac3, 0xb1b5, 0xcfb6, 0xd0b1, 0xc3bb, 0xe2c3, 0xfbb8, 
    0x73a5, 0xa2b5, 0xa6b4, 0xb5b1, 0xd2ac, 0xc5d6, 0xf3b5, 0xa5b6, 0x49ba, 
    0x54a7, 0x60b8, 0xdcae, 0x4eaa, 0xb6b1, 0xfbb7, 0xdcba, 0xa1cf, 0xb2b5, 
    0xd1b8, 0x6aa9, 0xd9a7, 0xc7c2, 0xe3aa, 0xc9ac, 0xc9ad, 0xb6a4, 0xceac, 
    0x7cbb, 0xa1a9, 0x79a4, 0xacb5, 0xe7a4, 0xf7aa, 0xb5a4, 0x7aac, 0xccc3, 
    0xf2ba, 0x41c0, 0xc8b6, 0xd4c2, 0x69b6, 0xdae0, 0xcaae, 0x54b8, 0xf1aa, 
    0x75c2, 0xfbae, 0xc9ba, 0x6cab, 0xf0af, 0xb8b9, 0xf4b2, 0xfab7, 0xb9b4, 
    0x48c4, 0xcaa8, 0x61d5, 0xebba, 0x65b8, 0x67b8, 0xaba4, 0xb5c4, 0xbab4, 
    0x56c0, 0x52c0, 0xd2b9, 0x71b7, 0xe8c3, 0x7cae, 0x6ab5, 0x74b9, 0xbab3, 
    0x76c4, 0x62b2, 0xb3ac, 0x7eb5, 0xaab4, 0x73a8, 0xc8aa, 0x68a8, 0xb4ad, 
    0x5ba4, 0x62a8, 0x45a4, 0x73b0, 0x5cb4, 0xcfb1, 0xc2c2, 0xdda6, 0xa4b8, 
    0x53a9, 0x4eb4, 0xb9aa, 0xf9c1, 0xeba9, 0xaeaa, 0x6aaf, 0x7ea9, 0x73be, 
    0xe2b5, 0xbda7, 0x43a9, 0x78af, 0x7cc1, 0x71aa, 0x45bb, 0xdaa9, 0x75d5, 
    0xa8a5, 0xe3a8, 0x5ab6, 0xf5bd, 0xf7bf, 0xd1ad, 0x79a5, 0xdfc4, 0xb2ac, 
    0x40bc, 0xbdae, 0x59c3, 0x53ae, 0xc2ad, 0xb2b2, 0xf7a8, 0x68b8, 0xb1e5, 
    0xccc5, 0xe0a7, 0xb8b1, 0xcfad, 0xefc0, 0xb1c4, 0x4da8, 0x5ab3, 0xb4b5, 
    0xa1a7, 0xdfb5, 0x76b6, 0x78ad, 0x67a7, 0x6dae, 0x54ab, 0xa4b5, 0x43af, 
    0x70b0, 0x40c2, 0xd8b3, 0x40a9, 0x64a5, 0xa3ab, 0x7db6, 0x7bb4, 0xa2b7, 
    0xcdb3, 0x6eb4, 0x5aa5, 0xf4b3, 0xc9b0, 0xa2a7, 0xe5ac, 0xddac, 0x64b1, 
    0x42ba, 0x52c1, 0xaaa6, 0xdca7, 0xaea4, 0xa1aa, 0xd2a6, 0xfeab, 0x4eaf, 
    0x61be, 0x56a9, 0x56ad, 0x5fac, 0xcab4, 0x57bd, 0xfbc1, 0xecac, 0xdfb4, 
    0x79ab, 0x69a5, 0xf7b4, 0x4aa7, 0xe8a8, 0xc8ab, 0xd2bd, 0xd6aa, 0xd9b0, 
    0xc1be, 0xb5c0, 0x7ca7, 0x5ca7, 0xc5aa, 0xa3ae, 0xd5a4, 0xb1b1, 0xe0e1, 
    0x66a4, 0xa9a6, 0x46b1, 0x5cac, 0xfaad, 0x5db8, 0x57ad, 0xc5bb, 0x77ae, 
    0xc7bf, 0x6aa6, 0xb1ab, 0xd8ce, 0xf3b8, 0xe3af, 0xf4b6, 0x5fb8, 0xfbbb, 
    0xd6a7, 0x65bc, 0xdab4, 0x4aa6, 0xa8b5, 0x67a8, 0xd8ae, 0x71c4, 0xb5b2, 
    0x6dc3, 0x70aa, 0xabc1, 0xafb2, 0xc9f5, 0x73bf, 0xaab8, 0xb6ab, 0xedbb, 
    0xc8b3, 0x58f5, 0x5cb7, 0xecbc, 0x5ba9, 0xf8a9, 0xb9ae, 0x78a7, 0x41ac, 
    0x58c2, 0xf8b9, 0xefc1, 0x55a9, 0xd4a9, 0xe2b3, 0xdce3, 0x4bdb, 0xb6bb, 
    0xd5b0, 0xdcb5, 0xd3a8, 0xe0bf, 0xc5c2, 0xfdb0, 0xe6c4, 0x64c4, 0x78c4, 
    0xf1c1, 0xf5c4, 0x69c4, 0xc2f8, 0xf3c5, 0xfdc4, 0x69c3, 0x6cc6, 0xeac4, 
    0xddc0, 0x77b2, 0x7db7, 0x54af, 0x59b4, 0xa6ad, 0xd4ae, 0xf6ae, 0xb4bc, 
    0xd2b3, 0x63a8, 0xd1a6, 0xd0a8, 0xbeab, 0x54b9, 0x4faf, 0xfce5, 0xc7b0, 
    0xd6bc, 0x70b9, 0x4ac5, 0xa2c1, 0x55bd, 0xd6b2, 0x77c0, 0x53c2, 0xddbe, 
    0xd8a6, 0xfec3, 0x5cb2, 0xb1d9, 0xabb7, 0x4ea7, 0xf9cd, 0xf9b1, 0x70b2, 
    0xa4be, 0xcced, 0x57af, 0xc3d6, 0x76ba, 0x7ab2, 0xf5a7, 0xbda8, 0x55c3, 
    0xa7c2, 0xfab2, 0xefaf, 0x4fa6, 0xdfae, 0x52c4, 0x46bc, 0x79c0, 0x74c4, 
    0xfabe, 0x51a7, 0xf4e0, 0xd2a8, 0x57ab, 0x67b5, 0xdfa5, 0xc9b2, 0x77c3, 
    0xf5c1, 0x4fa4, 0xfebc, 0xf9ad, 0xc7ad, 0x70c1, 0xacbd, 0x73b3, 0x49c5, 
    0x47b7, 0xb0cc, 0xa7ba, 0xa9a9, 0xc4c0, 0x79c1, 0xecc3, 0xcac5, 0xd2b7, 
    0x6dbd, 0xb3c2, 0x44b2, 0xe7b1, 0x64b8, 0x7da8, 0xe2a8, 0xf8bd, 0x71b6, 
    0xbdb4, 0x47ab, 0xccbd, 0xbabc, 0xe1b2, 0xb1b9, 0xf8c0, 0x52bf, 0xe9b9, 
    0xf1bf, 0xe3bc, 0x46a4, 0xe6e1, 0xeff4, 0xf9b9, 0xc6ae, 0x43a6, 0xf5b5, 
    0x50af, 0x48a6, 0x79c2, 0x59b5, 0x4caa, 0x43c1, 0x4dc0, 0x7bc1, 0x46be, 
    0xecc5, 0x4fb2, 0xfebb, 0xeeb8, 0x5ba7, 0xf0a9, 0xc2ac, 0xd9b5, 0x73b9, 
    0xd6c4, 0x61b9, 0x44a7, 0xdcb2, 0xe2ad, 0x46c6, 0xaeb3, 0x64cc, 0xe2bb, 
    0x74a5, 0x4fa5, 0xc8b7, 0x5baf, 0x68ba, 0xb8b2, 0x48c3, 0x64af, 0x42bc, 
    0x46bd, 0x79ac, 0x68ac, 0xbba4, 0x73c0, 0xa4c5, 0x56c4, 0xa2c5, 0x4bc1, 
    0xa9b6, 0x62c3, 0x6cc3, 0xf7c3, 0xd3bc, 0xfab0, 0x4fba, 0x4dc1, 0x7cba, 
    0xaead, 0xaac4, 0x63bf, 0x60c6, 0x66c3, 0x6cc4, 0xdbbe, 0xbfb3, 0xb8b8, 
    0x7cbe, 0x53c4, 0x4cb8, 0x53c5, 0xf4b8, 0xefb8, 0xc0b3, 0xf0e9, 0x53b8, 
    0xfdbf, 0xb0b3, 0xaebc, 0x6ac6, 0x66a7, 0x54be, 0x51ab, 0xc8ae, 0x69bc, 
    0xf0b9, 0x5cc1, 0x7bbc, 0xe2b4, 0xdfab, 0x76b2, 0x6fc2, 0xf1ba, 0x72c5, 
    0xcbc5, 0x70c5, 0x65c6, 0x5aa7, 0xc3b6, 0xb0b1, 0xa4b2, 0xc1b1, 0xfcbd, 
    0xdbad, 0xdaa8, 0x5fb2, 0xfaba, 0xd7bd, 0xdac5, 0xb3c1, 0xb9c3, 0xdec5, 
    0x72c6, 0x59c6, 0x5bc5, 0x72bb, 0xa8b8, 0xa5ac, 0x64c0, 0xb8b5, 0xfdb6, 
    0xc2b3, 0xbfba, 0x58bd, 0xc2bf, 0xa8b0, 0x7cbd, 0xc0b9, 0xdcb6, 0x49ae, 
    0x52b6, 0xc1b3, 0xe6bd, 0xdac1, 0xdfaf, 0x66bf, 0x43c4, 0x5ac6, 0xa1ba, 
    0xafbd, 0xd2b0, 0x43ba, 0xa9ba, 0xe3f0, 0x7ea8, 0xedaf, 0xbcaa, 0x5daa, 
    0xa3a6, 0xf5b2, 0xdfbf, 0x54ad, 0xe3c1, 0xf2a4, 0xd9a5, 0x67b9, 0x66a5, 
    0x5aad, 0x5fab, 0x55b4, 0xaabb, 0x54b6, 0x5ca4, 0xb4aa, 0x54aa, 0xf6b1, 
    0x40a1, 0x60be, 0xd1b7, 0x53a8, 0xdcac, 0x43b4, 0xe2c1, 0x43a8, 0xfcac, 
    0x4eac, 0x4bb4, 0x66a9, 0x41b4, 0xf9aa, 0x65b4, 0xccad, 0xdeb5, 0x58bb, 
    0x63c2, 0xf9b7, 0xf8bf, 0x72b2, 0xdab9, 0x73a9, 0xa9d6, 0xc5ee, 0xfbc3, 
    0x53c1, 0x67b0, 0xbcc1, 0xb1c0, 0xcca6, 0xb5af, 0x56b3, 0x63aa, 0x65bb, 
    0x4bb1, 0xadbe, 0xd6b4, 0x76af, 0xf8ba, 0xc3b0, 0x4ba7, 0x6aab, 0x59ae, 
    0x71bd, 0xb1ad, 0x5dad, 0x79b4, 0xcbba, 0xc6c2, 0xedac, 0xf9b4, 0x71bc, 
    0xaea7, 0xb0bd, 0xc0b7, 0xc1a5, 0xd7a9, 0xd7a5, 0xd3b1, 0xa7bc, 0xd4bb, 
    0xfaa9, 0xc1bf, 0xefbb, 0xcabb, 0x57a6, 0x52a9, 0xd5c2, 0x4eba, 0xb1bc, 
    0xa8c4, 0xd2bc, 0xa4bd, 0x69bf, 0xafbc, 0x5dc5, 0xd9a9, 0xbda5, 0xf6b2, 
    0xa5be, 0x71c0, 0x6aaa, 0x7aba, 0xe6b9, 0xafad, 0xd1bf, 0xc8a6, 0x59ac, 
    0xe6a9, 0x64a8, 0x61af, 0x69a9, 0xc0a5, 0xd3b9, 0xc7bc, 0xf5b9, 0xd2b6, 
    0x7dbc, 0xeca4, 0xd8a5, 0xfcb7, 0xaaaa, 0x70bf, 0xb3ae, 0xfead, 0x6fa7, 
    0x75b6, 0xbaa8, 0x52ae, 0xc7af, 0xbba6, 0x44a4, 0xa4a5, 0x40ad, 0x60a9, 
    0x6eab, 0x6ba8, 0xf8c3, 0x6ec5, 0xb8bc, 0xa3b8, 0x6fb4, 0x78be, 0xefd5, 
    0x4fa9, 0x6bbe, 0xbaa4, 0xe0b9, 0xe0af, 0x67a9, 0x4fc0, 0xd9ad, 0x64aa, 
    0xa7a5, 0xc0c0, 0x41a7, 0xceb0, 0xb0bf, 0x66b0, 0xc4b7, 0x59e7, 0xe0a9, 
    0x7ea6, 0x56bd, 0x5bc2, 0xc9b1, 0xc0a9, 0x51ae, 0x43c6, 0xbeb3, 0xbfa7, 
    0xbaae, 0xbfc2, 0x5ec4, 0xbfef, 0x68c6, 0xecc2, 0x49af, 0x7ab1, 0x66c2, 
    0xf1c0, 0xaebe, 0x72c9, 0xbec0, 0x46cd, 0xfba4, 0xe1a7, 0x73b6, 0xc3af, 
    0x77c1, 0x40bf, 0x41b9, 0xcba7, 0xa3a5, 0x56a7, 0xe3ab, 0x6ba4, 0x78b7, 
    0x68ad, 0xc4ba, 0xbfae, 0xb6c0, 0x7ac4, 0xd5bf, 0x40ae, 0xdabc, 0xc3c5, 
    0xdebc, 0xc2c3, 0xc3b9, 0xb8b0, 0x78e2, 0xd4b0, 0x77ad, 0xa6aa, 0xaca9, 
    0xc8a9, 0x5db5, 0xe7a9, 0xc6b1, 0x50b5, 0x72b1, 0xfbb4, 0xa3ac, 0x6bc3, 
    0xefbc, 0x4cbd, 0x59bd, 0xdfac, 0x60af, 0x50a7, 0x71ab, 0xe3a5, 0x65c3, 
    0xc7ae, 0xe5ea, 0x44ad, 0xdfa9, 0x48a9, 0x53a7, 0xb6ac, 0x54b3, 0x5db6, 
    0x77aa, 0x41a9, 0x46ad, 0xf6b0, 0x70bb, 0xdfbd, 0xadb3, 0x74b0, 0xd8a8, 
    0x4ba8, 0x51bc, 0xd6ac, 0x79af, 0xe1a9, 0x69b2, 0xeabc, 0x5eb4, 0xb4bd, 
    0xd7b4, 0x4eb8, 0x4fc1, 0xb1bf, 0x42aa, 0x50c4, 0xb7b1, 0x49b8, 0xf2cb, 
    0xeccf, 0x52c5, 0xe5a7, 0xdca9, 0x41bc, 0x5cb5, 0x73ac, 0xe0b0, 0xcab5, 
    0x68af, 0xd6a5, 0xc7a4, 0x6cb5, 0xf7bb, 0xbea7, 0xb4c4, 0x67bd, 0xbeb0, 
    0xf9a4, 0x46c4, 0xc6c4, 0x7dba, 0x5dbf, 0xbcb2, 0x4aba, 0x68bf, 0xf7ab, 
    0x57c0, 0x68b3, 0x7eab, 0x75b8, 0xe2a5, 0x57a9, 0x6dd0, 0xd3b5, 0xada5, 
    0xccbe, 0x7eb2, 0xfbb5, 0xccab, 0x59a9, 0xe2bc, 0xe1bb, 0x43b1, 0x7daf, 
    0x7abe, 0xa2ad, 0xcbb2, 0xe5ad, 0xb7a5, 0x51be, 0xb2a4, 0x40b3, 0xb2b8, 
    0xd0b5, 0x5abb, 0x48ae, 0xb5a6, 0x45ae, 0xb6b4, 0xfaae, 0xd0c3, 0x6ec3, 
    0x72c2, 0xc1b4, 0xdbb4, 0xe1d1, 0xadb1, 0x64a9, 0x43a4, 0xe2d0, 0xa3ba, 
    0x6dac, 0x50cb, 0xe4a8, 0xd1b4, 0x5fa9, 0x5baa, 0xa5b2, 0x54b1, 0xc0c2, 
    0xf4bb, 0x58ba, 0xe8ac, 0xc2aa, 0x4dc3, 0x5fb0, 0x5ab0, 0x5ea4, 0xf8a5, 
    0xd2b1, 0xb4ab, 0xe4ac, 0xb9be, 0x61c9, 0xb4a8, 0xf3b1, 0x54a8, 0x5faa, 
    0x57b0, 0x74d5, 0xeaab, 0xa2ac, 0x6fb2, 0xe7c9, 0x40a1, 0x5db9, 0x64a4, 
    0x45be, 0xb1c3, 0x61a5, 0xbec1, 0xaeb0, 0x72c0, 0xfabf, 0x58b9, 0x65ab, 
    0xe7bc, 0xbabb, 0x4cb2, 0xfec4, 0xd5b9, 0x4fb4, 0xeda4, 0x70ba, 0x6aba, 
    0xe6b6, 0xc4b5, 0xcaaa, 0xf0c0, 0xa5c1, 0x6ab1, 0x6db7, 0xf5be, 0xeac1, 
    0x56ba, 0xa8ae, 0xf4be, 0x40c1, 0xecb3, 0xb4b9, 0xa9a5, 0x54c0, 0xc0bc, 
    0xbcc2, 0x6bae, 0x4eab, 0xacc2, 0xc1a4, 0x58ad, 0x42a5, 0xc4a9, 0xd1c5, 
    0xdcb4, 0x49ab, 0xcbbf, 0xb3af, 0x5eb5, 0xd4b6, 0xe0aa, 0xe0be, 0x56b8, 
    0xecb9, 0x47a8, 0x43ab, 0xb4bb, 0x42b2, 0xc9b6, 0xebad, 0x4db2, 0xbac0, 
    0xb8b4, 0xe6d9, 0xa1b1, 0xbcb3, 0xd0bd, 0x79bc, 0xa3c3, 0x61bd, 0xeeac, 
    0x43a5, 0xf4aa, 0x79b2, 0x44a8, 0x7da5, 0xa9ad, 0x73aa, 0xcdc1, 0xcfb0, 
    0x49b3, 0xb1a6, 0xdfc2, 0x7da9, 0x58c5, 0xebb4, 0xfaa8, 0xf9b0, 0x54c6, 
    0xecbd, 0x68a5, 0xe9b0, 0xaff9, 0x76c5, 0xc7ee, 0x75ac, 0xfea5, 0xacb2, 
    0xb1ae, 0xfca4, 0xe9a8, 0x55c4, 0xcaaf, 0x50cd, 0x61bf, 0x6fab, 0x4ec4, 
    0x65ba, 0xccda, 0xb6b3, 0xc8b8, 0x73b8, 0x4db5, 0x55bf, 0x54a5, 0x56ac, 
    0x7bc5, 0x5bc4, 0x63c4, 0x57c4, 0xfdc5, 0xc7c4, 0xf0ca, 0xb6c2, 0x53b7, 
    0xf6bc, 0xd0a4, 0xafa4, 0x48a4, 0xd4a7, 0xb4b6, 0xf4a5, 0x7bbb, 0x62a4, 
    0xb3a7, 0xf7ac, 0xb5a5, 0xb4a4, 0xe9a4, 0xa5a6, 0xf1af, 0x54bb, 0x61ba, 
    0xc4bf, 0xb2ba, 0xbbb7, 0x65ae, 0xb3b5, 0xbea4, 0x7cb4, 0x58ac, 0xd7a6, 
    0xf8af, 0xafc4, 0xa7be, 0xa9c0, 0x70a6, 0x64b0, 0xc5a8, 0xbca6, 0x4aa4, 
    0xc8bf, 0x6eb3, 0xbfa8, 0xb6bf, 0xe7b7, 0x55be, 0x7cb6, 0xedbc, 0x59ad, 
    0x7aae, 0xbbbc, 0x73cf, 0xc4c2, 0x7cb8, 0xcec4, 0xebb6, 0xc9c1, 0x54a4, 
    0xd4cc, 0xcab3, 0xb2b4, 0xe1ae, 0xdab6, 0xe0b3, 0x6bb7, 0xccc4, 0xbdb1, 
    0x41b7, 0xe6b7, 0xe2a6, 0xdfc0, 0xcbb4, 0xacb9, 0xefb2, 0xe2ac, 0xfeb1, 
    0x62ab, 0x46a8, 0xbdaf, 0xccb6, 0xa3d4, 0xd9b7, 0x7abf, 0xcdae, 0xc0ac, 
    0x73d0, 0xfca7, 0x73a4, 0x52a7, 0xb4ba, 0x6dad, 0x7bb0, 0xa2b0, 0xd5be, 
    0xbac4, 0xafbf, 0xbdb5, 0xc2a6, 0xb0ae, 0xb5c2, 0x40a1, 0xcbb6, 0xd3b0, 
    0xe0bd, 0xceae, 0x57a4, 0x7ca9, 0x6ebb, 0xe9b1, 0xb4ae, 0x79b5, 0x4ebf, 
    0xa2a8, 0x63a4, 0xe0bb, 0xd6a4, 0xefad, 0xf2aa, 0xd0b2, 0xf8b0, 0xadbb, 
    0x44b3, 0xdea6, 0xd9aa, 0x6ab3, 0xe1c4, 0x67ae, 0xe0c4, 0x41af, 0xc0aa, 
    0x5db3, 0x7eaf, 0xd3a5, 0x44a9, 0xf9a6, 0xada8, 0x60b2, 0x57ae, 0xd4b2, 
    0xabaf, 0x48a8, 0x66bc, 0x54c2, 0xc6ac, 0xc7b5, 0x56b7, 0xafba, 0x6ec1, 
    0xcda5, 0x63b5, 0xb9ac, 0xc9a4, 0xb7c3, 0xd9ac, 0xb1b2, 0xd1b3, 0x60d0, 
    0x6fc9, 0x76ae, 0xa2a5, 0xe0b7, 0x49ac, 0xe3c0, 0xd6b8, 0x72a4, 0xebaa, 
    0x51a4, 0xdba5, 0x42ac, 0xc9ae, 0xb0a4, 0xb9ad, 0x6bbb, 0xeab9, 0xd1c3, 
    0x76a5, 0xdaa5, 0xcfa8, 0xcbab, 0x70be, 0x6ca9, 0xa1a6, 0xdca5, 0x68a4, 
    0x40a5, 0x55ac, 0xc6a8, 0xf8ab, 0x7dbb, 0x75b3, 0xd5b6, 0x4fac, 0xddb6, 
    0xbdbe, 0xecd3, 0x4ba5, 0xcda8, 0xc0c4, 0xa2b9, 0xf3a4, 0xaba5, 0xeeab, 
    0xc7ab, 0xf8b5, 0xd5b8, 0xaca6, 0xe2a4, 0xbaad, 0x75a6, 0xd8b9, 0xc2b1, 
    0xe2b0, 0xfca8, 0x47bd, 0x7ec3, 0xadbd, 0xcfbc, 0xdeae, 0xedae, 0xe7a7, 
    0xe9bf, 0xfba8, 0xceb5, 0x51b2, 0xa8b2, 0xd1ae, 0xabc5, 0x45b1, 0xf4bc, 
    0xa6c1, 0xbbb4, 0xc6c0, 0x70b8, 0xbeb8, 0xc1b6, 0xabb9, 0xddc4, 0x4eb3, 
    0x7aad, 0xf0be, 0xf4a7, 0xa7a6, 0xddbd, 0xd6b9, 0x66b1, 0xc6bc, 0xa4ba, 
    0xa4ae, 0xeaa8, 0x41ad, 0x4cba, 0x49b0, 0xcfa5, 0xd3ab, 0xeaae, 0x43ac, 
    0xf7c1, 0xf9c2, 0x6eb2, 0xd6bd, 0xf4a4, 0xceba, 0x7cb5, 0x6da7, 0xfec0, 
    0xb6b6, 0xcfb5, 0xa1bb, 0xd3ba, 0xd2ae, 0x7bc3, 0xb5b4, 0xb9bc, 0x52bc, 
    0xe4ab, 0x70a8, 0x71a5, 0xb7b5, 0xbaa6, 0x76b8, 0x78a6, 0xe0b6, 0x7ca5, 
    0xf8a6, 0xfca6, 0x7db9, 0x78a4, 0x51aa, 0x71c1, 0xa3bc, 0x7cb9, 0x65b0, 
    0xbaa7, 0x5eb3, 0x77bb, 0x6ab7, 0xb4bf, 0x5dc2, 0xc2b9, 0xacc4, 0x70b6, 
    0x55ab, 0xc0af, 0x74b3, 0xafb5, 0xf4e0, 0xecb6, 0xb9b7, 0x4ab1, 0x44b6, 
    0xc2b5, 0xc4bb, 0x5bbb, 0xe2ba, 0xf6c1, 0xa6b6, 0x48c0, 0x6bb8, 0xe8c5, 
    0x48b8, 0xb3b7, 0x4ac1, 0x45b9, 0x47c0, 0xa9af, 0x5dae, 0x6cb7, 0xabb5, 
    0x62bb, 0xf4b1, 0xf6ad, 0x59c1, 0xbeba, 0xc1af, 0xeac2, 0xd2a9, 0xf2b6, 
    0x4ca5, 0xa6a5, 0x6fa6, 0xf0b6, 0xa1c3, 0xd8be, 0xcfc1, 0xf1bd, 0x4cad, 
    0x61ad, 0xefa9, 0x78a5, 0xf5ae, 0x40a1, 0xd3a4, 0x41ba, 0x4fa8, 0x7ea7, 
    0x75c5, 0x67b3, 0xf5c5, 0x79c5, 0xc2be, 0xc8c0, 0xf0b7, 0xe6bc, 0xd3c3, 
    0xcdbd, 0x5aa9, 0xe0b4, 0x52b3, 0xd2ba, 0xb4b1, 0xc4b9, 0xb4ac, 0xf6b4, 
    0xedb6, 0x65b7, 0xf3b0, 0xc5b4, 0xa3bd, 0xf0ad, 0x7dbf, 0xd5ad, 0xf6bd, 
    0x49b2, 0xebbd, 0x53bf, 0xc7b1, 0xdcc0, 0xcab7, 0x6cb8, 0xe5b5, 0xe7ae, 
    0x6bb0, 0x5eb2, 0xb3b3, 0x51b0, 0x4dae, 0x53af, 0xc3c3, 0xcbc4, 0x6baf, 
    0xc3c1, 0xe8b1, 0xe7ad, 0xf0bd, 0x4fbe, 0xa3b4, 0x44c3, 0xe1bf, 0xdab3, 
    0x5eca, 0xc0b4, 0xa1c0, 0xa7b1, 0xf7ae, 0x63ab, 0x50b1, 0xd1a4, 0x4bb2, 
    0xf1b6, 0xd0a5, 0xa2b2, 0xefab, 0x51bb, 0xc9b5, 0x44ac, 0xf8b1, 0x7cad, 
    0xb7b2, 0xf5b8, 0x4bb6, 0x4bc5, 0xaba9, 0x55c6, 0x76a7, 0x4cd6, 0xc5a5, 
    0xcaa7, 0xb1b0, 0x46ab, 0x78ae, 0xbcae, 0xa5b8, 0x71b3, 0xe4ae, 0xace0, 
    0xfbc0, 0x50a6, 0xc9bb, 0xcda7, 0xa3b5, 0xedb1, 0xb6d1, 0xa9b5, 0xceb2, 
    0x68b5, 0xbdb0, 0xeba7, 0x59c0, 0x7ab3, 0x59a5, 0x72a8, 0xf0ac, 0xcfb9, 
    0x7bae, 0x7eb3, 0x5cd2, 0x4fb1, 0x67a4, 0x52a6, 0xdfa8, 0xf8b4, 0xceb9, 
    0xc0b1, 0x5ac0, 0x4cbb, 0xc0b8, 0xc6bf, 0x68b0, 0x5da7, 0xd9a4, 0x76c1, 
    0xeca9, 0xaba6, 0xe6b2, 0x6bc0, 0xfbaa, 0xa4b9, 0x6dbe, 0xf2be, 0xb4a7, 
    0xdda9, 0xe8b3, 0xf5ab, 0x7aab, 0xecb5, 0x71cf, 0xbdab, 0xcba5, 0xfbc4, 
    0x6eac, 0x7ea5, 0xdcbd, 0x73c5, 0x57c6, 0xb1aa, 0x78b9, 0x59a4, 0x4ad6, 
    0xb9a7, 0x4ab8, 0xbeae, 0xdfb1, 0x70b5, 0x7bb1, 0x7ba9, 0xfbb0, 0x45c9, 
    0xc3b5, 0x4ca8, 0xfda4, 0x60a4, 0x50aa, 0x49ca, 0xb9a9, 0xf4a9, 0xe6b1, 
    0xd1a7, 0x6ba6, 0xc2ab, 0xdec4, 0x4cb7, 0x4da6, 0xb3ad, 0x48b9, 0xe9ae, 
    0xf2b3, 0xdfb0, 0xa9b1, 0xb0ac, 0xe5c0, 0xfbba, 0xabb8, 0xe4b5, 0x65a9, 
    0xb6b0, 0xb0b0, 0xc0a7, 0x6ebd, 0xbca5, 0xabbd, 0xfda8, 0xc8ac, 0x47ad, 
    0xdeb3, 0x51c3, 0xeca6, 0xf4b4, 0xd7bf, 0x4cb1, 0xa2bc, 0xc3bd, 0x45bd, 
    0xc5b7, 0x41b0, 0xe5a4, 0x44bb, 0xbeaf, 0x6ba7, 0xadc3, 0xbfaf, 0xddb0, 
    0xe4b6, 0xceaf, 0xd8cf, 0xe2be, 0xbdbd, 0xf5b4, 0xdbba, 0xdaa7, 0x57ba, 
    0xd7aa, 0xa4b4, 0x55a8, 0xc5a7, 0xe3b6, 0xebc2, 0x51af, 0xc3a6, 0x7abb, 
    0xceab, 0x4cb5, 0xbebf, 0xefb1, 0x5ea7, 0x64a7, 0xf0a4, 0x5aaa, 0xada4, 
    0xb8ae, 0xc8a4, 0x52bb, 0xeea5, 0x56ab, 0xf5b6, 0xb3a5, 0xfac3, 0xe0b1, 
    0xabaa, 0xc5a4, 0xc8b0, 0xa9ae, 0x7ebb, 0xf5a9, 0xb3ba, 0x52aa, 0xe8a6, 
    0xb4d6, 0xbfaa, 0xb7b4, 0x48bc, 0x6ca7, 0xfcbf, 0xebc4, 0x7db5, 0xa7ae, 
    0xc6a7, 0x78b1, 0xa5bd, 0x69a4, 0xa4b1, 0xb6ba, 0x6db2, 0xcbb7, 0xc1a6, 
    0x52b5, 0xcac0, 0xa7c5, 0x75ae, 0xdfb2, 0x40b7, 0xdfb3, 0xd1bb, 0x7eac, 
    0x74a8, 0xd8bb, 0xb8c0, 0xd3b2, 0x4dbd, 0xbcbd, 0x58a7, 0xf8c1, 0xd2c1, 
    0x76b7, 0x6cae, 0x4cab, 0x55af, 0x55a4, 0x48b7, 0x4cae, 0x7ec0, 0xc8b1, 
    0x40a1, 0xfda5, 0x50a5, 0x41c2, 0xd6c5, 0x77ab, 0xe5bd, 0xcebb, 0xecb2, 
    0x7eb6, 0x43b2, 0xb6a9, 0xfbb6, 0xe3c5, 0x49c0, 0x7bb2, 0x6dc4, 0xa4bf, 
    0xa2b8, 0x60c0, 0x43db, 0xcbbe, 0xb4b3, 0xadad, 0x75bd, 0xdbac, 0x5bb4, 
    0x5ec6, 0xbbad, 0x63bd, 0xb8c1, 0xf0b4, 0x6db6, 0xbeb5, 0xbbb2, 0xd4b8, 
    0x51b7, 0x54c5, 0xc9a8, 0xb5b6, 0xd1ab, 0xf3be, 0xb3b9, 0x56a6, 0x48b6, 
    0xbdbf, 0x76b5, 0x5dbe, 0x64ab, 0xfdad, 0xdbc4, 0x50be, 0xf8ae, 0x64ae, 
    0x63b2, 0xe5be, 0x70a4, 0xb5a7, 0xd5ae, 0x76a8, 0x53bc, 0xbaaf, 0xc4ae, 
    0xa4b7, 0xc7a8, 0xb2b7, 0xb2e7, 0x63be, 0xf3a8, 0xb5ae, 0xe2c4, 0xb8a8, 
    0xd7b1, 0xd9af, 0xd3bf, 0x67bc, 0xf1b1, 0xf8a8, 0xc9c3, 0xd3be, 0x6eaa, 
    0x6dc2, 0xc2c1, 0x68ae, 0x7ec1, 0xe4aa, 0x4ebe, 0x59aa, 0xafa8, 0x73b7, 
    0xe3ca, 0xdfa4, 0x48ab, 0xb5f4, 0x50ac, 0x7bb8, 0x56b5, 0x69b4, 0xb3bf, 
    0x44a6, 0xacab, 0xcea7, 0xb7a8, 0xe6a6, 0xf4bf, 0xafa9, 0xf6a7, 0xcaa9, 
    0x6da9, 0x53a5, 0xbfa4, 0xddaf, 0x49a6, 0xa4ac, 0xafb6, 0xb5ba, 0xf0a5, 
    0xd7ad, 0xdbb2, 0xb4a6, 0xe5b6, 0xc3c4, 0x71a8, 0x53b3, 0xb8c2, 0x56bc, 
    0xa6a6, 0xddbb, 0xeab5, 0x4ebc, 0xb7b6, 0x7dae, 0x5cb3, 0x57bb, 0xa4b3, 
    0xd4b1, 0xb0a6, 0xc7a7, 0x62af, 0xf2ab, 0xb6b5, 0x42b4, 0xfcba, 0xf2c4, 
    0x61b0, 0xd9b3, 0xc5ab, 0x61c4, 0xdbb1, 0xc8a5, 0xefbf, 0x7ec5, 0x74af, 
    0xbab5, 0x75b9, 0xa7c1, 0xc7be, 0xdea5, 0xb7b3, 0xe5a6, 0xd4b3, 0xc0e2, 
    0x60b4, 0xafa6, 0xdfb8, 0x4db4, 0xa5b9, 0xb5a8, 0xeeae, 0xc4a6, 0x56b0, 
    0x54b0, 0xb9bb, 0xb3a8, 0xa3c0, 0xe3a9, 0x7ebe, 0x6ec0, 0x72a7, 0x58a4, 
    0xdeaa, 0xfaa4, 0x48b0, 0x56b1, 0xc5b8, 0x50b2, 0xaeb6, 0xd7b0, 0xc8a8, 
    0x59b3, 0x6ab2, 0x7cab, 0x49ec, 0xcfb7, 0x54b2, 0x51c6, 0x59c4, 0xe3ac, 
    0xe9e3, 0xa5a9, 0xb5a9, 0xa5a8, 0x43c3, 0x46c0, 0xa2aa, 0x75aa, 0x61a9, 
    0xbbb1, 0xb4b2, 0x6cad, 0x74ba, 0x41c6, 0xf7b3, 0x50bf, 0xbdb9, 0x78b5, 
    0xadb6, 0xf1ad, 0xdbab, 0x4bb5, 0x62ae, 0xcebf, 0xe7c5, 0x6fac, 0xa1a5, 
    0x6dc0, 0xb1af, 0xa8b7, 0xadb4, 0xcba8, 0xc5ba, 0xcfa6, 0x76ac, 0xa7b6, 
    0xf1ae, 0xf5a5, 0x7dd6, 0x69be, 0xcbbc, 0x79ba, 0xdcc1, 0x79b8, 0xafa7, 
    0xbdba, 0x6eb7, 0xf3b3, 0xbbbb, 0x60bd, 0xc1c1, 0xc0ab, 0x72ab, 0xe6af, 
    0xc4c3, 0x6ead, 0xa3c4, 0xa6b7, 0x4fbc, 0x43ad, 0xddb7, 0xa5b3, 0x4da7, 
    0x5da4, 0xb6ad, 0xb3b1, 0x7eb7, 0xadb8, 0xb2a6, 0xc5b5, 0x5da9, 0x47b2, 
    0x40a4, 0xfcb3, 0xe5c2, 0xa5b4, 0x76e4, 0xcca8, 0xeca5, 0xe7a6, 0x5bc0, 
    0x69a6, 0xf2bf, 0xbeb2, 0xf6bb, 0xd8af, 0xc3ba, 0x5ea8, 0x79a9, 0xbcab, 
    0x55c2, 0xc8b4, 0xc6c3, 0xcaad, 0x77a4, 0x41a4, 0x6fa8, 0x48a5, 0xc0c3, 
    0xeda7, 0xf6a9, 0xb6a8, 0x7aa6, 0xf5bb, 0xd0a7, 0x72c1, 0x68b6, 0x77b8, 
    0xccac, 0xe7a5, 0xc7b8, 0x4eb7, 0xddbc, 0xd0be, 0x71b8, 0x71af, 0xb8b7, 
    0xdab8, 0xb3c4, 0xcbbd, 0xb6c4, 0xddc9, 0x6cc1, 0xddb2, 0xb6c3, 0xf4af, 
    0xaebd, 0x5da6, 0xefae, 0xb5ad, 0xb1b3, 0xc3ab, 0x75a7, 0xc8bb, 0x5db2, 
    0x47b1, 0xbcb6, 0xa8a4, 0xdea4, 0xf4c1, 0x4ca6, 0x5ead, 0xe5c4, 0xa6c0, 
    0x4ec6, 0xb3c0, 0xd5c5, 0xfcbc, 0xc3bf, 0xe7c0, 0xb7ba, 0xc7c3, 0xefaa, 
    0xb9c4, 0xd5ac, 0x76bc, 0x6fbf, 0x77b5, 0x4dac, 0xe9b3, 0xd6be, 0xfea6, 
    0x73c1, 0xeaf7, 0x65b1, 0x6cb9, 0x5ce4, 0xbab8, 0xfab5, 0x61aa, 0x46af, 
    0xc3a5, 0x76b1, 0x69ab, 0xcea5, 0xd5ab, 0xacc9, 0x79b1, 0x7ebc, 0xd7a4, 
    0xd1a5, 0x6cb6, 0x5cb9, 0x53b5, 0x6faa, 0xe5b4, 0xbba8, 0xb3a6, 0xcda4, 
    0x6ba5, 0xf6a6, 0x56b9, 0xa4bb, 0x53a4, 0xaea5, 0xb1a8, 0x4ab2, 0x5fa4, 
    0xbbaa, 0xaeb7, 0xb7b8, 0x4db7, 0xd6c1, 0x45a7, 0x5cab, 0x4fb9, 0xbdb3, 
    0x72b4, 0xfcb4, 0xaeba, 0xa8b6, 0xa9a4, 0x54ae, 0x42ab, 0x4fc9, 0xacc0, 
    0xeaac, 0x74a6, 0x79bb, 0xd0a6, 0xc9a5, 0xecb0, 0xa1a8, 0xa7ad, 0x53a6, 
    0x4ab9, 0xebb3, 0x6eae, 0x73b1, 0x55b7, 0xfdb1, 0xbbba, 0x7ca8, 0x41c5, 
    0x44af, 0x4ab4, 0xceb8, 0x77b9, 0xddbf, 0xbfb6, 0x70c0, 0x57b2, 0xdead, 
    0xb8a4, 0xaeab, 0x4bb0, 0xecad, 0xa9b4, 0xd5c1, 0xe9b6, 0xfbad, 0xeab6, 
    0xe1b7, 0xbdb7, 0x74bd, 0xb7bb, 0x62ad, 0x40ba, 0xe8ab, 0x7cb0, 0xeaa4, 
    0xf9ac, 0x56b6, 0x44c5, 0x5fc6, 0xa8a9, 0x66b8, 0xeba4, 0xaeae, 0x5cbe, 
    0xd0af, 0xaaa4, 0xa8e0, 0xc3a4, 0x6bb9, 0xb9a4, 0x42b9, 0xadc4, 0xdfc1, 
    0x77b7, 0xfdc3, 0xa5a5, 0x60a5, 0x7baf, 0xf8c2, 0xe2ae, 0x76ab, 0x61a8, 
    0x5fae, 0xfcb8, 0x41a6, 0x62a6, 0xa5ab, 0xe3f6, 0xc8bc, 0xd9c3, 0x42c5, 
    0xa6c5, 0xaeb8, 0x44be, 0x56c1, 0x77c6, 0xa6c4, 0xc7b4, 0xada6, 0xfebe, 
    0x44b0, 0xbcc4, 0xb8be, 0x79b3, 0x6da8, 0x5fa8, 0xeac0, 0x64b3, 0xdcbe, 
    0x68ab, 0x41bf, 0xe9b8, 0xe7ab, 0x57bc, 0xa8bc, 0xbfb4, 0xd8c3, 0xe3a4, 
    0xe5b3, 0xedb4, 0xbea5, 0xeeaa, 0xd4ee, 0x68b9, 0x77af, 0x5dac, 0x5eba, 
    0x51a9, 0x45a5, 0xb5ac, 0x42b6, 0x4bba, 0x4ec2, 0x76a6, 0xb6af, 0xc5b6, 
    0xebb9, 0xa4c2, 0xd6c0, 0xe2b8, 0xdfd6, 0x67aa, 0xf8b7, 0xd9b1, 0xd3c1, 
    0xf1b9, 0x69ae, 0xd9c5, 0xccb4, 0x65a5, 0xd4be, 0xb8af, 0xefb4, 0xecba, 
    0xccbc, 0xb9b3, 0xfcb9, 0x73ba, 0x69b1, 0x78b4, 0xa6ba, 0xfaa7, 0x56a4, 
    0x62b1, 0xe3bd, 0x4da5, 0xc8b5, 0x60bf, 0xd9bb, 0xdba9, 0x4cac, 0xe4a7, 
    0x68aa, 0xafbb, 0xd3b7, 0x6eb8, 0xfca5, 0x46bb, 0x6ca5, 0x42be, 0xe9a7, 
    0xf5ad, 0x68ee, 0xe1c2, 0xccaa, 0xe3eb, 0xa9bd, 0x6fb3, 0xfdae, 0xc3ac, 
    0x72b7, 0x75af, 0xc2ba, 0x7aaf, 0xb2bf, 0x73ad, 0x77b0, 0xbbb0, 0x45aa, 
    0x6caf, 0x45b6, 0x5fbe, 0xb6ae, 0xedc2, 0x7db0, 0x5dbb, 0xc3b1, 0x43b8, 
    0xbaa9, 0x75b2, 0xa7aa, 0xc3a9, 0xe3be, 0x40ac, 0xbfa5, 0x46ac, 0x56b4, 
    0x67af, 0x47be, 0xfdb5, 0xdbaa, 0x4baa, 0xe4a4, 0x73a7, 0x6abb, 0xbeaa, 
    0xcfaa, 0xd7af, 0xc4a5, 0xa7a4, 0xb4c2, 0xbec2, 0xbdaa, 0xd3b4, 0xdeb4, 
    0xf5b0, 0xc8ad, 0xbbcb, 0x7da7, 0xfcab, 0xeea4, 0x6bb3, 0x75a5, 0xaea6, 
    0xc8af, 0xd3a7, 0xb0bc, 0x59c2, 0xdca6, 0x50ad, 0x6db8, 0x6dbc, 0xcfab, 
    0xeea8, 0xbcb4, 0xb4af, 0x58b8, 0xe8bd, 0xa5aa, 0xa9b2, 0xa2ba, 0x76aa, 
    0xbfb2, 0xa4a4, 0xd8ac, 0xbea9, 0xc1c4, 0x4ab0, 0xd7b2, 0xfacf, 0x7eb8, 
    0xabad, 0xf2a5, 0xb3b2, 0xe0a6, 0x50a9, 0x7ba6, 0x77ac, 0xa4ee, 0xb0b5, 
    0x62b6, 0x79a8, 0xaaa9, 0x47a9, 0x4bbd, 0x7aa9, 0xdeb1, 0x4ac6, 0x5daf, 
    0xe8ae, 0xefb5, 0xb6a6, 0xdebd, 0xd1bd, 0xddb8, 0x76b3, 0xcba6, 0xebc0, 
    0x4eb5, 0xd6a9, 0x66c6, 0xf1c5, 0x44a5, 0xdbb5, 0x57ac, 0x55a7, 0x45b3, 
    0x4ab6, 0xb1c5, 0xaeb5, 0xeda6, 0x60aa, 0xacaf, 0x6ebe, 0xeca7, 0xf6a4, 
    0xfbab, 0x4db1, 0x6abf, 0xe0c2, 0xb6bc, 0xc8c1, 0x66bd, 0xcebc, 0xdcc9, 
    0xcbb8, 0xa9a7, 0xb2bc, 0xa7a7, 0xacaa, 0xd5b4, 0x40c0, 0x6cb0, 0xd8c2, 
    0x59bc, 0xf3ba, 0xcebd, 0xe3ad, 0xbbae, 0xe5a9, 0xf4a8, 0xe0ae, 0x5ab5, 
    0x5fad, 0x75b0, 0xd6b0, 0xdbb5, 0x60a8, 0x42bf, 0xf7af, 0x74ab, 0xeab8, 
    0xbaab, 0xfeb4, 0x64b2, 0xb6a7, 0xb5b5, 0x4aa5, 0xf3ac, 0xbab7, 0x6ca4, 
    0xdba6, 0x7bba, 0x72a6, 0x4fc3, 0xc4b4, 0xdcc2, 0x76a9, 0xeeba, 0x60c1, 
    0x61c1, 0x51b9, 0xaba8, 0xb5ab, 0x7eb4, 0xb2af, 0xaca8, 0xf2a8, 0xdab1, 
    0xaaaf, 0x41b6, 0xfdaa, 0xd5b2, 0x70c6, 0xa1c4, 0x4cbc, 0x4bbe, 0xccb3, 
    0x6fb8, 0x4cb4, 0xedbf, 0x51ac, 0xaaa5, 0xf5a6, 0x67ac, 0xb5b0, 0x40a7, 
    0xa4a7, 0x79ae, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x48c9, 0x46c9, 
    0x61a4, 0xa2a4, 0xdca4, 0xcaa4, 0x41a5, 0xe5a5, 0xe0a5, 0xacb0, 0x65d1, 
    0xb5be, 0x40a1, 0xebac, 0x40a1, 0x50a4, 0x47c9, 0xd4a4, 0xf8a4, 0x67a5, 
    0xc2a5, 0xc7c9, 0x4ead, 0x50d8, 0xb6b7, 0xe9e2, 0x40a1, 0x40a1, 0x45ab, 
    0xf3e8, 0x41c9, 0xe4a5, 0x51c9, 0xcbaa, 0xb8a7, 0xdeb6, 0x4ce1, 0xb7a4, 
    0xf8cd, 0xedad, 0xeff1, 0xd6b3, 0x40a1, 0xe2c5, 0xacc5, 0x43c9, 0x72a5, 
    0x6fd4, 0xbcb9, 0xd0b0, 0xf3f0, 0xf6a8, 0x72ca, 0x40a1, 0xc2a4, 0x46a6, 
    0xf0cd, 0xd2cb, 0xf3e4, 0xcfb3, 0x66ab, 0xe9d0, 0xe6d0, 0xe6ad, 0xc7e3, 
    0xd1b6, 0xbbb9, 0x42e1, 0x40a1, 0xb0be, 0x40a1, 0xc9aa, 0x40a1, 0xb1a4, 
    0x53c9, 0x52c9, 0x65c9, 0x68c9, 0x40a1, 0x51a5, 0xbadc, 0xf6a5, 0xb7c9, 
    0x5fca, 0xaec9, 0xcead, 0xc9b3, 0xeaa5, 0xeea6, 0xf0a6, 0x61ca, 0xf1a7, 
    0x48a7, 0x46a7, 0x63ca, 0xefa6, 0x40a1, 0xf7a6, 0xb8cb, 0xb9cb, 0xdda8, 
    0xbacb, 0xd4a8, 0xdca8, 0xdba8, 0xd9a8, 0xabbe, 0xb3cb, 0xfabb, 0xc6cb, 
    0xa9be, 0x6bc5, 0xd7c4, 0xdecd, 0x5aab, 0xe3cd, 0xe5cd, 0x4dab, 0x53ab, 
    0xc4ad, 0xc5ad, 0xb4b0, 0xd6ad, 0xd3d0, 0xbfb0, 0x40a1, 0xd8ad, 0xdaad, 
    0xd7d0, 0xbead, 0xc3ad, 0xd0ad, 0xf3e0, 0xb3b0, 0xbab0, 0x55d4, 0xb9b0, 
    0xcdb6, 0xbcdc, 0x6cc5, 0xaabe, 0xc0f5, 0xbfdc, 0xafb9, 0xeae4, 0xb0b9, 
    0xfae0, 0xf0e0, 0xadb9, 0xebe4, 0xeee4, 0x69c9, 0xf8c9, 0x65ca, 0xc2dc, 
    0x5bab, 0xb6ef, 0xf8c9, 0x40f7, 0xbca4, 0x53b4, 0x64c6, 0x55ef, 0xd8f6, 
    0xdcc4, 0x40a1, 0x6dab, 0xb1d0, 0xcbb0, 0xede0, 0x67a6, 0xe1a8, 0x40a1, 
    0x5eab, 0xbdad, 0x4fb3, 0xc0d7, 0xb6c1, 0x43f9, 0xcfb8, 0x5bb8, 0xc6be, 
    0xddf2, 0xbdc3, 0x40a1, 0xbcc9, 0xe6a8, 0xcecb, 0x40a1, 0x40a1, 0xe0ad, 
    0xdfad, 0x40a1, 0x50b0, 0x52b0, 0x53b0, 0xddf0, 0xd6db, 0x5bb3, 0xfeb5, 
    0x46b6, 0x43b6, 0x40b6, 0x40a1, 0xdfdb, 0x46e0, 0x4ce0, 0x47e0, 0xd7b8, 
    0xd9b8, 0x4de0, 0xe1b8, 0xe0b8, 0xd8bd, 0xa1eb, 0x48e0, 0xa6bb, 0xa2bb, 
    0xa5bb, 0x4de4, 0xd4e7, 0xd8e7, 0xd3bd, 0xdbbd, 0xdce7, 0xd4bd, 0xd9bd, 
    0xdbbf, 0xcfbf, 0xa6eb, 0xd6bf, 0xa7eb, 0xd9bf, 0xdcbf, 0xdabf, 0xcdbf, 
    0xd4bf, 0xabeb, 0xd3c2, 0x6dc6, 0xa2ee, 0xb0ee, 0xc4c1, 0xd6c2, 0x56f7, 
    0xfbf2, 0xd7c3, 0xd4c3, 0xa9f9, 0xb8c4, 0x40c6, 0x40a1, 0xafce, 0x40a1, 
    0x56ca, 0xe9a6, 0xc0a8, 0xc1a8, 0xd9cd, 0xdacd, 0x40ab, 0xa4b0, 0xc2d0, 
    0x46d4, 0xa1dc, 0xb5b3, 0xabb6, 0xb2b3, 0x7edc, 0xaab6, 0xd4e0, 0xf5ee, 
    0x52ca, 0x54ca, 0x4bf1, 0x51ca, 0xa9e0, 0xa8cb, 0xd4cd, 0xd6cd, 0xf5aa, 
    0xf3bf, 0xf3aa, 0xd7cd, 0xf7d3, 0xbbd0, 0xbcd0, 0xd7eb, 0x40a1, 0xbdd0, 
    0x50dc, 0xb0c5, 0x72b0, 0xfcd3, 0xfbd3, 0xfad3, 0xefd7, 0xe8d7, 0x6eb6, 
    0x52dc, 0x6be4, 0xc2bb, 0x68e4, 0x49be, 0x54e8, 0xddc1, 0xe1f4, 0x5af6, 
    0xecaf, 0xb7ab, 0xf4e4, 0x57a7, 0x6fca, 0xf0a8, 0xf8d0, 0x40a1, 0x6dd4, 
    0x45bc, 0xeead, 0xe8c0, 0x77f4, 0x40a1, 0x42c9, 0x40a1, 0x4ad4, 0x40a1, 
    0xafa5, 0x63af, 0x57d0, 0xacca, 0xeeb0, 0x40a1, 0xd1b9, 0x4fe9, 0xc3be, 
    0xa4c0, 0x66a6, 0x64a6, 0xc9c9, 0x60a6, 0x52c2, 0xc8c9, 0x65a6, 0x63c3, 
    0xa6a7, 0x40a1, 0x58a9, 0x40a1, 0xf5cb, 0xf6f1, 0x5ca9, 0xfbcb, 0x40cc, 
    0xf4cb, 0xf8cb, 0xfccb, 0xc1d4, 0x53ce, 0x54ce, 0xe5dc, 0xbad4, 0x40a1, 
    0x40a1, 0xb2ab, 0xadab, 0x4cd1, 0xf6b6, 0xfab3, 0xe4dc, 0x4dd1, 0x52d1, 
    0xb2d4, 0x40a1, 0xb7d4, 0xf1b0, 0x40a1, 0xb8d4, 0xcbd4, 0xb5d4, 0xb4d4, 
    0xb3d4, 0xa2d8, 0xa1d8, 0x40a1, 0xfbb3, 0xeddc, 0x40a1, 0x5be1, 0x56e1, 
    0x40a1, 0x55bc, 0xc9c4, 0x67c5, 0x74c5, 0x40a1, 0x4dca, 0x4eca, 0x7acb, 
    0xa1cb, 0xbfcd, 0xa3cb, 0x7ccb, 0x7dcb, 0x52eb, 0xdcaa, 0xb4cd, 0xe5aa, 
    0xe8aa, 0xe7aa, 0xbef4, 0x40a1, 0x55ad, 0xb6cd, 0xe9aa, 0xbacd, 0x41b3, 
    0xe7b5, 0x76e7, 0xc1cd, 0xbecd, 0xc0cd, 0x40a1, 0xdfaa, 0x40a1, 0x52ad, 
    0xc3cd, 0x77d0, 0x5bad, 0x40a1, 0xa1d0, 0xb8f4, 0x6ed0, 0x60ad, 0x75d0, 
    0x5cad, 0x40a1, 0x7ad0, 0x7bd0, 0x64ad, 0x72e7, 0x40a1, 0x67ad, 0xf7b6, 
    0x72de, 0xa2d0, 0x71d0, 0x7dd3, 0x77d3, 0xfeea, 0x6ee7, 0xa5d3, 0xf7b2, 
    0xa6d3, 0xf5af, 0xfcaf, 0x6cd7, 0x4ceb, 0xacd3, 0xf6af, 0xaed3, 0xc5af, 
    0xefad, 0xf16c, 0xa1bb, 0xa140, 0xfa40, 0xfa7b, 0xfbfb, 0xfcbc, 0xfe5b, 
    0x40a1, 0xa3d3, 0xb0f0, 0x40a1, 0x5ebb, 0xd2df, 0x40a1, 0x74d7, 0xf2b2, 
    0xc2e3, 0xb1b8, 0xfbb2, 0x6dd7, 0xf9b2, 0x40a1, 0x59bb, 0xfeb2, 0x40a1, 
    0x67d7, 0x68d7, 0x4feb, 0xfdb2, 0xf1b2, 0xf0b2, 0x5bd7, 0x61c5, 0x40a1, 
    0xd7b5, 0x6ddb, 0x6fdb, 0x70db, 0xbbd4, 0x40a1, 0xd6b5, 0x6edb, 0xe0b5, 
    0x75db, 0x40a1, 0xe3b5, 0x7bdb, 0x7cdb, 0xe8b5, 0xe9b5, 0x79db, 0xd1b5, 
    0xd2b5, 0x5edb, 0xa5db, 0xd5b5, 0x60db, 0xa2bf, 0xddb5, 0x72db, 0x40a1, 
    0xb1df, 0xb8df, 0xb9df, 0xbbdf, 0x41eb, 0x40a1, 0xc2df, 0x44eb, 0xc4df, 
    0xb0b8, 0xb6b8, 0xb4b8, 0xacdf, 0x6fe7, 0xd5df, 0xa9b8, 0xb5b8, 0xb8e3, 
    0xb9e3, 0xbde3, 0x5ac5, 0x40a1, 0x5fbb, 0x63bb, 0x55bb, 0xb0e3, 0xf0f7, 
    0xaee3, 0xb6e3, 0xb7e3, 0xace3, 0x40a1, 0x40ee, 0x5ce7, 0x5fbf, 0x40a1, 
    0x75e7, 0xfdf5, 0x54e7, 0xa9c4, 0xf8ea, 0x4de7, 0xb6bd, 0x64e7, 0xb7bf, 
    0xb8bf, 0xb9bf, 0x42eb, 0xbfbf, 0x44ee, 0x7bea, 0xbbbf, 0xbff4, 0xfbed, 
    0xfced, 0xa9c1, 0xa8c1, 0xf5ed, 0x52ee, 0xc1c3, 0xa3c1, 0x51ee, 0xcac2, 
    0xb8f0, 0xc8c2, 0xf6c4, 0x40a1, 0xd4f2, 0xb9f4, 0xf8f5, 0xc1f4, 0xfcf5, 
    0x40a1, 0xf3f7, 0x7ba4, 0xd9ab, 0xcbc9, 0xddb9, 0x55d0, 0xb3ab, 0x4fae, 
    0x4eae, 0xccb0, 0x71a4, 0xd5c9, 0xbca7, 0xaac0, 0x40a1, 0xbfb1, 0xdee1, 
    0xbdcc, 0xb9cc, 0xe9a9, 0xeda9, 0xfaab, 0xb9e5, 0xdfce, 0xbad1, 0xbbd1, 
    0xb8d1, 0x6dd5, 0x5ed9, 0xbab1, 0xccb1, 0x71d5, 0x51ba, 0x79d5, 0xc5b1, 
    0x68d5, 0xcab1, 0x7ad5, 0xe8e1, 0x60d9, 0x40a1, 0x5dd9, 0xc3bc, 0x67d9, 
    0x40a1, 0x55d9, 0xacb4, 0x7db4, 0x64d9, 0xd2ef, 0xaedd, 0xb6dd, 0xb8dd, 
    0x7ddd, 0xaadd, 0xabdd, 0xbadd, 0xe7e1, 0x4cf4, 0x54ba, 0xafe5, 0x50ba, 
    0x5ec2, 0xd7ef, 0xc1bc, 0xcdf5, 0x40a1, 0xa6e9, 0xa4e9, 0xecec, 0xc2c0, 
    0x45f2, 0xddf7, 0xeff8, 0x7ca4, 0xd6ca, 0x40a1, 0x49b7, 0x40a1, 0x77a5, 
    0x54bc, 0x6ea5, 0x6fa5, 0x7ba5, 0x5ca6, 0x40a1, 0x5ba6, 0x40a1, 0x46e5, 
    0x6dc5, 0x79ca, 0xf2f1, 0x63a7, 0x40a1, 0xf9d0, 0x40a1, 0x40a1, 0x40a1, 
    0xdfcb, 0x40a1, 0x45a9, 0x4ba9, 0xefcb, 0x4ea9, 0x7bc0, 0x46a9, 0x4ca9, 
    0xe7cb, 0x40a1, 0x40a1, 0xfccd, 0x7bab, 0x40a1, 0xfde8, 0xa8ab, 0x78ab, 
    0xf9e4, 0xcdb9, 0x4dce, 0x40a1, 0x41e9, 0xa6ab, 0xa9ab, 0x40a1, 0x44e9, 
    0x40a1, 0xc1ec, 0xa7ab, 0x7dab, 0x40a1, 0xbabe, 0xfecd, 0x4ece, 0x4de1, 
    0xf4d0, 0x47bc, 0x43ae, 0xf8ad, 0xf5d0, 0xd4dc, 0x40a1, 0x44ae, 0xfcd0, 
    0x41ae, 0x77d4, 0xc9b9, 0x6fd8, 0x70d8, 0x40a1, 0xdac4, 0xe7b0, 0xdeb0, 
    0x40a1, 0x75d4, 0x73d4, 0xf2ad, 0xdcb0, 0x71d4, 0x72d4, 0x6ad8, 0xe6b0, 
    0x7cd4, 0xe3b0, 0xe3b3, 0xd6dc, 0xe4b3, 0xedb3, 0x40a1, 0x6ed8, 0x71d8, 
    0xe7b3, 0xeeb3, 0xdbdc, 0x66d8, 0xd7b3, 0xd8b6, 0xc6b9, 0x41f4, 0xe1b3, 
    0xf1b3, 0x40a1, 0xc8b9, 0xe8b6, 0xcab9, 0xdfb6, 0xd9c4, 0x40a1, 0xd2dc, 
    0xdbb6, 0xd0dc, 0xd3dc, 0xe2b6, 0xe7b6, 0xdddc, 0xbcbe, 0xccdc, 0xdedc, 
    0xd9b6, 0x40a1, 0xe1b6, 0xafc5, 0x40a1, 0xcbb9, 0x49e1, 0x4ee1, 0x58c4, 
    0x40a1, 0xbeb9, 0xbfb9, 0x47e1, 0x40a1, 0xfee4, 0x49bc, 0x50bc, 0x44e5, 
    0x40a1, 0xbebe, 0xb2be, 0x50c2, 0xf8e4, 0x40a1, 0xc3ec, 0xb6be, 0xbbbe, 
    0xb3be, 0x40a1, 0x40a1, 0x7dc0, 0xc0ec, 0x40a1, 0xeef8, 0x49c9, 0x5fa6, 
    0xc6c9, 0xacd4, 0x7aa7, 0xf1cb, 0xaaab, 0x46ae, 0xabd4, 0xebb0, 0x47e9, 
    0x57b4, 0x6fcc, 0x6ecc, 0xaea9, 0xdbec, 0xb2e1, 0xf6b9, 0x63b1, 0xf2d8, 
    0xf7b9, 0xf3b9, 0x40a1, 0x6ebc, 0xc4a7, 0xd7c9, 0xc0ca, 0xc1a7, 0xf2b9, 
    0xc2ca, 0x73ae, 0x40a1, 0xc2a7, 0x50b4, 0x40a1, 0x5ecc, 0x65cc, 0x40a1, 
    0x61cc, 0xa6a9, 0xa7a9, 0x63cc, 0x40a1, 0xa2a9, 0x61e9, 0xd0ab, 0x69e5, 
    0x7dce, 0x57b1, 0x63e5, 0xfbd4, 0x5db1, 0xfcd4, 0x44d5, 0x5cb1, 0xf4d4, 
    0x53b1, 0x55b1, 0xafc0, 0x40a1, 0x51b4, 0xe6d8, 0x52dd, 0xe3d8, 0x44b7, 
    0xa8e1, 0xdbd8, 0xddd8, 0x4fdd, 0x43b7, 0x40a1, 0xa3e1, 0x62e5, 0x6abc, 
    0xc7c1, 0xdaec, 0x71c5, 0x4cc9, 0xcfa7, 0x75cc, 0xe0ab, 0xe2ab, 0xe1ab, 
    0x74b1, 0x70b1, 0x75b1, 0x61b4, 0x64dd, 0x78bc, 0x75e9, 0xfcc5, 0x40a1, 
    0x40a1, 0xa6c9, 0x43ca, 0x78c2, 0x40a1, 0x63cb, 0x65cb, 0xadaa, 0x40a1, 
    0x61cd, 0xc0cf, 0x61ea, 0xbcac, 0xacde, 0xa5d2, 0x58af, 0xaad2, 0xa8d2, 
    0x60ea, 0xa4d2, 0xa9d2, 0x61d6, 0x74b2, 0x7ac5, 0x65d6, 0x69d6, 0x60d6, 
    0x6ef4, 0x70da, 0x40a1, 0x54b5, 0x40a1, 0x40a1, 0x71da, 0xbcba, 0xcbe2, 
    0xfbbc, 0x79e6, 0x5eea, 0x65ed, 0xddf5, 0xdfa6, 0xd9b9, 0xb8b6, 0xdbb9, 
    0x40a1, 0x40a1, 0x4fef, 0xbbb6, 0xadf1, 0xb9b6, 0xaedc, 0xbdb6, 0x7eb9, 
    0xe8bb, 0xc1e8, 0x6cbe, 0x5fc0, 0x40a1, 0x49c3, 0xb1f3, 0xaff3, 0x44c4, 
    0x40a1, 0x57f5, 0x40a1, 0x79c9, 0x75e5, 0xcdca, 0xb4a9, 0xbace, 0xd6ab, 
    0x49d5, 0x67b1, 0x68b1, 0x4bd5, 0xe9bd, 0xbce1, 0xb6e1, 0x73e5, 0x72e9, 
    0x6fe9, 0x74c1, 0x40a1, 0x7bc9, 0xa4a6, 0xe2c9, 0xadbc, 0xdbca, 0xd3e1, 
    0xddca, 0xdeca, 0x5ab7, 0xa3b1, 0x5bb7, 0xd8a7, 0xd9ca, 0xd7a7, 0x7ccc, 
    0xc5a9, 0x7bcc, 0xcda9, 0xc2a9, 0xa9cc, 0xcba9, 0xcca9, 0xa2cc, 0x7de9, 
    0xc9a9, 0x45ba, 0xe6ec, 0x6cb4, 0x5fb7, 0xcbce, 0xf1ab, 0x40d9, 0xafae, 
    0xaaae, 0xc7e1, 0xa7d1, 0xa8d1, 0xa5d1, 0xadae, 0xaad1, 0x67b4, 0xa2b1, 
    0x5ed5, 0x5cd5, 0xa6b1, 0xa8b1, 0xabb1, 0x7cb1, 0x59b7, 0xa6e5, 0x6ab4, 
    0x68b4, 0x6db4, 0x73b4, 0x70b4, 0x68dd, 0x67dd, 0x48ba, 0xa9bc, 0xacbc, 
    0xa5bc, 0x40a1, 0xcfbe, 0x6ac3, 0xbda9, 0x6ff1, 0xacad, 0x45d8, 0xf2c1, 
    0xa3b6, 0x7bb6, 0x7adc, 0x6bf6, 0x5bbe, 0xa7e8, 0xc4f9, 0xa6e8, 0x48ec, 
    0x4bec, 0x40a1, 0x4cec, 0x4eec, 0x46ec, 0xf1ee, 0xf0c1, 0xf3c2, 0xf5c2, 
    0xf6c2, 0x45f5, 0x40a1, 0x62c9, 0xcfa9, 0x40a1, 0xfdc9, 0xfac9, 0xfcc9, 
    0xd3f5, 0x4aa8, 0x4ea8, 0x5ca8, 0x50a8, 0x51a8, 0x53cb, 0x58a8, 0x5aa8, 
    0x59a8, 0xecd9, 0xf8cc, 0xf4cc, 0xf5cc, 0x5df2, 0x79c3, 0x74aa, 0x72aa, 
    0x40a1, 0x7eaa, 0x7daa, 0xf6ef, 0xf0cc, 0xf1cc, 0x62aa, 0x6caa, 0x7baa, 
    0xf9ae, 0xa7ac, 0xa8ac, 0x7bac, 0x48af, 0xfed9, 0xa7cf, 0x77cf, 0x78cf, 
    0xadac, 0xaeac, 0xf8e9, 0xabac, 0xacac, 0x7acf, 0x73c2, 0x71e2, 0xf1bc, 
    0x76cf, 0x50d2, 0x4fd2, 0xe9d5, 0x5ce6, 0x55d2, 0xfeae, 0x4baf, 0x40a1, 
    0x57d2, 0x5ad2, 0x46d2, 0x5ab2, 0x4eb2, 0x58b2, 0x53b2, 0x70c2, 0x67b2, 
    0xedd5, 0x49bf, 0x68b2, 0xf3d5, 0x46b2, 0x6ec2, 0xe1d5, 0xf1d5, 0x52b2, 
    0xf1d9, 0xf3b4, 0x42b5, 0x47da, 0x59de, 0x47b5, 0xa9e2, 0x4bda, 0xe6b4, 
    0xe8b4, 0xecb4, 0x44b5, 0xd0f9, 0x4cde, 0xc2b7, 0xd4f5, 0xa5e2, 0xefef, 
    0xc1b7, 0xccb7, 0x50de, 0x40a1, 0x54de, 0x5fe6, 0xcdb7, 0x58de, 0x46de, 
    0xbcb7, 0x48de, 0x43e6, 0x5ef2, 0x74c3, 0x40a1, 0xa8ba, 0xa3e2, 0xaaba, 
    0xa1e2, 0x6bc4, 0xf5ef, 0xacba, 0x75e2, 0x78ba, 0x41e6, 0x40e6, 0x42e6, 
    0xe8bc, 0x4ce6, 0xe0bc, 0xebbc, 0x75c3, 0x41ea, 0x43bf, 0x46bf, 0x47bf, 
    0xfcbe, 0xe1c0, 0xe4c0, 0x57ed, 0xdac0, 0xdec0, 0x76c3, 0x63f2, 0x73c3, 
    0x65f4, 0x5cf4, 0xaff8, 0xf4c5, 0x40a1, 0x73c9, 0x58cc, 0x57cc, 0xc9ab, 
    0x66ae, 0xacda, 0xcac4, 0x53ba, 0xedb9, 0x64bc, 0x72eb, 0xc8be, 0xbfee, 
    0xaaee, 0x40a1, 0xcecd, 0xd1cd, 0x7ead, 0xb5d0, 0xa3ad, 0xe2c2, 0x7bad, 
    0x77b3, 0xa4ad, 0x6db0, 0xefd3, 0xe3d7, 0xddc5, 0xe4d7, 0x70b3, 0x7cb3, 
    0xe7d7, 0x66b6, 0x6ab6, 0x4ddc, 0x4fdc, 0xa1e0, 0x4eb9, 0x7de0, 0x49b9, 
    0x43be, 0xb8bb, 0xbdbb, 0xbfbb, 0xe6be, 0xeebf, 0xd8c1, 0xdbc1, 0xe4c2, 
    0xe3c2, 0xe5c3, 0x40a1, 0x6bb1, 0xbdce, 0xf4d8, 0x75c9, 0xa4ab, 0x6aae, 
    0xf3d4, 0x47b4, 0xa2e1, 0xabc0, 0xf3c4, 0x79d1, 0xb8a9, 0xdaab, 0x58d7, 
    0x5db4, 0xa7f7, 0x4bc9, 0x71a6, 0x6da6, 0xb0a7, 0x61bc, 0xe1b9, 0xada7, 
    0xb5ca, 0x6ea9, 0xb5d8, 0xaca7, 0xb1a7, 0x71a9, 0x48cc, 0x6fa9, 0x6ba9, 
    0x63a9, 0xd5d4, 0x63bc, 0x67ce, 0xdcf6, 0xbbab, 0xb9ab, 0x40a1, 0x5bae, 
    0x5cae, 0x45b4, 0x5fbc, 0x50ae, 0x58ae, 0x55ae, 0xfeb0, 0xd1d4, 0x44b1, 
    0xd0d4, 0x40b1, 0x41b1, 0x60bc, 0x40a1, 0xfeb6, 0x44b4, 0x40b4, 0xb8d8, 
    0xfcb6, 0x72e1, 0x42b7, 0x54e9, 0xa7c0, 0xf7dc, 0x75e1, 0xe4b9, 0x52e9, 
    0xe2b9, 0xdfb9, 0xe3b9, 0x6be1, 0x5ebc, 0x51e9, 0x58e9, 0xd0ec, 0xa8c0, 
    0x5cc4, 0xd4c9, 0x40a1, 0xb7a7, 0x56cc, 0x46b4, 0x6da4, 0x6ea4, 0x55cc, 
    0xcae8, 0x6fbe, 0x74be, 0x5ff5, 0xe6c5, 0x71be, 0xcee8, 0xbbc5, 0x7ef7, 
    0x63c0, 0x7ac6, 0xaff1, 0xb2f1, 0xb6f1, 0x7cf6, 0x63f5, 0x45c4, 0x7df6, 
    0x61f5, 0x62f5, 0x59c5, 0x7bf6, 0x7cf7, 0x6bc6, 0xb3f9, 0x40a1, 0xfaac, 
    0xf4ac, 0xf8ac, 0x4bd0, 0xe7f5, 0xc6af, 0xc4af, 0x56d3, 0xe6d6, 0xd8b2, 
    0xd9b2, 0xa8bf, 0xd1b2, 0xd2b2, 0x40a1, 0x40a1, 0xfbda, 0xbcb5, 0x58df, 
    0x60df, 0x57df, 0xf0ba, 0xf6ba, 0x6ee3, 0x40a1, 0x6ce3, 0x78bd, 0xfeba, 
    0x71e3, 0x65e3, 0xedba, 0xfdba, 0x79bd, 0xe7e6, 0x7bbd, 0x7abd, 0x72f0, 
    0xebe6, 0xece6, 0xede6, 0xd8ea, 0xe8e6, 0xa7bf, 0xa6bf, 0xd0ea, 0xa5bf, 
    0xd0ed, 0x7ebf, 0xa1bf, 0x7dc4, 0x65c1, 0x67c1, 0x5dc1, 0x5bc1, 0x62c1, 
    0xe9f5, 0xb7c2, 0xb9c2, 0x40a1, 0xadf4, 0xb2f2, 0xb4f2, 0x40f9, 0x40a1, 
    0x42bd, 0x40a1, 0x72cd, 0x6fb0, 0x45ca, 0x59bf, 0xb3de, 0x6acd, 0xb3aa, 
    0xb2aa, 0xc8cf, 0x6ec4, 0xbfac, 0xc5ac, 0xc4ac, 0x40a1, 0xc9cf, 0xadd2, 
    0xacd2, 0x7ab9, 0x78b2, 0xb2d2, 0xb0d2, 0x5faf, 0xa2c3, 0xe9b7, 0xa3e6, 
    0x58b5, 0xebb7, 0x61b5, 0x5bb5, 0x62b5, 0x7cda, 0x7ada, 0x7bda, 0x60b5, 
    0xa1da, 0xe8b7, 0xecb7, 0xb6de, 0xe5b7, 0xeab7, 0xf5c0, 0xc1ba, 0x40bd, 
    0x58bf, 0xedc4, 0x41bd, 0xa6e6, 0xa2e6, 0xfdbc, 0x5cbf, 0xf6c0, 0xf3c0, 
    0x69ed, 0x7ac2, 0xd0c5, 0x6ff4, 0xa4f1, 0xa5f3, 0xfcc3, 0x42cb, 0x40a8, 
    0xfba7, 0x43cb, 0x59e2, 0x55f4, 0x4aaa, 0x57aa, 0x48aa, 0xd9cc, 0x40a1, 
    0x53aa, 0xb3d9, 0xd7bc, 0xfab1, 0x44aa, 0x49aa, 0x56aa, 0x55cf, 0xcdc0, 
    0x43cf, 0x53f4, 0x5eac, 0x69ac, 0x56f4, 0x6aac, 0x4acf, 0x63ac, 0x4ccf, 
    0x6cac, 0xf7b1, 0x61cf, 0x65ac, 0x6bac, 0x52f2, 0xfbce, 0xf7ec, 0xdcd1, 
    0xddd1, 0xb2d9, 0xf8be, 0xe8d1, 0xa9b7, 0xe9d1, 0x4ee2, 0xf2b1, 0xebd1, 
    0x40a1, 0xecbe, 0xecae, 0xccc0, 0xe5ae, 0xe1f7, 0x40a1, 0xdbd1, 0xddae, 
    0xebb1, 0xc0d5, 0xbed5, 0xc1d5, 0xeab1, 0xc4d5, 0xe7c4, 0xd8b4, 0xc8d9, 
    0x70c3, 0xdee5, 0xcdb4, 0xe2f7, 0xdedd, 0xcad9, 0xcdbc, 0xd4dd, 0xd0b4, 
    0xb9d9, 0xcedd, 0xd2dd, 0xa3b7, 0xd3dd, 0xafb7, 0x56c6, 0xaab7, 0x40a1, 
    0x40a1, 0xdfdd, 0xe0dd, 0x6cba, 0x68c4, 0x71c3, 0x41e2, 0xd1f5, 0xc9dd, 
    0xb0b7, 0xadb7, 0x64ba, 0x49e2, 0x66ba, 0x67ba, 0x6bba, 0x52e2, 0xfce1, 
    0x60ba, 0x54e2, 0x62c2, 0x5fba, 0x4cf2, 0x4ae2, 0xd3e5, 0xcec0, 0xd8bc, 
    0xcce5, 0xe8e5, 0x55f2, 0xdae5, 0xf1be, 0xc4e9, 0xd2c0, 0xd1e9, 0xc8e9, 
    0xf6be, 0xfeec, 0x72c3, 0xeabe, 0xcae9, 0xefbe, 0x58f2, 0x44ed, 0xd1c0, 
    0xf5ec, 0xd0c0, 0x40a1, 0xdfb7, 0x76e6, 0x5caa, 0x65cf, 0xdcbc, 0x66cf, 
    0x66e2, 0xd4c0, 0xdcd5, 0xe3e9, 0xfbdd, 0x6cc2, 0xe2e9, 0x62b0, 0x6db3, 
    0x40a1, 0x61b6, 0x47f8, 0x40dc, 0x63b6, 0x48dc, 0x40a1, 0x65f7, 0xfedb, 
    0xfdb8, 0xfeb8, 0x79e0, 0x78e0, 0xb3bb, 0xfbbd, 0xfebd, 0xf9bd, 0xfdbd, 
    0xeabf, 0x46f1, 0xe1c3, 0x40a1, 0xb1cc, 0xd7e1, 0xaeb1, 0x75b4, 0x61b7, 
    0x60b7, 0x75dd, 0x76dd, 0xd6e1, 0x4ebb, 0x5ebf, 0xbed2, 0xc0de, 0x72ed, 
    0x70ed, 0x49f0, 0x40a1, 0xeec9, 0xedc9, 0xf8ca, 0xfea9, 0xe8be, 0x58aa, 
    0xcacc, 0xfda9, 0xfba9, 0x52cd, 0x54ac, 0xf5ce, 0xf6ce, 0x52ac, 0xebce, 
    0xeece, 0xcfaf, 0xd4d1, 0xe7be, 0xd0ae, 0xcbae, 0x75b7, 0xaad5, 0x40a1, 
    0xbeb4, 0x79b7, 0xc0dd, 0xc7c0, 0x5cba, 0xb4e9, 0xdbef, 0x60c2, 0x66c4, 
    0xe4c4, 0x4eb6, 0xeadb, 0xe8db, 0x4db6, 0xf4f0, 0xedb8, 0xf0b8, 0x52f6, 
    0xacbb, 0xece7, 0x53e4, 0x40a1, 0xeee7, 0xccc1, 0xd1db, 0xbbc1, 0x44e4, 
    0x53f7, 0xccbf, 0x7aee, 0xd1c2, 0xf3f2, 0xbdcf, 0x40a1, 0xc9a6, 0x40a1, 
    0xbaac, 0x5cd6, 0x5fd6, 0x51b5, 0x6cda, 0xa5de, 0x40a1, 0xb9ba, 0xb2ae, 
    0x66d5, 0x54d9, 0xeee1, 0xbcc0, 0xfeac, 0x40a1, 0xe5d9, 0xb7b7, 0xf7e5, 
    0x40de, 0xfbbe, 0x5bf2, 0xe8e9, 0xe7f6, 0x7ec9, 0xf7c9, 0x49cb, 0x4acb, 
    0x6ccf, 0xe3b4, 0xf4ae, 0xdfd5, 0x72ba, 0x40a1, 0xd5b1, 0x40a1, 0x7cc3, 
    0xdeb7, 0xf8bc, 0xb8ac, 0x7ee7, 0x47a6, 0x40a1, 0x78cb, 0x77a8, 0x40a1, 
    0xd2cc, 0x40a1, 0xd0aa, 0xd2aa, 0xb0cd, 0xd4aa, 0x40a1, 0x67c4, 0x40a1, 
    0x40a1, 0xa4c4, 0x4bad, 0x5bd0, 0x48ad, 0x5fd0, 0x67d0, 0x65d0, 0xface, 
    0x4fad, 0x48d7, 0xd6af, 0xdbaf, 0xdaaf, 0x7ac1, 0x40a1, 0x6ad3, 0xe2af, 
    0xd3ae, 0x40a1, 0x62b3, 0xa2df, 0x4cd7, 0x4bd7, 0x45d7, 0x40a1, 0x40a1, 
    0xcbb5, 0xccb5, 0xcdb5, 0x40a1, 0x40a1, 0x78b8, 0x73df, 0x76df, 0x40a1, 
    0xa6e3, 0x40a1, 0x7ddf, 0xe6dc, 0xf9dc, 0x4abb, 0x4dbb, 0xc1c2, 0xf0bc, 
    0xfde6, 0x40a1, 0xeaed, 0x61c2, 0xebed, 0xe8ed, 0x40a1, 0xe9ea, 0x6ac2, 
    0xd8d5, 0xdad9, 0xf7dd, 0xf5dd, 0xf9be, 0xcde4, 0xe3bb, 0xfcc1, 0x42c4, 
    0x74f6, 0x40a1, 0x5fc9, 0x62dd, 0xd4c1, 0x7dee, 0xb4b4, 0x7ef8, 0xe3c4, 
    0xf3a9, 0xcfd1, 0xd1d1, 0xd0d1, 0xdcb1, 0xddb1, 0x7ed9, 0x59ba, 0xd5b7, 
    0x6dde, 0x57cd, 0x40a1, 0x40a1, 0x40a1, 0xaecf, 0xafac, 0xb1cf, 0x4dea, 
    0x4caf, 0x51d6, 0x55d6, 0x54bf, 0x5ada, 0x5dda, 0x40a1, 0xd4b7, 0xdbb7, 
    0x40a1, 0x73de, 0x40a1, 0x74de, 0x40a1, 0x40a1, 0x72e6, 0x69e6, 0xf7bc, 
    0x66e6, 0xeec0, 0x4fea, 0xe6c0, 0xfbef, 0xdaf5, 0x79c6, 0x40a1, 0x76c2, 
    0xd6b7, 0x51bf, 0xd1a9, 0xb2cc, 0xd3ce, 0xafb1, 0x76b4, 0x40a1, 0xc1aa, 
    0xe6ac, 0xe7ac, 0xe7d2, 0xe9d2, 0xead2, 0xaeaf, 0xe4ef, 0xadaf, 0xa8af, 
    0xd5ba, 0xbdd6, 0x52b8, 0x49c1, 0xf9e2, 0xcbe6, 0x48c1, 0xa1c5, 0xd7ca, 
    0xd8ca, 0xc8ef, 0x7cd1, 0x7dd1, 0x7ed1, 0xa1d1, 0x7eae, 0xa1ae, 0xc3e1, 
    0x5eb7, 0x5db7, 0x7cbc, 0xcdbe, 0x76e9, 0xb7c0, 0x56c2, 0xbaf9, 0x40a1, 
    0xd6a6, 0xeecc, 0x4fed, 0xe7d9, 0x46c1, 0x7acd, 0xeee2, 0xf1cf, 0xcfda, 
    0x40a1, 0xebcf, 0x48ac, 0xa2af, 0x40a1, 0x7caf, 0x40a1, 0x72c4, 0xe2f5, 
    0xa4af, 0x40a1, 0xa1af, 0xdad2, 0xd9d2, 0xe2d2, 0xbab2, 0xc9da, 0xc6da, 
    0xa6ed, 0xb9d6, 0xbbd6, 0x40a1, 0xb8d6, 0xcbda, 0x6cbf, 0x50b8, 0xe5de, 
    0xe4de, 0xa7ea, 0xecde, 0xd4ba, 0xebe2, 0x40a1, 0xf3e2, 0xc6e6, 0x40a1, 
    0xcae6, 0x6bbf, 0xaaed, 0xabed, 0x45c1, 0x58f0, 0x7df4, 0xa4f2, 0xa1f2, 
    0xcef7, 0xb0dc, 0xb0ef, 0xf5f3, 0x77cd, 0xe2cf, 0x40a1, 0xdaac, 0xe0ac, 
    0xe0cf, 0xd6d2, 0xd7d2, 0xd5d2, 0xaad6, 0x40a1, 0xb2d6, 0xb6b2, 0xfdb7, 
    0xa5c2, 0xbbda, 0x40a1, 0xddde, 0x45b8, 0x46b8, 0x44b8, 0xcdba, 0xbce6, 
    0xccba, 0xe7e2, 0x4fbd, 0x50bd, 0x67bf, 0x65bf, 0xfdc0, 0x7aed, 0xa2c2, 
    0x6dcb, 0x71cd, 0xcaac, 0xcbac, 0xdbcf, 0xc1d2, 0xaeda, 0xc4de, 0x40a1, 
    0x4dd0, 0x5cd3, 0xcbaf, 0xe0db, 0x6adf, 0xbcf2, 0x40a1, 0xa9bf, 0xf9c5, 
    0xe3ed, 0x72af, 0x64bf, 0xf8f7, 0x40a1, 0xc1d0, 0xc0d0, 0x7ab0, 0x78b0, 
    0x42d4, 0x41d8, 0xa9b3, 0xfcd7, 0xeaeb, 0xa6b3, 0xfdd7, 0x40a1, 0x67dc, 
    0x64b9, 0x7ab6, 0x78b6, 0x62dc, 0x70dc, 0x6fdc, 0x64dc, 0x6adc, 0xb1e0, 
    0xb2e0, 0x57b9, 0xb3e0, 0x40a1, 0x5ab9, 0x59b9, 0xb5e0, 0xbbe0, 0xbae0, 
    0x66b9, 0xe0c5, 0xb0e0, 0x62b9, 0xaee0, 0x63b9, 0xb7e0, 0x65b9, 0x4dc5, 
    0x52be, 0x7be4, 0x7ae4, 0x7de4, 0xa8e4, 0x68e8, 0xc2c4, 0x40a1, 0x4cc5, 
    0x40a1, 0xa1e4, 0x5af1, 0xcbbb, 0x6fe8, 0xb7e4, 0xf3f4, 0xcdbb, 0xa6e4, 
    0x6af3, 0xa2e4, 0xfebf, 0xa3e4, 0xb6e0, 0xe7f4, 0xcfbb, 0xa7e4, 0xf9f4, 
    0xe4eb, 0x69e8, 0xf4c3, 0x40a1, 0x59be, 0x70e8, 0x72e8, 0x56be, 0x6ee8, 
    0x40a1, 0x40a1, 0x40a1, 0x63e8, 0x6ae8, 0x76e8, 0xe8eb, 0xe1eb, 0x40a1, 
    0xe6eb, 0x40a1, 0xe9eb, 0x43c0, 0x44c0, 0x40a1, 0x40a1, 0xdfeb, 0xe0eb, 
    0x45c0, 0xe6c1, 0xd2ee, 0xd6ee, 0xeec1, 0xd0ee, 0x5cf1, 0xecc1, 0x40a1, 
    0xf3c3, 0xf2c3, 0x40a1, 0x40a1, 0x68f3, 0xf0c2, 0x4fc5, 0x40a1, 0x5ef1, 
    0xefc2, 0x53f1, 0x67f7, 0xf0c3, 0xedc3, 0xeec3, 0xf1c3, 0x60f3, 0xebc3, 
    0x61f3, 0xe9c3, 0xecf4, 0x40a1, 0xf5f4, 0x40a1, 0xe8f4, 0x53f9, 0xeaf4, 
    0x40a1, 0x66f3, 0xf1f4, 0x69f7, 0x4ec5, 0x5ef6, 0x40a1, 0xdfc5, 0xe9c1, 
    0xe8cf, 0xc4da, 0x6eb9, 0xf9cf, 0xf1d2, 0xb0af, 0xedd2, 0x40a1, 0x52b4, 
    0xd8da, 0xd6da, 0x5cb8, 0x5ab8, 0xd2e6, 0x5ebd, 0xa8c2, 0x48c2, 0x4cc3, 
    0xf8f6, 0xd4ac, 0xaeb2, 0x71b5, 0xd5de, 0x78ed, 0xbcd2, 0x7db2, 0x69a8, 
    0xa7b9, 0xf0bb, 0xe9e8, 0x7dbe, 0x6cc0, 0xf0f3, 0xb7f9, 0xa9ec, 0xaeec, 
    0xeec5, 0x7bef, 0xc1f7, 0x7eef, 0xa2ef, 0x7dc6, 0xd0f1, 0xcff9, 0x5bc3, 
    0xd9f1, 0x70f8, 0xcff1, 0x4dc4, 0x40a1, 0x4fc4, 0xedf3, 0x4cc4, 0x7cf5, 
    0xa7f5, 0x40a1, 0x40a1, 0x40a1, 0x63c5, 0xb4f6, 0x78c6, 0xc2c5, 0xbaf7, 
    0x63f8, 0x6bf8, 0x6cf8, 0x66f8, 0x40a1, 0x4fc6, 0x7cc6, 0x40a1, 0x6fcb, 
    0x4bf0, 0x7ec2, 0xb7aa, 0x40a1, 0xd0ac, 0x69af, 0x40a1, 0x6eaf, 0xc6d2, 
    0x40a1, 0xc3d2, 0x40a1, 0x6daf, 0x40a1, 0xadb2, 0x69b5, 0xf7c0, 0xb2da, 
    0xb5da, 0x77ed, 0xafda, 0xc9de, 0xf3b7, 0xccde, 0xf5b7, 0xcfde, 0xefb7, 
    0x76ed, 0xdce2, 0xb0e6, 0xdfe2, 0xafe6, 0x40a1, 0xdde2, 0xb1e6, 0xb7e6, 
    0x70ea, 0x49bd, 0x43bd, 0x40a1, 0x6cea, 0x73ea, 0xf4f6, 0x71ea, 0x74ed, 
    0x7dc5, 0x6fea, 0x40a1, 0xeec4, 0x40a1, 0x4af0, 0x7dc2, 0xf6c5, 0x40a1, 
    0xf6d6, 0xdcda, 0xa5cd, 0xc6aa, 0xfecf, 0xf6d2, 0xb7af, 0xc0b2, 0x75c4, 
    0x5eb8, 0x4ce3, 0x4ae3, 0xb2ea, 0xd5e6, 0x40a1, 0xaed0, 0xd4d3, 0x4db0, 
    0xd7d3, 0x4cb0, 0xb5d7, 0xcbc3, 0xcbdb, 0xcadb, 0xcfdb, 0x72ee, 0xf4df, 
    0xf0f2, 0xd9f0, 0xf7e3, 0x75bb, 0xfce3, 0x74bb, 0xfae3, 0x40e4, 0xcabf, 
    0xc8e7, 0xc8bd, 0xb9c1, 0xcabd, 0xb0c4, 0xc9bf, 0xb7c1, 0x75ee, 0xcbf4, 
    0x4bf9, 0xd4a5, 0x45ad, 0xe1e2, 0x72b5, 0xe1ac, 0xd4a6, 0x54d0, 0x66d3, 
    0xe0b2, 0x40a1, 0x40a1, 0x40a1, 0xa2bd, 0xe7ed, 0xe6ea, 0xaebf, 0x40a1, 
    0xc0b5, 0xaacd, 0x43d7, 0xe2b2, 0xb2f4, 0xc1b5, 0x7cf0, 0x6dc1, 0xf7b5, 
    0xacdc, 0xe0e0, 0xdee0, 0x65be, 0x67be, 0x40a1, 0xb8e8, 0x58c0, 0x45c3, 
    0x46c3, 0xaef9, 0x4ff5, 0xa8f3, 0x56c5, 0x40a1, 0x49c6, 0x4fca, 0x40b0, 
    0xc8cd, 0xcacd, 0xe3f2, 0x6bad, 0xa8d0, 0x6aad, 0xbad3, 0xbcd3, 0xc2d3, 
    0xc3b8, 0xccd3, 0xc8d3, 0x47b0, 0x42b0, 0x43b0, 0x45b0, 0x46b3, 0x47b3, 
    0x48b3, 0xf7c4, 0xa5d7, 0xa4d7, 0x4bb3, 0x4cb3, 0x4db3, 0xdef2, 0xabd7, 
    0xbcdb, 0xe6df, 0xccc2, 0xedb5, 0x6beb, 0xf1b5, 0xbbb8, 0xf2b5, 0xc5f4, 
    0xebb5, 0xb1db, 0xbfdb, 0xc2b8, 0xbdb8, 0xeadf, 0xbcb8, 0xc4b8, 0xefdf, 
    0xecdf, 0xd6e3, 0x66bb, 0xdbe3, 0x68bb, 0xdae3, 0xf2e3, 0xe2e3, 0xb4c1, 
    0x69bb, 0xebe3, 0x6dbb, 0x6cbb, 0x64bb, 0xb8bd, 0x67bb, 0x40a1, 0xc6f4, 
    0xbce7, 0xbbbd, 0x40a1, 0xc1bd, 0xb8e7, 0x40a1, 0xc2bd, 0xa2e7, 0xb2c1, 
    0xa3e7, 0xbfbd, 0xbae7, 0x5eeb, 0x67ee, 0x40a1, 0xafc1, 0xb0c1, 0x60eb, 
    0x67eb, 0x5aee, 0x5deb, 0xc0bf, 0xb1c1, 0xc1f0, 0x5dee, 0x5fee, 0xaec1, 
    0xb5c1, 0xc7f0, 0x6eee, 0xadc1, 0xacc1, 0x69ee, 0xbef0, 0xc0f0, 0xcfc2, 
    0x40a1, 0xc9f4, 0xc8f4, 0xcac3, 0xe7f2, 0x42f6, 0xf9c4, 0xfbc5, 0x6ef9, 
    0xcea6, 0xa2c4, 0x6ac1, 0xe1ed, 0xe7af, 0xc7aa, 0xf2ac, 0x44d3, 0x77bf, 
    0x40d3, 0x63b8, 0x45d3, 0xd7d6, 0x43d3, 0xccd6, 0xdbd6, 0xd4d6, 0xc6b2, 
    0xc8b2, 0xd6d6, 0xc1b2, 0xced6, 0xd1d6, 0xd2d6, 0xfbf8, 0xc7b2, 0xe5da, 
    0xc3ed, 0xe6da, 0xe1ba, 0xe2da, 0xe5ba, 0x61b8, 0x62b8, 0x40a1, 0x40a1, 
    0x4bdf, 0x4edf, 0x52e3, 0xbded, 0xd7e6, 0xe6ba, 0xd9e6, 0xe3ba, 0xfbf6, 
    0x58e3, 0xb0c2, 0x4fe3, 0x40a1, 0xadc2, 0x65bd, 0xb1c2, 0x68bd, 0x6abd, 
    0xbaea, 0xbdea, 0xbcea, 0x7bbf, 0xc1ea, 0x50c1, 0x4ec1, 0x40a1, 0xbbed, 
    0xb8f8, 0xc2ed, 0x65f0, 0xafc2, 0x68f0, 0xb0c3, 0xa3c5, 0xb3c3, 0xd8aa, 
    0x70d3, 0xeab2, 0x57db, 0xe5af, 0xd0d3, 0x69d0, 0x51ad, 0xc3f2, 0x40a1, 
    0x71d3, 0xeaaf, 0xe9af, 0x53d7, 0x46f7, 0x54d7, 0x56d7, 0x40a1, 0xa5df, 
    0xa4df, 0x53bb, 0x46e7, 0xf1ed, 0xa2f0, 0xb6f4, 0xdad3, 0xcdb8, 0x50b3, 
    0xcab8, 0xc6b8, 0xcec3, 0xf4d6, 0x6ddf, 0x6edf, 0x7ebd, 0xf3e6, 0xaabf, 
    0x40a1, 0xc5ae, 0x4ad3, 0x40a1, 0x40a1, 0xe8da, 0xe7da, 0x54df, 0x60e3, 
    0xeaba, 0x58c1, 0x40a1, 0xe1e6, 0x40a1, 0xe0e6, 0xdfe6, 0xc9ea, 0xcfed, 
    0xe1a6, 0x5bba, 0xfdac, 0xdeb2, 0xbfb5, 0x7de3, 0x42bb, 0xf9e6, 0xa1bd, 
    0xadbf, 0x6bc1, 0xcda6, 0xdeed, 0x75e3, 0x76e3, 0xd9ed, 0x41f9, 0xa2be, 
    0x54c4, 0x76ad, 0xeddb, 0x64e0, 0x5ee0, 0x6ff9, 0x69b3, 0xeabd, 0xded3, 
    0x61b3, 0xaaad, 0xfed3, 0x40d4, 0x41d4, 0x5edc, 0x60dc, 0x5ddc, 0x40a1, 
    0x55b9, 0xade0, 0x6fc6, 0x71f9, 0x73e4, 0xc6bb, 0x74e4, 0x40a1, 0x5be8, 
    0xd9eb, 0xdbeb, 0xdaeb, 0xc6ee, 0xdec1, 0x52f1, 0x5ff3, 0xe7c3, 0x51f1, 
    0xe4f4, 0xbfc4, 0x48c5, 0xa8a8, 0xc9f6, 0xbbc4, 0x72e0, 0x60e4, 0xd9c2, 
    0x44f1, 0xe5d3, 0xd9d7, 0xd6d7, 0x6cb3, 0xbeee, 0xf4db, 0xfadb, 0x5cb6, 
    0x60f7, 0x59b6, 0xf6db, 0x5fb6, 0x60b6, 0x69e0, 0xdfc3, 0xdec2, 0x6ee0, 
    0x5ff7, 0x45c5, 0xf8b8, 0x57e4, 0x5be4, 0x40e8, 0xefbd, 0xf3bd, 0xaec5, 
    0xf7e7, 0xf4e7, 0xadc5, 0x40a1, 0xc0eb, 0xe5bf, 0xe6bf, 0xc4eb, 0xe2bf, 
    0xcec1, 0xc5eb, 0xe4bf, 0x5cc6, 0xdac2, 0xd1c1, 0x5df7, 0xddc3, 0xdac3, 
    0x53f3, 0xe0c3, 0xbdc4, 0x6ec6, 0x5ef7, 0x61f7, 0x70f9, 0xc3f8, 0xa4cb, 
    0x49b6, 0xe7b8, 0x5ae0, 0xf1f0, 0xb7ee, 0xd8b1, 0xc6d7, 0xd2c2, 0xd4db, 
    0x43e0, 0xfddf, 0x48e4, 0xf5f2, 0xe4b8, 0xe7f0, 0xb0e8, 0xb8b3, 0x45c6, 
    0xb2b6, 0x5ebe, 0xb4c5, 0xaee8, 0x50c0, 0x4bc0, 0xf9c3, 0x47c6, 0xc5c4, 
    0xb5c5, 0xb5ef, 0xd4c4, 0xbef5, 0xbbf5, 0x68c5, 0x69c5, 0xc5c5, 0xc6c5, 
    0x53c6, 0xefe0, 0xb1ef, 0x65f9, 0x41ab, 0x47d4, 0x6db9, 0xd5e0, 0xdcbb, 
    0xa3c2, 0x41f8, 0xa9e4, 0x71c6, 0x74e8, 0xf5c3, 0xdeee, 0x6cf3, 0xdcee, 
    0x40a1, 0xcaf8, 0x7bbe, 0xdbe8, 0x5bf8, 0x40a1, 0x40a1, 0x74c6, 0x71bf, 
    0x7bec, 0x4dc6, 0x7cec, 0x40a1, 0x6aef, 0x43c2, 0x6cef, 0x5cf9, 0x42c2, 
    0xd0f3, 0x58f8, 0xc7f1, 0xcef9, 0xbfc5, 0xaef7, 0xadf6, 0xb2f7, 0x58c3, 
    0x54c3, 0xc3f1, 0x56c3, 0x4ac4, 0xc7f3, 0xc8f3, 0xd6f3, 0xcbf3, 0x49c4, 
    0x40a1, 0xcdf3, 0xcef3, 0x4bc4, 0x40a1, 0xcff3, 0x40a1, 0x6bf5, 0x73c6, 
    0xcfc4, 0x6ef5, 0x76f5, 0x40a1, 0x40a1, 0xb4f7, 0x5fc5, 0xaef6, 0x60c5, 
    0xaff6, 0xaff7, 0xc0c5, 0xabf7, 0xc1c5, 0x40a1, 0x40a1, 0xedc5, 0xebc5, 
    0x57f8, 0xd7f8, 0xdebb, 0xdfbb, 0xb6c5, 0x40a1, 0x56ec, 0x69c6, 0x7cf1, 
    0xfdc2, 0x40a1, 0x40a1, 0xdae4, 0xebbb, 0x75be, 0xbcf6, 0x40a1, 0x40a1, 
    0x66c0, 0x4ec3, 0xb9f1, 0x5cc5, 0xc1f3, 0x62c6, 0xd4f8, 0xeac5, 0x79be, 
    0xd9e8, 0x4cc6, 0x52c3, 0x69ef, 0x53c3, 0x5ec5, 0x57c5, 0xe4c5, 0x4bc3, 
    0xb9c5, 0x76f7, 0x49d4, 0xa6b9, 0xecbb, 0x77be, 0xd4e8, 0x67c0, 0x68c0, 
    0x70ec, 0xbbf1, 0x40a1, 0x4bc6, 0x55f8, 0x63c6, 0xf2bb, 0xa3be, 0xdded, 
    0xa8b9, 0xb1ec, 0xafec, 0x47c2, 0x51c4, 0xefc3, 0x65c5, 0xefc5, 0x4cc2, 
    0x4ac2, 0x4bc2, 0x5cc3, 0xe0f1, 0x40a1, 0x75c6, 0xb6f5, 0xb4f5, 0x76f8, 
    0x66c5, 0xb4ef, 0x5ec3, 0xd3c4, 0xc4c5, 0x7bf8, 0xb8ec, 0x4dc2, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40fa, 
    0x41fa, 0x42fa, 0x43fa, 0x44fa, 0x45fa, 0x46fa, 0x47fa, 0x48fa, 0x49fa, 
    0x4afa, 0x4bfa, 0x4cfa, 0x4dfa, 0x4efa, 0x4ffa, 0x50fa, 0x51fa, 0x52fa, 
    0x53fa, 0x54fa, 0x55fa, 0x56fa, 0x57fa, 0x58fa, 0x59fa, 0x5afa, 0x5bfa, 
    0x5cfa, 0x5dfa, 0x5efa, 0x5ffa, 0x60fa, 0x61fa, 0x62fa, 0x63fa, 0x64fa, 
    0x65fa, 0x66fa, 0x67fa, 0x68fa, 0x69fa, 0x6afa, 0x6bfa, 0x6cfa, 0x6dfa, 
    0x6efa, 0x6ffa, 0x70fa, 0x71fa, 0x72fa, 0x73fa, 0x74fa, 0x75fa, 0x76fa, 
    0x77fa, 0x78fa, 0x79fa, 0x7afa, 0x7bfa, 0x7cfa, 0x7dfa, 0x7efa, 0xa1fa, 
    0xa2fa, 0xa3fa, 0xa4fa, 0xa5fa, 0xa6fa, 0xa7fa, 0xa8fa, 0xa9fa, 0xaafa, 
    0xabfa, 0xacfa, 0xadfa, 0xaefa, 0xaffa, 0xb0fa, 0xb1fa, 0xb2fa, 0xb3fa, 
    0xb4fa, 0xb5fa, 0xb6fa, 0xb7fa, 0xb8fa, 0xb9fa, 0xbafa, 0xbbfa, 0xbcfa, 
    0xbdfa, 0xbefa, 0xbffa, 0xc0fa, 0xc1fa, 0xc2fa, 0xc3fa, 0xc4fa, 0xc5fa, 
    0xc6fa, 0xc7fa, 0xc8fa, 0xc9fa, 0xcafa, 0xcbfa, 0xccfa, 0xcdfa, 0xcefa, 
    0xcffa, 0xd0fa, 0xd1fa, 0xd2fa, 0xd3fa, 0xd4fa, 0xd5fa, 0xd6fa, 0xd7fa, 
    0xd8fa, 0xd9fa, 0xdafa, 0xdbfa, 0xdcfa, 0xddfa, 0xdefa, 0xdffa, 0xe0fa, 
    0xe1fa, 0xe2fa, 0xe3fa, 0xe4fa, 0xe5fa, 0xe6fa, 0xe7fa, 0xe8fa, 0xe9fa, 
    0xeafa, 0xebfa, 0xecfa, 0xedfa, 0xeefa, 0xeffa, 0xf0fa, 0xf1fa, 0xf2fa, 
    0xf3fa, 0xf4fa, 0xf5fa, 0xf6fa, 0xf7fa, 0xf8fa, 0xf9fa, 0xfafa, 0xfbfa, 
    0xfcfa, 0xfdfa, 0xfefa, 0x40fb, 0x41fb, 0x42fb, 0x43fb, 0x44fb, 0x45fb, 
    0x46fb, 0x47fb, 0x48fb, 0x49fb, 0x4afb, 0x4bfb, 0x4cfb, 0x4dfb, 0x4efb, 
    0x4ffb, 0x50fb, 0x51fb, 0x52fb, 0x53fb, 0x54fb, 0x55fb, 0x56fb, 0x57fb, 
    0x58fb, 0x59fb, 0x5afb, 0x5bfb, 0x5cfb, 0x5dfb, 0x5efb, 0x5ffb, 0x60fb, 
    0x61fb, 0x62fb, 0x63fb, 0x64fb, 0x65fb, 0x66fb, 0x67fb, 0x68fb, 0x69fb, 
    0x6afb, 0x6bfb, 0x6cfb, 0x6dfb, 0x6efb, 0x6ffb, 0x70fb, 0x71fb, 0x72fb, 
    0x73fb, 0x74fb, 0x75fb, 0x76fb, 0x77fb, 0x78fb, 0x79fb, 0x7afb, 0x7bfb, 
    0x7cfb, 0x7dfb, 0x7efb, 0xa1fb, 0xa2fb, 0xa3fb, 0xa4fb, 0xa5fb, 0xa6fb, 
    0xa7fb, 0xa8fb, 0xa9fb, 0xaafb, 0xabfb, 0xacfb, 0xadfb, 0xaefb, 0xaffb, 
    0xb0fb, 0xb1fb, 0xb2fb, 0xb3fb, 0xb4fb, 0xb5fb, 0xb6fb, 0xb7fb, 0xb8fb, 
    0xb9fb, 0xbafb, 0xbbfb, 0xbcfb, 0xbdfb, 0xbefb, 0xbffb, 0xc0fb, 0xc1fb, 
    0xc2fb, 0xc3fb, 0xc4fb, 0xc5fb, 0xc6fb, 0xc7fb, 0xc8fb, 0xc9fb, 0xcafb, 
    0xcbfb, 0xccfb, 0xcdfb, 0xcefb, 0xcffb, 0xd0fb, 0xd1fb, 0xd2fb, 0xd3fb, 
    0xd4fb, 0xd5fb, 0xd6fb, 0xd7fb, 0xd8fb, 0xd9fb, 0xdafb, 0xdbfb, 0xdcfb, 
    0xddfb, 0xdefb, 0xdffb, 0xe0fb, 0xe1fb, 0xe2fb, 0xe3fb, 0xe4fb, 0xe5fb, 
    0xe6fb, 0xe7fb, 0xe8fb, 0xe9fb, 0xeafb, 0xebfb, 0xecfb, 0xedfb, 0xeefb, 
    0xeffb, 0xf0fb, 0xf1fb, 0xf2fb, 0xf3fb, 0xf4fb, 0xf5fb, 0xf6fb, 0xf7fb, 
    0xf8fb, 0xf9fb, 0xfafb, 0xfbfb, 0xfcfb, 0xfdfb, 0xfefb, 0x40fc, 0x41fc, 
    0x42fc, 0x43fc, 0x44fc, 0x45fc, 0x46fc, 0x47fc, 0x48fc, 0x49fc, 0x4afc, 
    0x4bfc, 0x4cfc, 0x4dfc, 0x4efc, 0x4ffc, 0x50fc, 0x51fc, 0x52fc, 0x53fc, 
    0x54fc, 0x55fc, 0x56fc, 0x57fc, 0x58fc, 0x59fc, 0x5afc, 0x5bfc, 0x5cfc, 
    0x5dfc, 0x5efc, 0x5ffc, 0x60fc, 0x61fc, 0x62fc, 0x63fc, 0x64fc, 0x65fc, 
    0x66fc, 0x67fc, 0x68fc, 0x69fc, 0x6afc, 0x6bfc, 0x6cfc, 0x6dfc, 0x6efc, 
    0x6ffc, 0x70fc, 0x71fc, 0x72fc, 0x73fc, 0x74fc, 0x75fc, 0x76fc, 0x77fc, 
    0x78fc, 0x79fc, 0x7afc, 0x7bfc, 0x7cfc, 0x7dfc, 0x54f3, 0x40a1, 0x40a1, 
    0x40a1, 0xbec4, 0x40a1, 0xd9f4, 0xd7f4, 0xd8f4, 0xbbc4, 0x43c5, 0x45c5, 
    0x56f6, 0x44c5, 0x55f6, 0xadc5, 0x60f7, 0xaec5, 0x5df7, 0x62f7, 0x63f7, 
    0x46f8, 0x40a1, 0x5ff7, 0x40a1, 0xc6f8, 0xc4f8, 0xc5f8, 0x5cc6, 0x40a1, 
    0x51f9, 0x50f9, 0x4ff9, 0x70f9, 0x40a1, 0xbef9, 0xabf9, 0x6ec6, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 
    0x40a1, 0x40a1, 0x40a1, 0xc8eb, 0x40a1, 0x40a1, 0xdfc2, 0x40a1, 0x55f3, 
    0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0x40a1, 0xacf9, 0xaea8, 0xeeaa, 
    0x79ad, 0x78ad, 0x63b0, 0x40a1, 0xe8d3, 0x61b0, 0xe9d3, 0x62b0, 0x40a1, 
    0x40a1, 0xdfd7, 0xdbd7, 0x40a1, 0x40a1, 0x6db3, 0xded7, 0xddd7, 0xdcd7, 
    0x6eb3, 0xe0d7, 0xe1d7, 0x40a1, 0x40a1, 0x40a1, 0x43dc, 0x41dc, 0x45dc, 
    0x46dc
};


BOOL APIENTRY DllMain( HANDLE hModule, 
                       DWORD  ul_reason_for_call, 
                       LPVOID lpReserved
					 )
{
    return TRUE;
}

// dest and src can be same 
CHINA_API void  __stdcall  Big2GB(char *dest, const char *src, int len)
{
	const char *end = src + (len & 0X7FFFFFFF);
	for ( ; src < end; ) {
		if (*src == 0) {
			*dest = 0;
			break;
		}

		if( ((BYTE *) src)[0] < 0xA1 || ((BYTE *)src)[1] < 0x40) 
		{
			*dest++ = *src++;
		}
		else //Is BIG5 code
		{
			*(WORD *) dest = pBIGTable[(((BYTE *) src)[0] - 0xA1)*0xBF+ ((BYTE *) src)[1]- 0x40];
			src += 2;
			dest += 2;
		}
	}
}

// dest and src can be same 
CHINA_API  void  __stdcall GB2Big(char *dest, const char *src, int len)
{
	const char *end = src + (len & 0X7FFFFFFF);
	for ( ; src < end; ) {
		if (*src == 0) {
			*dest = 0;
			break;
		}

		if( ((BYTE *) src)[0] < 0xA1 || ((BYTE *) src)[1] < 0xA1)
		{
			*dest++ = *src++;
		}
		else if ( /*((BYTE *) src)[0] > 0xA1 && */ ((BYTE *) src)[0] < 0xB0) {
			*(WORD *) dest = pGBTable[(((BYTE *) src)[0]-0xA1) * 0x5E + ((BYTE *) src)[1] - 0xA1];
			src += 2;
			dest += 2;
		}
		else {
			*(WORD *) dest = pGBTable[(((BYTE *) src)[0]-0xA7) * 0x5E + ((BYTE *) src)[1] - 0xA1];
			src += 2;
			dest += 2;
		}
	}
}

