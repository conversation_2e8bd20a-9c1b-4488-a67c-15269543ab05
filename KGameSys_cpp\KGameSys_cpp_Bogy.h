
#ifdef DEF_BOGY_OEN_080407

#ifndef DEF_UI_CONTENTS_KUMADORI_090227

using namespace KWindowCollector;

/**
*  ���̵� �̺�Ʈ �� �� ���� ��ũ��Ʈ ���� Ŭ����
* \param Window_raid_team_create_E
* \return 
*/
BEGIN(Window_raid_team_create_E)

std::string teamName;   // ����
std::string teamIntro;  // ���Ұ�
bool comfirm;           // ���� Ȯ�� ����
bool teamOpen;          // �� ���� ����    

/**
*  ��ũ��Ʈ�� �ʱ�ȭ �� �� ȣ��ȴ�.
*/
void OnInit()
{
	// �ڽ��� �̸��� ����� �ش�.        
	SetChangedText("captainName", CGame_Character::m_Master->m_pCharaterName);
	comfirm = teamOpen = false;
}


/**
*  ��ũ��Ʈ�� link �� Ŭ���� ȣ��ȴ�.
* \param szName �ش� ��ũ �̸�
* \param param �Ķ� ����
*/
void OnLink(LPCSTR szName, lisp::var param) 
{
	// �ߺ�Ȯ�� ��Ŷ ����
	if (!stricmp(szName, "comfirm")) 
	{
		// �� �̸��� ���´�.
		teamName = UpdateText("teamName");

#ifdef DEF_S34_DIOB_080424 // DEF_S34_DIOB_080424_01
		if( teamName.size() > 12 )
		{
			KWindowCollector::MBoxEx( std::string( "#pos(20 8)��  ��! #n#pos(20 30)������ #color(0 255 0)����12��, �ѱ�6��#n#pos(20 35)#color(255 255 255)�̳��� ��밡���մϴ�." ) );
			return;
		}
#endif

		// 1. ������ ���� ��� ó��
		if (teamName.empty())
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1619)));
			return;
		}

		// 2. ������ �߸��� ���ڰ� ���� ��
		if (!IsValidNameEx(teamName.c_str()))
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1642)));
			return;
		}

		KSocket::WritePacketAutoCrc(C2S_NPC, "dbbs", View_Script::m_nNpcID, 0, A_CHECK_ARMY_NAME, teamName.c_str());
	}
	// ���� ��Ŷ ����
	else if (!stricmp(szName, "create")) 
	{
		// �� �Ұ��� ���´�.
		teamIntro = UpdateText("teamIntro");

		// 1. �� ���� ���� ���
		if (UpdateText("teamName").empty())
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1619)));
			return;
		}

		// 2. ���Ұ��� ���� ���
		if (teamIntro.empty())
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1635)));
			return;
		}

		// 3. �̸� Ȯ���� ���� �ʾ��� �� ó��
		if (!comfirm || UpdateText("teamName") != teamName)
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1618)));
			return;
		}

		// 4. ���Ұ��� ���ڰ� �߸������� ���
		if (!IsValidNameEx(teamIntro.c_str()))
		{
			KWindowCollector::MBoxEx(std::string(KMsg::Get(1642)));
			return;
		}

		// 4. �� ���� ���� ����
		// ��� ���� �� ��
		KWindowCollector::MBoxEx(std::string(KMsg::Get(1620)),
			MBOXEX_ACTION_2,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO,
			KMsg::Get(742), KMsg::Get(743));
	}
	// ��� �� ��Ÿ ó��
	else
		Close();
}

/**
*  ���� ��Ŷ ó��
* \date 2008-04-10
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p)
{
	// ��� �ڵ�
	int resultCode = 0;
	p = ReadPacket(p, "b", &resultCode);

	switch (resultCode)
	{
	case AMSG_NO:
		// ������ ������ �����
		KWindowCollector::MBoxEx(KMsg::Make(1617, teamName.c_str()));
		break;
	case ASMG_OK:
		// ��� ���� �� ��
		KWindowCollector::MBoxEx(KMsg::Make(1616, teamName.c_str()),
			MBOXEX_ACTION_1,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO);
		break;
	case A_CREATE_ARMY_COMPLETE:
		POINT pt = {140, 100};
		// �� ���� ����
		KWindowCollector::MBoxEx(KMsg::Make(1621, teamName.c_str()), 
			MBOXEX_ACTION_NONE, NULL, NULL,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_OK,
			NULL, NULL, &pt);
		// �Ϸᰡ �Ǹ� �ڽ��� �ݾ� �ش�.
		Close();
		break;
	}

	return p;
}

/**
*  �̸� ����
* \date 2008-04-10
*/
std::string UpdateText(char* name)
{
	CHAR buf[MAX_PATH] = {0};

	// �� �̸��� ���´�.
	GetText(name, buf, MAX_PATH);
	return std::string(buf);
}

/**
*  �� ������ ȣ��ȴ�.
*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
* \date 2008-04-10
*/
void OnPaint()
{
#ifndef DEF_UI_RENEWALL_081016
	CHyperView::OnPaint();
#else
	KHyperView::OnPaint();
#endif
}

/**
*  ������ ��ȿȭ ������ �߻��ϸ� ȣ��ȴ�.
* \date 2008-04-10
* \param hdc ������ dc
* \param prcClip ���� ����?
*/
void OnPaint(HDC hdc, LPRECT prcClip)
{
	KHyperView::OnPaint(hdc, prcClip);
}

/**
*  ACTION_TEAM_NAME_REGISTER mbox �� ok �Ǵ� cancel Ŭ���� ȣ��
* \date 2008-04-10
* \param okOrCancel false : cancel Ŭ�� true : ok Ŭ��
*/
static void OnMboxYesNo(int actionType_, bool result_, char* called_)
{
	Window_raid_team_create_E* called = 0;

	if (called_)
		called = (Window_raid_team_create_E*)called_;

	switch(actionType_)
	{
	case MBOXEX_ACTION_1:
		// ���� ��� or Ȯ��
		called->comfirm = result_;
		break;
	case MBOXEX_ACTION_2:
		// ���� ����� or �����
		called->teamOpen = result_;
		KSocket::WritePacketAutoCrc(C2S_NPC, "dbbssb", View_Script::m_nNpcID, 0, A_CREATE_ARMY, 
			called->teamName.c_str(), called->teamIntro.c_str(), result_);
		break;
	}
}

END

/**
*  �� �Ұ� ������ ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-14
* \param Window_raid_team_intro_edit_E 
* \return 
*/
BEGIN(Window_raid_team_intro_edit_E)

HyperElement::CEdit* m_edit;

/**
*  ��ũ��Ʈ�� �ʱ�ȭ �� �� ȣ��ȴ�.
*/
void OnInit()
{
	m_edit = (HyperElement::CEdit*) FindElement("intro");
}

/**
*  ���� ��Ŷ ó��
* \date 2008-04-10
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p)
{
	char* text = 0;
	p = ReadPacket(p, "s", &text);
	m_edit->SetText((LPCSTR)text);
	return p;
}

void OnLink(LPCSTR szName, lisp::var varParameter)
{
	// ���� Ȯ��
	if (!stricmp(szName, "ok"))
	{
		char text[MAX_PATH] = {0};

		// ���� ����Ʈ�� ������ ������ ����
		if (m_edit)
		{
			m_edit->GetText(text, MAX_PATH);

			if (text[0])
			{
				// ���Ұ��� ���ڰ� �߸������� ���
				if (!IsValidNameEx(text))
				{
					KWindowCollector::MBoxEx(std::string(KMsg::Get(1642)));
					return;
				}

				// ���� ����
				KSocket::WritePacketAutoCrc(C2S_NPC, "dbbs", View_Script::m_nNpcID, 0, A_ARMY_LEGEND_EDIT, text);
			}
		}

		return;
	}

	Close();
}

END


/**
*  ���̵� �̺�Ʈ�� �� ����Ʈ ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-13
* \param Window_raid_team_list_E 
* \return 
*/
BEGIN(Window_raid_team_list_E)

enum
{
	LIST_FRONT          = 0,
	LIST_MIDDLE         = 1,
	LIST_BACK           = 2,        

	LIST_END,
};

#ifdef DEF_S34_DIOB_080424 // DEF_S34_DIOB_080424_28 // ����� ������ ���̵� �̺�Ʈ�� ���� ���¶� �ʸ��纴�� �������� �ٲ��.
#   ifdef DEF_ILRYER_EVENT_OEN_090205
static const int allListMember = 90;    // �� ��� ���Ѽ�
static const int maxListMember = 90;    // ����Ʈ ��� ���Ѽ�
#   else
static const int allListMember = 100;   // �� ��� ���Ѽ�
static const int maxListMember = 100;   // ����Ʈ ��� ���Ѽ� ( �϶��̾�� 90 ������ ���� �Ѵ�. )
#   endif
static const int minListMember = 0;     // ����Ʈ �� ���� �����
#else
static const int allListMember = 200;
static const int maxListMember = 100;   
static const int minListMember = 66;
#endif

HyperElement::CList* m_list[LIST_END];
stArmyMember* m_memberInfo;
int m_memberSize;
int m_sel;
int m_packetType;
std::string m_teamName;    
int m_frontCount;
int m_middleCount;
int m_backCount;

char m_str[4][MAX_PATH];

/**
*  �ʱ�ȭ �Լ�
* \date 2008-04-13
*/
void OnInit()
{
	const std::string listName = "list";

	char str[MAX_PATH] = {0};

	for (int i = 0; i < LIST_END; i++)
	{
		m_list[i] = (HyperElement::CList*) FindElement( (listName + itoa(i + 1, str, 10)).c_str() );
		m_list[i]->m_listbox.m_nMaxElement = maxListMember;
		m_list[i]->SetCurSel(-1);
	}

	// �δ�� ���� ���� �ʱ�ȭ �� �ε�
	memset(m_str, 0, sizeof(m_str));
	strcpy(m_str[E_BMC_Master], KMsg::Get(1645));
	strcpy(m_str[E_BMC_SubMaster], KMsg::Get(1646));
	strcpy(m_str[E_BMC_General], KMsg::Get(1647));
	strcpy(m_str[E_BMC_BAN], KMsg::Get(1648));

	m_memberInfo = 0;
	m_memberSize = m_packetType = m_frontCount = m_middleCount = m_backCount = 0;
	m_sel = -1;

	SetChangedText("frontNow", 0);
	SetChangedText("middleNow", 0);
	SetChangedText("backNow", 0);
	SetChangedText("frontMax", m_frontCount);
	SetChangedText("middleMax", m_middleCount);
	SetChangedText("backMax", m_backCount);
}

/**
*  ���� ��Ŷ ó��
* \date 2008-04-13
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p)
{
	switch(m_packetType)
	{
	case 0:
		{
			int type = 0;
			p = ReadPacket(p, "b", &type);

			switch(type)
			{
				// ��� ��ü ���� ����
			case A_MY_ARMY_INFO_ANSWER:
				{
					int teamClass = 0;
					char* teamName = 0;
					p = ReadPacket(p, "dsbbb", &teamClass, &teamName, &m_frontCount, &m_middleCount, &m_backCount);
					m_teamName = teamName;

#ifdef DEF_S34_DIOB_080424 // DEF_S34_DIOB_080424_28
					SetChangedText( "frontMax", m_frontCount);
					SetChangedText( "middleMax", m_middleCount);
					SetChangedText( "backMax", m_backCount);
#endif

					// ���� �Է�
					OnCaption(teamName);
				}
				break;
				// ��� ��, ��, �� �δ� ���� ����
			case A_MY_ARMY_MEMBER_INFO_ANSWER:
				{
					int count = 0, armyType = 0;
					int addMemSize = 0, beforeMemSize = 0;

					// �ƹ� Ÿ�԰� ������ �д´�.
					p = ReadPacket(p, "bd", &armyType, &count);

					// 0�ϰ�� ����
					if (!count) break;

					// �޸� ������ ���
					beforeMemSize = sizeof(stArmyMember) * m_memberSize;
					addMemSize = sizeof(stArmyMember) * count;

					// �޸𸮸� �߰��� ��� �ʱ�ȭ �Ѵ�.
					m_memberInfo = (stArmyMember*)realloc(m_memberInfo, beforeMemSize + addMemSize);
					char* ptr = (char*)m_memberInfo + beforeMemSize;
					for (int i = 0; i < addMemSize; i++) *ptr++ = 0;

					// ���� �޸��� �ּҿ� ���� ���� �� ����� �ø���.
					p = ReadPacket(p, "m", m_memberInfo + m_memberSize, addMemSize);
					m_memberSize += count;

					// �ο� �����
					DrawPerson( armyType);
				}
				break;
			}
		}
		break;
	case 1:            
		{
			// ���� ��� ��Ŷ ����
			int result = 0;
			p = ReadPacket(p, "b", &result);

			switch(result)
			{
			case A_DISCHARGE_COMPLETE:
				// ���� �߹� ����
				KWindowCollector::MBoxEx(KMsg::Make(1624, m_memberInfo[GetMemberCount()].m_PlayerName));
				// �߹�� ������ �߹��ڷ� ���¸� �ٲ��ش�.
				m_memberInfo[GetMemberCount()].m_ArmyClass = E_BMC_BAN;
				DrawPerson(m_memberInfo[GetMemberCount()].m_PlayerClass);
				break;
			case A_DELETE_ARMY_COMPLETE:
				// �ڽ��� ���̹Ƿ� �ݾ� ������.
				Close();
				// �� ��ü ����
				KWindowCollector::MBoxEx(KMsg::Make(1626, m_teamName.c_str()));
				break;
			case A_LEAVE_COMPLETE:
				// �̿��� �ڽ��� ���̹Ƿ� �ݾ� ������.
				Close();
				// �� Ż�� ����
				KWindowCollector::MBoxEx(KMsg::Make(1628, m_teamName.c_str()));
				break;
			case A_ARMY_LEGEND_EDIT_COMPLETE:
				// �� �Ұ� ���� ����
				KWindowCollector::CloseReservation("raid_team_intro_edit_E");
				KWindowCollector::MBoxEx(std::string(KMsg::Get(1629)));
				break;
			case A_EDIT_ARMY_MAX_COUNT_COMPLETE:
				// �� �ִ�� ���� ����
				//KWindowCollector::MBoxEx(std::string(KMsg::Get(1629)));
				SetChangedText("frontMax", m_frontCount);
				SetChangedText("middleMax", m_middleCount);
				SetChangedText("backMax", m_backCount);
				refresh();
				break;
			}

		}
		break;
	}

	return p;
}

/**
*  ��� ������ ����Ѵ�.
* \date 2008-04-16
* \param type_ ����� ��� Ÿ�� ����Ʈ ���
* \param startPoint_ ����� ���� �ε���
* \param endPoint_ ����� �� �ε���
* \param insert ?
*/
void DrawPerson(int type_)
{
	int count = 0, maxCount = 0;
	HyperElement::CList* list = NULL;
	char text[MAX_PATH] = { 0 };
	char countName[MAX_PATH] = { 0 };
	char maxCountName[MAX_PATH] = { 0 };
	int selected = -1;

	if (m_sel != -1 && m_list[m_sel]->GetCurSel() != -1)
	{
		selected = m_list[m_sel]->GetCurSel();
		m_list[m_sel]->AllClear();
	}

	// ���⼭ ������ ����, �߰�, ���� �ڸ��� ���Ѵ�.( diob)
	// ����� ����Ʈ ����
	switch(type_)
	{
		// ����� ���� �δ��̴�
	case PC_KNIGHT:
		list = (HyperElement::CList*)m_list[LIST_FRONT];
		maxCount = m_frontCount;
		strcpy(countName, "frontNow");
		strcpy(maxCountName, "frontMax");            
		break;
		// �ü��� �߰� �δ��̴�.
	case PC_ARCHER:
		list = (HyperElement::CList*)m_list[LIST_MIDDLE];
		maxCount = m_middleCount;
		strcpy(countName, "middleNow");
		strcpy(maxCountName, "middleMax");
		break;
		// �ּ���� ���� �δ��̴�.
	case PC_MAGE:
		list = (HyperElement::CList*)m_list[LIST_BACK];
		maxCount = m_backCount;
		strcpy(countName, "backNow");
		strcpy(maxCountName, "backMax");
		break;
	}

	// ���� ����Ʈ �Է�
	for (int i = 0; i < m_memberSize; i++)
	{
		if (type_ == m_memberInfo[i].m_PlayerClass
#ifdef DEF_THIEF_SIBMAN_DIOB_081002
			|| ( type_ == 2 && m_memberInfo[i].m_PlayerClass == PC_THIEF)
#endif
			)
		{
			InsertString(list, m_str[m_memberInfo[i].m_ArmyClass], m_memberInfo[i].m_GuildName, 
				itoa(m_memberInfo[i].m_Level, text, 10), m_memberInfo[i].m_PlayerName);

			// �߹��ڴ� ī���� ���� �ʴ´�.
			if (m_memberInfo[i].m_ArmyClass != E_BMC_BAN)
				count++;
		}
	}

	// �� �� ����
	SetChangedText(countName, count);
	SetChangedText(maxCountName, maxCount);

	if (selected != -1)
		m_list[m_sel]->SetCurSel(selected);

	// ���� ��ħ
	refresh();
}

/**
*  ����Ʈ�� ���� ��� �ε����� ���
* \date 2008-04-13
* \param listType_ ����Ʈ Ÿ�� 
* \param getCur_ ����Ʈ ���� ������
* \return 
*/
int GetMemberCount()
{
	int count = 0;
	int type = 0;

	switch(m_sel)
	{
	case LIST_FRONT:
		type = PC_KNIGHT;
		break;
	case LIST_MIDDLE:
		type = PC_ARCHER;
		break;
	case LIST_BACK:
		type = PC_MAGE;
		break;
	}

	for (int i = 0; i < m_memberSize; i++)
	{
		if (m_memberInfo[i].m_PlayerClass == type
#ifdef DEF_THIEF_SIBMAN_DIOB_081002
			|| ( type == 2 && m_memberInfo[ i].m_PlayerClass == PC_THIEF)
#endif
			)                
		{
			if (m_list[m_sel]->GetCurSel() == count)
				return i;
			else
				count++;
		}
	}

	return 0;
}

//! ����Ʈ �ڽ��� �߰�
void InsertString(HyperElement::CList* list_, char* a_, char* b_, char* c_, char* d_)
{
	std::string str;
	char temp[MAX_PATH] = {0};

	// ���� ���
#ifdef DEF_VER_ENG_DIOB_080416
	list_->MakeString(a_, temp, 90);
#else
	list_->MakeString(a_, temp, 40);
#endif
	str += temp;
	memset(temp, 0, MAX_PATH);

	// ���� ���
	list_->MakeString(b_, temp, 130);
	str += temp;
	memset(temp, 0, MAX_PATH);

	// ���� ���
	list_->MakeString(c_, temp, 30);
	str += temp;
	memset(temp, 0, MAX_PATH);

	// ĳ���� ���
	list_->MakeString(d_, temp, 115);
	str += temp;
	memset(temp, 0, MAX_PATH);

	list_->AddString(str.c_str(), TEXTCOLOR_GENERAL);
}

void OnDelete()
{
	SAFE_DELETE(m_memberInfo);
}

/**
*  ��ũ��Ʈ�� link �� Ŭ���� ȣ��ȴ�.
* \param szName �ش� ��ũ �̸�
* \param param �Ķ� ����
*/
void OnLink(LPCSTR szName, lisp::var param) 
{
	// ���� ����
	if (!stricmp(szName, "go")) 
	{
		// ������� ���� ����

	}
	// ��� ��ü
	else if (!stricmp(szName, "destroy")) 
	{
		// ��� ��ü Ȯ�� �ڽ�
		KWindowCollector::MBoxEx(KMsg::Make(1625, m_teamName.c_str()),
			MBOXEX_ACTION_2,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO);
	}
	// ��� �Ұ�
	else if (!stricmp(szName, "intro")) 
	{
		// ��� �Ұ� ��û
		KSocket::WritePacketAutoCrc(C2S_NPC, "dbb", View_Script::m_nNpcID, 0, A_ARMY_LEGEND_INFO);
	}
	// ��� �߹�
	else if (!stricmp(szName, "ak"))
	{
		// ��� �߹� Ȯ�� �ڽ�
		if (m_sel != -1 && m_list[m_sel]->GetCurSel() != -1)
			KWindowCollector::MBoxEx(KMsg::Make(1623, m_memberInfo[GetMemberCount()].m_PlayerName),
			MBOXEX_ACTION_1,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO);
	}
	// ��� Ż��
	else if (!stricmp(szName, "ag")) 
	{
		// ��� Ż�� Ȯ�� �ڽ�
		KWindowCollector::MBoxEx(KMsg::Make(1627, m_teamName.c_str()),
			MBOXEX_ACTION_3,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO);
	}
	// ���� �δ� +
	else if (!stricmp(szName, "frontMinus")) 
	{
		if (m_frontCount + m_middleCount + m_backCount < allListMember && m_frontCount++)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_KNIGHT, m_frontCount);
	}
	// ���� �δ� -
	else if (!stricmp(szName, "frontPlus")) 
	{
		if (m_frontCount > minListMember && m_frontCount--)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_KNIGHT, m_frontCount);   
	}
	// �߰� �δ� +
	else if (!stricmp(szName, "middleMinus")) 
	{
		if (m_frontCount + m_middleCount + m_backCount < allListMember && m_middleCount++)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_ARCHER, m_middleCount); // ���⼭ PC_ARCHER�� �߰ߺδ�(2)�� �ǹ̷� ���ε�...Ŭ������ ����
	}
	// �߰� �δ� -
	else if (!stricmp(szName, "middlePlus"))
	{
		if (m_middleCount > minListMember && m_middleCount--)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_ARCHER, m_middleCount); // ���⼭ PC_ARCHER�� �߰ߺδ�(2)�� �ǹ̷� ���ε�...Ŭ������ ����
	}
	// ���� �δ� +
	else if (!stricmp(szName, "backMinus")) 
	{
		if (m_frontCount + m_middleCount + m_backCount < allListMember && m_backCount++)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_MAGE, m_backCount);
	}
	// ���� �δ� -
	else if (!stricmp(szName, "backPlus")) 
	{
		if (m_backCount > minListMember && m_backCount--)
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbbb", View_Script::m_nNpcID, 0, A_EDIT_ARMY_MAX_COUNT, PC_MAGE, m_backCount);            
	}
	// ��� �� ��Ÿ ó��
	else
		Close();
}

/**
*  �� ������ ȣ��ȴ�.
*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
* \date 2008-04-10
*/
void OnPaint()
{
	int sel = m_sel;

	// ������ 1���� �ǵ��� �Ѵ�.
	if (m_sel != LIST_FRONT && m_list[LIST_FRONT]->GetCurSel() != -1) 
		sel = LIST_FRONT;            
	else if (m_sel != LIST_MIDDLE && m_list[LIST_MIDDLE]->GetCurSel() != -1) 
		sel = LIST_MIDDLE;
	else if (m_sel != LIST_BACK && m_list[LIST_BACK]->GetCurSel() != -1) 
		sel = LIST_BACK;

	if (sel != m_sel)
	{
		if (m_sel != -1)
			m_list[m_sel]->SetCurSel(-1);
		m_sel = sel;
	}

#ifndef DEF_UI_RENEWALL_081016
	CHyperView::OnPaint();
#else
	KHyperView::OnPaint();
#endif
}

void OnSetType(BYTE byType)
{
	m_packetType = byType;
}

static void OnMboxYesNo(int type_, bool result_, char* called_)
{
	Window_raid_team_list_E* called = 0;

	if (called_)
		called = (Window_raid_team_list_E*)called_;

	switch(type_)
	{
	case MBOXEX_ACTION_1:
		if (result_)
			// �߹� ��Ŷ ����
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbs", View_Script::m_nNpcID, 0, A_DISCHARGE, called->m_memberInfo[called->GetMemberCount()].m_PlayerName);
		return;
	case MBOXEX_ACTION_2:
		if (result_)
			// ��ü ��Ŷ ����
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbb", View_Script::m_nNpcID, 0, A_DELETE_ARMY);
		return;
	case MBOXEX_ACTION_3:
		if (result_)
			// Ż�� ��Ŷ ����
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbb", View_Script::m_nNpcID, 0, A_LEAVE_ARMY);
		return;
	}
}
END

/**
*  ���̵� �̺�Ʈ �� ��ġ ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-14
* \param Window_raid_team_search_E 
* \return 
*/
BEGIN(Window_raid_team_search_E)

enum
{
	LIST_OPEN_TEAM,
	LIST_SEARCH_TEAM,

	LIST_END,
};


int m_sel;
int m_packetType;     
stArmy* m_openTeamData;
stArmy* m_searchTeamData;
HyperElement::CList* m_list[LIST_END];
static const int maxListSize = 200;
int m_openTeamSize;
int m_searchTeamSize;

/**
*  �ʱ�ȭ �Լ�
* \date 2008-04-13
*/
void OnInit()
{
	const std::string listName = "list";

	char str[MAX_PATH] = {0};

	for (int i = 0; i < LIST_END; i++)
	{
		m_list[i] = (HyperElement::CList*) FindElement( (listName + itoa(i + 1, str, 10)).c_str() );
		m_list[i]->m_listbox.m_nMaxElement = maxListSize;
		m_list[i]->SetCurSel(-1);
	}

	m_packetType = m_openTeamSize = m_searchTeamSize = 0;
	m_openTeamData = 0;
	m_searchTeamData = 0;
	m_sel = -1;
}

void OnSetType(BYTE byType)
{
	m_packetType = byType;
}

void OnDelete()
{
	SAFE_DELETE(m_openTeamData);
	SAFE_DELETE(m_searchTeamData);
}


/**
*  ���� ��Ŷ ó��
* \date 2008-04-13
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p) // DEF_S34_DIOB_080424_21
{
	switch(m_packetType)
	{
		// ���� ����Ʈ �޽��� ó��
	case 0:            
		{
			// �̹� �ִٸ� �����Ѵ�.
			if (m_openTeamData) m_list[LIST_OPEN_TEAM]->AllClear();
			SAFE_DELETE(m_openTeamData);
			m_openTeamSize = 0;

			// ���� ����Ʈ �Է�
			p = ReadPacket(p, "d", &m_openTeamSize);
			if (!m_openTeamSize) break;

			m_openTeamData = (stArmy*)malloc(sizeof(stArmy) * m_openTeamSize);
			memset(m_openTeamData, 0, sizeof(stArmy) * m_openTeamSize);
			p = ReadPacket(p, "m", m_openTeamData, sizeof(stArmy) * m_openTeamSize);

			for (int i = 0; i < m_openTeamSize; i++)
			{
				InsertString(m_list[LIST_OPEN_TEAM], m_openTeamData[i].m_Name, m_openTeamData[i].m_CommanderName, m_openTeamData[i].m_Legend);
			}

		}
		break;
		// �˻� ����Ʈ �޽��� ó��
	case 1:
		{
			// �̹� �ִٸ� �����Ѵ�.
			if (m_searchTeamData) m_list[LIST_SEARCH_TEAM]->AllClear();
			SAFE_DELETE(m_searchTeamData);
			m_searchTeamSize = 0;

			// �˻� ����Ʈ �Է�   
			p = ReadPacket(p, "d", &m_searchTeamSize);
			if (!m_searchTeamSize) break;

			m_searchTeamData = (stArmy*)malloc(sizeof(stArmy) * m_searchTeamSize);
			memset(m_searchTeamData, 0, sizeof(stArmy) * m_searchTeamSize);
			p = ReadPacket(p, "m", m_searchTeamData, sizeof(stArmy) * m_searchTeamSize);

			for (int i = 0; i < m_searchTeamSize; i++)
			{
				InsertString(m_list[LIST_SEARCH_TEAM], m_searchTeamData[i].m_Name, m_searchTeamData[i].m_CommanderName, m_searchTeamData[i].m_Legend);
			}
		}
		break;
		// ��Ÿ �޽��� ó��
	case 2:
		{
			int msgtype = 0;
			p = ReadPacket(p, "b", &msgtype);
			stArmy* army = 0;
			if (m_sel == LIST_OPEN_TEAM) army = m_openTeamData;
			else army = m_searchTeamData;

			switch(msgtype)
			{
			case AMSG_JOIN_UP:
				// ��û �Ϸ�
				KWindowCollector::MBoxEx(KMsg::Make(1631, army[m_list[m_sel]->GetCurSel()].m_Name));
				break;
			case AMSG_EXCESSIVE_NUMBER:
				// �� �ο� �ʰ�
				KWindowCollector::MBoxEx(KMsg::Make(1633, army[m_list[m_sel]->GetCurSel()].m_Name));
				break;
			}
		}
		break;
	} // end switch(m_packetType)

	return p;
}

//! ����Ʈ �ڽ��� �߰�
void InsertString(HyperElement::CList* list_, char* a_, char* b_, char* c_) // DEF_S34_DIOB_080424_21
{
	std::string str;
	char temp[MAX_PATH] = {0};

	// ���� ���
#ifdef DEF_VER_ENG_DIOB_080416
	list_->MakeString(a_, temp, 102);
#else
	list_->MakeString(a_, temp, 100);
#endif
	str += temp;
	memset(temp, 0, MAX_PATH);

	// ������ ���
#ifdef DEF_VER_ENG_DIOB_080416
	list_->MakeString(b_, temp, 129);
#else
	list_->MakeString(b_, temp, 100);
#endif
	str += temp;
	memset(temp, 0, MAX_PATH);

	// �Ұ� ���
#ifdef DEF_VER_ENG_DIOB_080416
	list_->MakeString(c_, temp, 108);
#else
#ifdef DEF_S34_DIOB_080424 // DEF_S34_DIOB_080424_21
	list_->MakeString(c_, temp, 132);
#else
	list_->MakeString(c_, temp, 138);
#endif
#endif
	str += temp;
	memset(temp, 0, MAX_PATH);

	list_->AddString(str.c_str(), TEXTCOLOR_GENERAL);
}

/**
*  ��ũ��Ʈ�� link �� Ŭ���� ȣ��ȴ�.
* \param szName �ش� ��ũ �̸�
* \param param �Ķ� ����
*/
void OnLink(LPCSTR szName, lisp::var param)
{
	// �˻� ��û
	if (!stricmp(szName, "search")) 
	{
		char text[MAX_PATH] = {0};

		// ���� ����Ʈ�� ������ ������ ����
		HyperElement::CEdit* edit = (HyperElement::CEdit*) FindElement("search");

		if (edit)
		{
			edit->GetText(text, MAX_PATH);

			// �˻��� �� ����
			if (text[0])
				KSocket::WritePacketAutoCrc(C2S_NPC, "dbbs", View_Script::m_nNpcID, 0, A_SEARCH_ARMY, text);
		}

	}
	// ���� ��û
	else if (!stricmp(szName, "request"))
	{
		if (m_sel == -1 || m_list[m_sel]->GetCurSel() == -1)
			return;

		stArmy* army = 0;
		if (m_sel == LIST_OPEN_TEAM)
			army = m_openTeamData;
		else
			army = m_searchTeamData;

		// ������ �� ���� ��û ����
		KWindowCollector::MBoxEx(KMsg::Make(1630, army[m_list[m_sel]->GetCurSel()].m_Name),
			MBOXEX_ACTION_1,
			(MBOXEX_CALLBACK)OnMboxYesNo, (char*)this,
			MBOXEX_BOX_TYPE_MODAL | MBOXEX_BOX_TYPE_YESNO);
	}
	// ��� �� ��Ÿ ó��
	else
		Close();
}

static void OnMboxYesNo(int type_, bool result_, char* called_)
{
	Window_raid_team_search_E* called = 0;

	if (called_)
		called = (Window_raid_team_search_E*)called_;

	switch(type_)
	{
		// �� ���� ��û
	case MBOXEX_ACTION_1:
		if (result_)
		{
			if ((called->m_sel == -1) || (called->m_list[called->m_sel]->GetCurSel() == -1))
				return;

			stArmy* army = 0;
			if (called->m_sel == LIST_OPEN_TEAM)
				army = called->m_openTeamData;
			else
				army = called->m_searchTeamData;

			// ���� ��Ŷ ����
			KSocket::WritePacketAutoCrc(C2S_NPC, "dbbs", View_Script::m_nNpcID, 0, A_JOIN, army[called->m_list[called->m_sel]->GetCurSel()].m_Name);
		}
		return;
	}
}

/**
*  �� ������ ȣ��ȴ�.
*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
* \date 2008-04-10
*/
void OnPaint()
{
	int sel = m_sel;

	// ������ 1���� �ǵ��� �Ѵ�.
	if (m_sel != LIST_OPEN_TEAM && m_list[LIST_OPEN_TEAM]->GetCurSel() != -1) 
		sel = LIST_OPEN_TEAM;            
	else if (m_sel != LIST_SEARCH_TEAM && m_list[LIST_SEARCH_TEAM]->GetCurSel() != -1) 
		sel = LIST_SEARCH_TEAM;

	if (sel != m_sel)
	{
		if (m_sel != -1)
			m_list[m_sel]->SetCurSel(-1);
		m_sel = sel;
	}

#ifndef DEF_UI_RENEWALL_081016
	CHyperView::OnPaint();
#else
	KHyperView::OnPaint();
#endif
}

END

/**
*  ���̵� �̺�Ʈ �ڱ� �� ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-14
* \param Window_raid_team_intro_E 
* \return 
*/
BEGIN(Window_raid_team_intro_E)

HyperElement::CList* m_list;
std::string m_armyName;
std::string m_armyCaptainName;
int m_class;
int m_num;

/**
*  �ʱ�ȭ �Լ�
* \date 2008-04-13
*/
void OnInit()
{
	const std::string listName = "list";
	char str[MAX_PATH] = {0};
	m_list = (HyperElement::CList*) FindElement( (listName + itoa(1, str, 10)).c_str() );
	m_list->m_listbox.m_nMaxElement = 2;
	m_list->SetCurSel(-1);
	m_num = m_class = 0;
}

/**
*  ���� ��Ŷ ó��
* \date 2008-04-13
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p)
{

	int msgtype = 0;
	p = ReadPacket(p, "b", &msgtype);

	switch(msgtype)
	{
	case AMSG_NOT_JOIN:
		KWindowCollector::MBoxEx(std::string(KMsg::Get(1636)));
		Close();
		break;
	case A_JOIN_STATE:
		{
			char* text = 0;

			char str[4][MAX_PATH] = { 0 };
			strcpy(str[0], KMsg::Get(1645));
			strcpy(str[1], KMsg::Get(1646));
			strcpy(str[2], KMsg::Get(1647));
			strcpy(str[3], KMsg::Get(1648));

			// �ڽ��� ������ �о ǥ��
			p = ReadPacket(p, "s", &text);
			m_armyName = text;
			p = ReadPacket(p, "s", &text);
			m_armyCaptainName = text;
			p = ReadPacket(p, "db", &m_num, &m_class);

			SetChangedText("teamName", m_armyName.c_str());
			SetChangedText("teamCaptainName", m_armyCaptainName.c_str());
			SetChangedText("teamNum", m_num);
			SetChangedText("teamClass", str[m_class]);
		}
		break;
	}

	return p;
}

/**
*  �� ������ ȣ��ȴ�.
*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
* \date 2008-04-10
*/
void OnPaint()
{
	// ������ �Ұ����ϵ��� �ع�����.
	m_list->SetCurSel(-1);
#ifndef DEF_UI_RENEWALL_081016
	CHyperView::OnPaint();
#else
	KHyperView::OnPaint();
#endif
}

/**
*  ��ũ��Ʈ�� link �� Ŭ���� ȣ��ȴ�.
* \param szName �ش� ��ũ �̸�
* \param param �Ķ� ����
*/
void OnLink(LPCSTR szName, lisp::var param) 
{
	Close();
}

END

/**
*  ���̵� �̺�Ʈ �ð� ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-14
* \param Window_raid_team_time_E 
* \return 
*/
//BEGIN(Window_raid_team_time_E)
class Window_raid_team_time_E : 
	public KHyperViewMoveable
{
public:

	Window_raid_team_time_E() { OnCreate();}
	virtual ~Window_raid_team_time_E() { OnDelete();}
	static KHyperView* CreateObject() {return new Window_raid_team_time_E();}

	//! ���� �ð�
	int m_nTime;
	//! ���� Ŭ���̾�Ʈ �ð�
	int m_nStartClientTime;
	// ǥ�� ��� �ε���
	int m_mobIndex;

	void OnInit()
	{
		// ��ġ ����(��ġ ������ ������ ���� �Ŀ� �Ͽ��� �Ѵ�!!! ����)
		POINT pos;
		pos.x = (LONG)((KWindowCollector::GetViewWidth() - (m_sizeWindow.cx)) / 2.0);
		pos.y = 0; //(LONG)((KWindowCollector::GetViewHeight() / 4 - (m_sizeWindow.cy)) / 2.0);
		OnMove(pos);

		m_mobIndex = 213;
	}

	/**
	*  ���� �� �ʱ�ȭ ����
	*/
	virtual void PreCreate()
	{
		// ���� �ð� �ʱ�ȭ
		m_nTime = m_nStartClientTime = -1;
	}

	/**
	*  ���� ��Ŷ ó��
	* \date 2008-04-13
	* \param p ��Ŷ
	* \return 
	*/
	char* ProcessPacket(char* p)
	{
		// �ð� �Է�
		int nTime = 0;
		p = ReadPacket(p, "d", &nTime);

		// ó�� �ʱ�ȭ ���Ŀ� �������� �Է� �� �ð��� 
		// ���� Ŭ���̾�Ʈ �ð��� 2��(+-) �̻� ���̰� ���� �ð��� �ٲ��ش�.
		if (m_nTime == -1 || nTime > m_nTime + 2 || nTime < m_nTime - 2)
		{
			// �ð��� ����
			m_nTime = nTime;
			// ������ ���� Ŭ���̾�Ʈ �ð�!
			m_nStartClientTime = (int)g_fCurrentTime;
		}

		// �������
		refresh();

		// �ð��� 0 �̵Ǹ� ��ũ��Ʈ�� �ݾ��ش�.
		if (m_nTime == 0)
		{
			KWindowCollector::CloseReservation("raid_team_conquest_E");
			KWindowCollector::CloseReservation("raid_team_time_E");
		}

		return p;
	}

	void UpdateConquest()
	{
		// Ÿ���߿� ��� �ε����� �ִ��� ã�´�.
		CGameTarget* target = CGame_Character::FindGameTargetIndex(CK_MONSTER, m_mobIndex);

		// ����� �����Ѵٸ� �ش� ����� �� �������� ���翡������ ��ŭ�� ǥ���� �ش�.
		if(target)
		{
			float percent = (float)target->m_nCurHP / (float)target->m_nMaxHP * 100.f;
			percent = abs(100 - percent);
#ifdef DEF_S34_DIOB_080424 // DEF_S34_DIOB_080424_30
			KHyperView* view = ( KHyperView* )KWindowCollector::LoadAndFind("raid_team_conquest_E");
#else
			KHyperView* view = KWindowCollector::LoadAndFind("raid_team_conquest_E");
#endif
			if (view) view->OnSetType((BYTE)percent);
		}
		else
		{
			KWindowCollector::CloseReservation("raid_team_conquest_E");
		}
	}

	/**
	*  �� ������ ȣ��ȴ�.
	*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
	*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
	* \date 2008-04-10
	*/
	void OnPaint()
	{
		PaintWithDC();
		InvalidateRect();
	}

	/**
	*  ������ ��ȿȭ ������ �߻��ϸ� ȣ��ȴ�.
	* \date 2008-04-10
	* \param hdc ������ dc
	* \param prcClip ���� ����?
	*/
	void OnPaint(HDC hdc, LPRECT prcClip)
	{
		if (m_nTime < 0) return;

		// client time flow 
		// (Ŭ���̾�Ʈ ��ü������ �ð��� �帣�� �Ѵ�. �ּ� ó���� �ð� �� �帧)
		// 0 ���ϴ� ó�� ���� �ʴ´�.
		if (m_nTime >= 0)
		{
			m_nTime -= ((int)g_fCurrentTime - m_nStartClientTime);
			m_nStartClientTime = (int)( g_fCurrentTime );
			if (m_nTime < 0) m_nTime = 0;
		}

		// �ʴ����� 60���� ���� ���� ������ �������� �ʷ� ����
		SetChangedText("time", m_nTime / 60, m_nTime % 60);
		//UpdateConquest();

		KHyperView* view = (KHyperView*)KWindowCollector::Find("raid_team_conquest_E");           
		if (view) view->OnInit();

		// �ʱ�ȭ�� �Ǳ� ���� ȣ��ǹǷ�
		KHyperView::OnPaint(hdc, prcClip);        
	}

	/**
	*  ESC Ű�� ����� ����Ʈ�� ĵ���� ȣ��ȴ�. �̸� ���´�.
	* \date 2008-04-17
	*/
	void OnCancel() {}

};
//END

/**
*  ���̵� �̺�Ʈ ������ ��ũ��Ʈ ���� Ŭ����
* \date 2008-04-14
* \param Window_raid_team_conquest_E 
* \return 
*/
BEGIN(Window_raid_team_conquest_E)

void OnInit()
{
	// ��ġ ����(��ġ ������ ������ ���� �Ŀ� �Ͽ��� �Ѵ�!!! ����)
	POINT pos;
	KHyperView* view = (KHyperView*)KWindowCollector::Find("raid_team_time_E");
	if(view)
	{
		// ������ �׵θ� 6��ŭ ���� �ش�.
		pos.x = view->m_ptWindow.x + view->m_sizeWindow.cx + 6;
		pos.y = view->m_ptWindow.y + view->m_sizeWindow.cy - m_sizeWindow.cy;
		OnMove(pos);
	}
}

/**
*  ���� ��Ŷ ó��
* \date 2008-04-13
* \param p ��Ŷ
* \return 
*/
char* ProcessPacket(char* p)
{
	// ������
	int conquest = 0;
	p = ReadPacket(p, "d", &conquest);

	SetChangedText("conquest", conquest);

	OnInit();
	PaintWithDC();
	InvalidateRect();

	return p;
}

/**
*  �� ������ ȣ��ȴ�.
*  (���� ���� ����Ʈ(�Ʒ� ����Ʈ �Լ�)�� ������ ��ȿȭ �ɶ� ȣ�������
*  �� ����Ʈ �Լ��� �� ������ ȣ��ȴ�. rander�� �����ϸ� �����ϱ� �������̴�.
* \date 2008-04-10
*/
void OnPaint()
{
	CHyperView::OnPaint();
}

/**
*  ����� �������� ���ȴ�.
* \date 2008-04-17
* \param byType ������ �ۼ�Ƽ�� 
*/
void OnSetType(BYTE byType)
{
	SetChangedText("conquest", byType);
	InvalidateRect();
}

/**
*  ESC Ű�� ����� ����Ʈ�� ĵ���� ȣ��ȴ�. �̸� ���´�.
* \date 2008-04-17
*/
void OnCancel() {}

END

#endif // DEF_UI_CONTENTS_KUMADORI_090227

/**
*  �޽��� ���� Ȯ�� �޽��� �ڽ� Ŭ����
* \date 2008-04-12
* \param View_MBOXEX 
* \return 
*/
BEGIN(View_MBOXEX)

KWindowCollector::MBOXEX_DESC m_desc;

#ifdef DEF_SUMMON_OEN_080801
DWORD m_timer;
#endif

/**
 *	ESC �� �������� ȣ��ȴ�.
 */
void OnCancel()
{
#ifdef DEF_SUMMON_OEN_080801
    Close();
#endif
}

void Init()
{
	POINT pos;

	// ������ ó��
	ReSize(m_desc.wndWidth, m_desc.wndHeight);

	// ������ ��ġ ����
	switch(m_desc.wndAlignType)
	{
	case KWindowCollector::MBOXEX_BOX_WINDOW_ALIGN_TYPE_CENTER:
		pos.x = (KWindowCollector::GetViewWidth() - m_sizeWindow.cx) / 2;
		pos.y = (KWindowCollector::GetViewHeight() - m_sizeWindow.cy) / 2;
		break;
	case KWindowCollector::MBOXEX_BOX_WINDOW_ALIGN_TYPE_LEFT:
		pos.x = (KWindowCollector::GetViewWidth() - m_sizeWindow.cx);
		pos.y = (KWindowCollector::GetViewHeight() - m_sizeWindow.cy);
		break;
	case KWindowCollector::MBOXEX_BOX_WINDOW_ALIGN_TYPE_RIGHT:
		pos.x = 0;
		pos.y = 0;
		break;
	}

	OnMove(pos);

	// �ڽ� Ÿ�� ó��
	switch(LOWORD(m_desc.boxType))
	{
	case KWindowCollector::MBOXEX_BOX_TYPE_MODAL:
		// ���� ��Ʈ���� ĸ���ϰ� Ÿ ��Ʈ���� ĸ������ ���ϵ��� ��ٴ�.
		SetCapture();
		Control::CContext::LockCapture();
		break;
	case KWindowCollector::MBOXEX_BOX_TYPE_MODALLESS:
		break;
	}

	switch(HIWORD(m_desc.boxType) << 16)
	{
	case KWindowCollector::MBOXEX_BOX_TYPE_OK:
		{                
			// ok ��ư �߰�
			InsertButton("ok", m_desc.buttonNames, m_desc.buttonWidth, m_desc.buttonPos[0].x,  m_desc.buttonPos[0].y);
		}
		break;
	case KWindowCollector::MBOXEX_BOX_TYPE_YESNO:
		{
			// yes �� no ��ư �߰�
			InsertButton("yes", m_desc.buttonNames, m_desc.buttonWidth, m_desc.buttonPos[0].x,  m_desc.buttonPos[0].y);
			InsertButton("no", m_desc.buttonNames + strlen(m_desc.buttonNames) + 1, m_desc.buttonWidth, m_desc.buttonPos[1].x,  m_desc.buttonPos[1].y);
		}
		break;
	}

	// ĸ�� ó��
	if (!m_desc.caption.empty())
		OnCaption(m_desc.caption.c_str());

	// �޽��� ó��
	if (!m_desc.text.empty())
		InsertMessage(m_desc.text.c_str());

#ifdef DEF_SUMMON_OEN_080801
    m_timer = GetTickCount();
#endif
    
	// �ʱ�ȭ
	// ���� �ƿ����� ����� �ǹǷ� �ݵ�� �������� �ʱ�ȭ�� ���־�� �Ѵ�.
	Initialize();

	// ��ü ���� ��ħ
	refresh();
}

#ifdef DEF_SUMMON_OEN_080801

void OnPaint()
{
    // Ÿ�̸� ���� �����Ѵٸ�
    if (m_timer != -1)
    {
        // ������ ���� ���ٵǸ� �ݾ��ش�.
        if (GetTickCount() - m_timer > m_desc.timer)
            Close();
    }

#ifdef DEF_UI_RENEWALL_081016
	FillRectWithD3D( 
		(float)m_ptWindow.x - 7,
		(float)m_ptWindow.y - 15,
		(float)m_ptWindow.x - 7 + m_sizeWindow.cx + 14,
		(float)m_ptWindow.y - 15 + m_sizeWindow.cy + 40,
		D3DCOLOR_ARGB( 100, 0, 0, 0 ) );

    KHyperView::OnPaint();
#else
	CHyperView::OnPaint();
#endif
}

#endif

void Destroy()
{
	// �ڽ� Ÿ�� ó��
	switch(LOWORD(m_desc.boxType))
	{
	case KWindowCollector::MBOXEX_BOX_TYPE_MODAL:
		// ����� Ǯ���ش�.
		Control::CContext::UnlockCapture();
		break;
	case KWindowCollector::MBOXEX_BOX_TYPE_MODALLESS:
		break;
	}
}

void SetParam(KWindowCollector::MBOXEX_DESC& desc_)
{
	m_desc = desc_;
#ifdef DEF_SUMMON_OEN_080801
	m_pRoot->m_vChild[2]->Reset();
#endif
	Init();
}

void Close()
{
	Destroy();
	KHyperView::Close();
}

/**
*  ��ũ��Ʈ�� link �� Ŭ���� ȣ��ȴ�.
* \param szName �ش� ��ũ �̸�
* \param param �Ķ� ����
*/
void OnLink(LPCSTR szName, lisp::var param) 
{
	if (!stricmp(szName, "ok") || !stricmp(szName, "yes")) 
	{
		if (m_desc.fun)
			(*m_desc.fun)(m_desc.actionType, true, m_desc.calledclass);
	}
	else
	{
		if (m_desc.fun)
			(*m_desc.fun)(m_desc.actionType, false, m_desc.calledclass);
	}

	Close();
}

/**
*  �����쿡 ��ư �߰�
* \date 2008-04-11
* \param name_ ��ư��
* \param text_ ��ư ǥ�� ����
* \param width_ ����
* \param x_, y_ ��ġ
*/
void InsertButton(LPCSTR name_, LPCSTR text_, int width_, int x_, int y_)
{
	HyperElement::CButton* button = (HyperElement::CButton*)HyperElement::CButton::CreateObject(this);        
	HyperElement::CPosition* pos = (HyperElement::CPosition*)HyperElement::CPosition::CreateObject(this);
	//HyperElement::COffset* offset = (HyperElement::COffset*)HyperElement::COffset::CreateObject(this);

	button->Reset();

#ifdef DEF_UI_RENEWALL_081016
	button->m_strBitmap = "data\\ui\\B2-mini-default";
	button->m_hBitmapEx[ HyperElement::CButton::DEFAULT ] = (HBITMAP)LoadImage(0, "data\\ui\\B2-mini-default.bmp", IMAGE_BITMAP, 0, 0, LR_LOADFROMFILE);
	button->m_hBitmapEx[ HyperElement::CButton::POP ] = (HBITMAP)LoadImage(0, "data\\ui\\B2-mini-over.bmp", IMAGE_BITMAP, 0, 0, LR_LOADFROMFILE);
	button->m_hBitmapEx[ HyperElement::CButton::PUSH ] = (HBITMAP)LoadImage(0, "data\\ui\\B2-mini-on.bmp", IMAGE_BITMAP, 0, 0, LR_LOADFROMFILE);
	button->m_nDesireWidth = 0;
	button->Layout();
#else
	button->m_nDesireWidth = 6 * (width_ + 2);
#endif

	button->m_strText = text_;
	button->m_strName = name_;
	button->m_nColorKey = 0;
	pos->Reset();
	pos->m_ptPosition.x = x_;
	pos->m_ptPosition.y = y_;
	//offset->Reset();
	//offset->m_sizeOffset.cx = (m_sizeWindow.cx / 2) - button->m_nDesireWidth;
	//offset->m_sizeOffset.cy = m_sizeWindow.cy - KControlDecorator::GetButtonHeight();

	m_pRoot->m_vChild[2]->AddChild(pos);
	m_pRoot->m_vChild[2]->AddChild(button);
}

//! ��ū Ÿ��
enum
{
	TOKEN_NONE,

	TOKEN_TYPE_NUM,         //@ ����
	TOKEN_TYPE_STRING,      //@ ���ڿ�
	TOKEN_TYPE_OPEN,        //@ ���� ����
	TOKEN_TYPE_CLOSE,       //@ ���� ����

	TOKEN_TYPE_COLOR,       //@ ���� ����
	TOKEN_TYPE_HALIGN,      //@ �¿� ����
	TOKEN_TYPE_VALIGN,      //@ ���� ����
	TOKEN_TYPE_LINEFEED,    //@ ����
	TOKEN_TYPE_POS,         //@ �߾� ����

	TOKEN_TYPE_CENTER = -1, //@ �߾� ����
	TOKEN_TYPE_RIGHT  = -2, //@ ������ ����
	TOKEN_TYPE_LEFT   = -3, //@ ���� ����

	TOKEN_TYPE_END,         //@ ��

	TOKEN_TYPE_ETC,         //@ ��Ÿ
};

/**
* �̴� ��ū Ŭ����
*
* \date 2008-04-11
* \author oen
*/
class CXToken
{
public:
	int operator =(LPCSTR str_) { memcpy(m_buf, str_, sizeof(str_)); }
	operator int() { return atoi(m_buf); }
	operator LPCSTR() { return m_buf; }
	char* getBuffer() { return m_buf; }
	int getBufferSize() { return sizeof(m_buf); }
	char m_buf[MAX_PATH];
};

/**
* �̴� �ļ� Ŭ����
*
* \date 2008-04-11
* \author oen
*/
class CXParser
{
public:

	CXParser(std::string str_)
	{
		m_focus = m_depth = 0;
		m_str = str_;
		m_size = str_.size();            

		m_symbol.insert(std::make_pair("color", TOKEN_TYPE_COLOR));
		m_symbol.insert(std::make_pair("halign", TOKEN_TYPE_HALIGN));
		m_symbol.insert(std::make_pair("valign", TOKEN_TYPE_VALIGN));
		m_symbol.insert(std::make_pair("n", TOKEN_TYPE_LINEFEED));
		m_symbol.insert(std::make_pair("center", TOKEN_TYPE_CENTER));
		m_symbol.insert(std::make_pair("left", TOKEN_TYPE_LEFT));
		m_symbol.insert(std::make_pair("right", TOKEN_TYPE_RIGHT));
		m_symbol.insert(std::make_pair("pos", TOKEN_TYPE_POS));            
	}

	int getToken()
	{
		while (m_focus < m_size)
		{
			m_ch = m_str[m_focus];

			// Ư�� ���� ó��
			switch(m_ch)
			{
			case '#': m_focus++; m_depth = 1; continue;
			case '(': m_focus++; m_depth++; return TOKEN_TYPE_OPEN;
			case ')': m_focus++; m_depth -= 2; return TOKEN_TYPE_CLOSE;
			}

			if (!m_depth)
			{
				if (getWord(m_token.getBuffer(), m_token.getBufferSize()))
					return TOKEN_TYPE_STRING;
			}

			// ���� ó��
			if (isspace(m_ch))
			{
				while (m_focus < m_size && isspace(m_str[m_focus])) 
					m_focus++;
				continue;
			}

			// ���� ó��
			if (isdigit(m_ch))
			{
				if (getWord(m_token.getBuffer(), m_token.getBufferSize()))
					return TOKEN_TYPE_NUM;
			}

			// ������ �ɹ� ó��
			if (getWord(m_token.getBuffer(), m_token.getBufferSize()))
			{
				std::hash_map<std::string, int>::iterator it = m_symbol.find((LPCSTR)m_token);
				if (it != m_symbol.end())
				{
					return it->second;
				}
			}

			return TOKEN_TYPE_ETC;
		}

		return TOKEN_TYPE_END;
	}

	char getWord(char* buf_, int bufSize_)
	{
		m_ch = 0;
		int size = 0;

		while (true)
		{                
			m_ch = m_str[m_focus++];
			*buf_++ = m_ch;
			size++;

			// ���� �� ���� �� Ư�� ó��
			if ( m_focus >= m_size ||
				(m_depth && (isspace(m_str[m_focus]) || m_str[m_focus] == '(' || m_str[m_focus] == ')' || m_str[m_focus] == '#')) ||
				(!m_depth && m_str[m_focus] == '#')) break;
		}

		// �ι��� ����
		*buf_++ = 0;
		return m_ch;
	}


	int m_depth;
	char m_ch;
	int m_size;
	int m_focus;
	std::string m_str;
	CXToken m_token;
	std::hash_map<std::string, int> m_symbol;
};

/**
*  �ش� ���ڿ��� ����Ѵ�.
* \date 2008-04-13
* \param text_ ���ڿ�
*/
void InsertMessage(LPCSTR text_)
{
	int x = 0, y = 0;        
	int tokenType = 0;
	UINT color = 0;
	int vAlign = 0;
	int hAlign = 0;	
	CXParser miniParser(text_);

#ifdef DEF_SUMMON_OEN_080801
    std::vector<std::string> vStr;
    std::vector<UINT> vColor;
#else
    std::string str;
#endif

	while ((tokenType = miniParser.getToken()) != TOKEN_TYPE_END)
	{
		switch(tokenType)
		{
		case TOKEN_TYPE_COLOR:

#ifndef DEF_SUMMON_OEN_080801
            // �̹� ��¥�� �ִٸ� ��¥�� �ִ� ���� �־������.
			if (!str.empty())
			{
				hAlign += InsertWord(str, hAlign, y * m_nFontHeight + vAlign, !color ? 0x00ffffff : color);
				str.erase();
				color = 0;
			}
#endif
			if (miniParser.getToken() == TOKEN_TYPE_OPEN)
			{
				int i = 0;
				while (i < 3 && miniParser.getToken() == TOKEN_TYPE_NUM)
                {
					color |= ((int)miniParser.m_token) << ((i++) * 8);
                }

#ifndef DEF_SUMMON_OEN_080801
				while (miniParser.getToken() == TOKEN_TYPE_END) { break;}
#else
                if (miniParser.getToken() == TOKEN_TYPE_CLOSE) 
                { 
                    vColor.push_back(color);
                    color = 0;
                    break;
                }
#endif
			}
			break;
		case TOKEN_TYPE_VALIGN:
			if (miniParser.getToken() == TOKEN_TYPE_OPEN)
			{
				if (miniParser.getToken() == TOKEN_TYPE_NUM)
					vAlign = (int)miniParser.m_token;

#ifndef DEF_SUMMON_OEN_080801
				while (miniParser.getToken() == TOKEN_TYPE_END) break;
#else
                if (miniParser.getToken() == TOKEN_TYPE_CLOSE) break;
#endif
			}
			break;
		case TOKEN_TYPE_HALIGN:
			if (miniParser.getToken() == TOKEN_TYPE_OPEN)
			{
				int type = 0;
				if ((type = miniParser.getToken()) != TOKEN_TYPE_NUM)
					hAlign = type;
				else
					hAlign = (int)miniParser.m_token;

#ifndef DEF_SUMMON_OEN_080801
                while (miniParser.getToken() == TOKEN_TYPE_END) break;
#else
                if (miniParser.getToken() == TOKEN_TYPE_CLOSE) break;
#endif
			}
			break;
		case TOKEN_TYPE_LINEFEED:
#ifndef DEF_SUMMON_OEN_080801
			InsertWord(str, hAlign, y++ * m_nFontHeight + vAlign, !color ? 0x00ffffff : color);
            str.erase();
#else
            InsertWord(vStr, hAlign, y++ * m_nFontHeight + vAlign, vColor);
            vStr.clear();
            vColor.clear();
#endif			
			color = vAlign = hAlign = 0;
			break;
		case TOKEN_TYPE_STRING:
#ifndef DEF_SUMMON_OEN_080801
			str += (LPCSTR)miniParser.m_token;
#else
            vStr.push_back((LPCSTR)miniParser.m_token);
#endif
			break;
		case TOKEN_TYPE_POS:
			if (miniParser.getToken() == TOKEN_TYPE_OPEN)
			{
				if (miniParser.getToken() == TOKEN_TYPE_NUM)
					hAlign = (int)miniParser.m_token;
				if (miniParser.getToken() == TOKEN_TYPE_NUM)
					vAlign = (int)miniParser.m_token;

#ifndef DEF_SUMMON_OEN_080801
                while (miniParser.getToken() == TOKEN_TYPE_END) break;
#else
                if (miniParser.getToken() == TOKEN_TYPE_CLOSE) break;
#endif
			}
			break;
		}
	}
#ifndef DEF_SUMMON_OEN_080801
	InsertWord(str, hAlign, y * m_nFontHeight + vAlign, !color ? 0x00ffffff : color);
#else
    InsertWord(vStr, hAlign, y++ * m_nFontHeight + vAlign, vColor);
#endif
}

/**
*  �ش� ���ڿ��� �ʺ� ��´�.
* \date 2008-04-13
* \param str �ʺ� ���� ���ڿ�
* \return ���ڿ� �ʺ�
*/
int GetWidth(std::string& str_)
{
	if (str_.empty()) return 0;
	HDC hdc = GetDC(CContext::m_hWnd);
	HFONT hFont = (HFONT) SelectObject(hdc, m_hFont);
	int	nCount = str_.size();
	LPCSTR	lpString = str_.c_str();        
	LPWSTR pGlyphs = (LPWSTR) _alloca(nCount * sizeof(WCHAR));
	LPINT pDxWidth = (LPINT) _alloca(nCount * sizeof(int));

	GCP_RESULTS result;
	memset(&result, 0, sizeof(GCP_RESULTS));
	result.lStructSize = sizeof(GCP_RESULTS);
	result.lpGlyphs = pGlyphs;
	result.nGlyphs = nCount;
	result.lpDx = pDxWidth;
	Control::GetCharacterPlacement(hdc, lpString, nCount, 0, &result, GetFontLanguageInfo(hdc));
	LPCTSTR	str2 = lpString;
	int resutlt2 = 0;

	for ( ; *str2; )
	{
		resutlt2 += *pDxWidth;
		if (Control::IsDBCSLeadByteEx(m_nCodePage, *str2)) 
			str2 += 2;
		else
			str2++;

		pDxWidth++;
	}

	SelectObject(hdc, hFont);
	ReleaseDC(CContext::m_hWnd, hdc);

	return resutlt2;
}

/**
*  �ش� ���ڿ��� ����Ѵ�.
* \date 2008-04-13
* \param str_ ���ڿ�
* \param hAlign_ �¿� ���� ���
* \param y_ y����
* \param color_ ��� ����
* \return x ��
*/
#ifndef DEF_SUMMON_OEN_080801
int InsertWord(std::string& str_, int hAlign_, int y_, UINT color_)
{
	int x = 0, y = 0;
	HyperElement::CWord* word = (HyperElement::CWord*)HyperElement::CWord::CreateObject(this);
	HyperElement::CPosition* pos = (HyperElement::CPosition*)HyperElement::CPosition::CreateObject(this);

	word->Reset();
	word->m_strText = str_;
	word->m_nColor = color_;
	pos->Reset();

	if (hAlign_ < 0 && hAlign_ == TOKEN_TYPE_CENTER)
		x = (m_sizeWindow.cx - GetWidth(str_)) / 2;
	else if (hAlign_ < 0 && hAlign_ == TOKEN_TYPE_RIGHT)
		x = m_sizeWindow.cx - GetWidth(str_);
	else if (hAlign_ > 0)
		x = hAlign_;

	y = y_;

	pos->m_ptPosition.x = x;
	pos->m_ptPosition.y = y;

	m_pRoot->m_vChild[2]->AddChild(pos);
	m_pRoot->m_vChild[2]->AddChild(word);

	return GetWidth(str_);
}
#else
int InsertWord(std::vector<std::string>& vStr, int hAlign_, int y_, std::vector<UINT>& vColor)
{
    std::string str;
    int x = 0, y = 0;

    for (size_t i = 0; i < vStr.size(); i++)
    {
        str += vStr[i];
    }

    if (hAlign_ < 0 && hAlign_ == TOKEN_TYPE_CENTER)
        x = (m_sizeWindow.cx - GetWidth(str)) / 2;
    else if (hAlign_ < 0 && hAlign_ == TOKEN_TYPE_RIGHT)
        x = m_sizeWindow.cx - GetWidth(str);
    else if (hAlign_ > 0)
        x = hAlign_;

    y = y_;

    str.clear();

    for (size_t i = 0; i < vStr.size(); i++)
    {
        HyperElement::CWord* word = (HyperElement::CWord*)HyperElement::CWord::CreateObject(this);
        HyperElement::CPosition* pos = (HyperElement::CPosition*)HyperElement::CPosition::CreateObject(this);

        word->Reset();
        word->m_strText = vStr[i];
        word->m_nColor = DWORD( vColor.size() > i ? ( vColor[i] == 0 ? 0x00ffffff : vColor[i] ) : 0x00ffffff );
        pos->Reset();
        i == 0 ? pos->m_ptPosition.x = x : pos->m_ptPosition.x = x + GetWidth(str);
        pos->m_ptPosition.y = y;
        m_pRoot->m_vChild[2]->AddChild(pos);
        m_pRoot->m_vChild[2]->AddChild(word);

        str += vStr[i];
    }

    return GetWidth(str);
}
#endif // DEF_SUMMON_OEN_080801

virtual void OnRButtonDown(POINT pt, WPARAM wParam) {

}
END
#endif