#include "StdAfx.h"
#include "hyperview.h"
#include "lisp.h"
#include "Editor.h"
#include "CControlEX.h"
#include "HyperElement.h"
#include "XFile.h"
#include "XParser.h"

#ifdef GAME
#include "KGameSys.h"
#include "KGameSysex.h"
#endif

#include "utility.h"

#ifdef DEF_UI_RENEWALL_081016
#   include "xsystem.h"
#endif

#define WHEEL_DELTA 120

using namespace HyperElement;
using namespace Control;

// void	CHyperView::Create(CControl	*pctrParent, LPRECT prcWindow)
BOOL	CHyperView::Create(CControl	*pctrParent, LPRECT prcWindow, int nStyle)
{
	int style = ( m_dwStyle & KWS_OVERLAPPED) ? OVERLAPPED : 0;
	style |= ( m_dwStyle & KWS_VSCROLL) ? VSCROLL : 0;

#ifdef DEF_UI_RENEWALL_081016
	style |= ( m_dwStyle & KWS_CHILD) ? CHILD : 0;
#endif

	// ������ â ���� �� ����(���ؽ�Ʈ)�� ���
	CControl::Create(pctrParent, prcWindow, style);

#ifdef	GAME
#ifndef DEF_UI_RENEWALL_081016
	if( m_dwStyle & KWS_OVERLAPPED)
#else
	if( m_dwStyle & KWS_OVERLAPPED &&  !( m_dwStyle & KWS_CHILD) )
#endif
		CreateDC();

	m_nWrap = m_sizeWindow.cx + (( m_dwStyle & KWS_VSCROLL)? - KControlDecorator::GetScrollWidth() : 0);
#else
	m_nWrap = m_sizeWindow.cx;
#endif

	if (m_nCodePage == 0)
		m_nCodePage = Control::m_nCodePage;
	if (!m_pRoot) {
		m_pRoot = new CFrame(this);
		m_pRoot->Reset();
	}

	Initialize();

	return TRUE;
}

void CHyperView::Destroy()
{
	POINT pt = { 0, 0};
	OnMouseLeave( pt, 0);

	Reset(); 
	CControl::Destroy(); 
}

void CHyperView::Reset()
{
	if (m_pRoot) {
		m_pRoot->Reset();
		delete m_pRoot;
		m_pRoot = 0;
	}
	m_hFont = 0;
	m_nHeight = 0;
	m_strDirectory = ".";
	m_nCodePage = 0;
	m_pTrackElement = 0;
	m_pFocusElement = 0;
	m_dwStyle = 0;
	m_ptFrom.x = -1;

#ifdef DEF_UI_RENEWALL_081016
	memset(m_aRectFrame, 0, sizeof(m_aRectFrame));
	memset(&m_ptCaption, 0, sizeof(m_ptCaption));
	memset(&m_ptClose, 0, sizeof(m_ptClose));
	memset(m_aTitleTexture, 0, sizeof(m_aTitleTexture));
	memset(m_aTitleImageOffset, 0, sizeof(m_aTitleImageOffset));
#endif
}

BOOL	CHyperView::Load(LPCSTR szFilename, BOOL bReadTxt_)
{	
	char szPath[MAX_PATH+1];

#ifdef _DEBUG
	TRACE( "HyperView Load: '%s'\n", szFilename );
#endif

#ifdef DEF_TEST_TXT_PK_CONVERTER_OEN_071009

	// txt��
	XFileEx file;
	std::string contry;

	// ���� ������ ��� s�� ����Ѵ�.
	if (KGameSys::m_szLocale == "k")
	{
		if (KGameSys::m_byTestClient)
		{
			contry = "t";
		}
		else
			contry = "s";
#if defined(DEF_UI_RENEWALL_081016) && defined(DEF_COSMO_DEVELOPER_VER)
		contry = "uik";
#endif
	}
	else
	{
		if (KGameSys::m_byTestClient)
		{
			contry = "t";
			contry += KGameSys::m_szLocale;
		}
		else
			contry = KGameSys::m_szLocale;

#if defined(DEF_UI_RENEWALL_081016) && defined(DEF_COSMO_DEVELOPER_VER)
		contry = "uie";
#endif
	}

	// ������ ������ ������ txt�� �д´�.
	wsprintf(szPath, "HyperText\\%s\\%s.txt", contry.c_str(), szFilename);    

	if (!file.Open(szPath)) 
	{
		return FALSE;
	}    

	return Load(&file);

#else

#ifdef DEF_OPEN_SCRIPT_OEN_071023
	// ���� ���������� Ư�� ���(�ڽ��� ��������)�� txt�� �д´�.
	if (bReadTxt_)
	{
		XFileEx file;

		wsprintf(szPath, "HyperText\\%s\\%s.txt", KGameSys::m_szLocale.c_str(), szFilename);
		if (!file.Open(szPath)) {
			return FALSE;
		}
		return Load(&file);
	}
	else
#endif
	{
		// ������(pk��)
		XFileZip file;

		// ���� ������ ������ pk�� �д´�.
#ifdef DEF_UI_RENEWALL_081016
		if ( KGameSys::m_szLocale == "k" )
		{
			wsprintf( szPath, "Data\\HyperText\\%s\\%s.dat", "UIk", szFilename);
		}
		else if ( KGameSys::m_szLocale == "c" )
		{
			wsprintf( szPath, "Data\\HyperText\\%s\\%s.dat", "UIc", szFilename);
		}
		else if ( KGameSys::m_szLocale == "e" )
		{
			wsprintf( szPath, "Data\\HyperText\\%s\\%s.dat", "UIe", szFilename);
		}
#else
		wsprintf( szPath, "Data\\HyperText\\%s\\%s.dat", KGameSys::m_szLocale.c_str(), szFilename);
#endif // DEF_UI_RENEWALL_081016

		if ( !file.Open( szPath ) )
		{
			return FALSE;
		}
		XFile decoded_file;
		decoded_file.Open(_alloca(file.GetLength()), file.GetLength());

		// ���ڵ� �Ѵ�.
#ifndef _DEBUG
		Decode(4, (BYTE *) decoded_file.m_pViewBegin, ( BYTE*) file.m_pViewBegin, file.GetLength());
#else
		// ����� ���� �����̴�.
		// ���ڵ� 1�� �о�鿩 ������ �����Ѵ�.
		// ���� �����ϵ��� �Ѵ�. �����ִ°� ����
		Decode(1, (BYTE *) decoded_file.m_pViewBegin, ( BYTE*) file.m_pViewBegin, file.GetLength());
#endif
		return Load(&decoded_file);
	}

#endif

}

BOOL	CHyperView::Load(XFile *pFile)
{
	if (!m_pRoot)
		m_pRoot = new CFrame(this);
	m_pRoot->Reset();
	XParser parser;
	parser.Open(pFile);
#ifdef DEF_HAPPY_TIME_OEN_080507
	BOOL bLoad = m_pRoot->Load(&parser, this);
#else
	BOOL bLoad = m_pRoot->Load(&parser);
#endif

	if (!bLoad) {
		TRACE("Load Failed\n");
		BREAK();
		m_pRoot->Reset();
	}
	if (IsInitialized())
		Initialize();
	return bLoad;
}

void    CHyperView::refresh(bool bLayOut) 
{
#ifdef DEF_BOGY_OEN_080407
	// ��������� ����(����, Ÿ��Ʋ����) �� �����
	RECT rcUpdate = { m_ptWindow.x, m_ptWindow.y, m_ptWindow.x + m_sizeWindow.cx, m_ptWindow.y + m_sizeWindow.cy};
	if (m_dwStyle & CHyperView::KWS_EDGE) InflateRect( &rcUpdate, KW_BORDER, KW_BORDER);
	if (m_dwStyle & CHyperView::KWS_TITLE) rcUpdate.top -= KControlDecorator::GetTitleHeight();

	UpdateRect(&rcUpdate);
#else
	InvalidateRect();
#endif
}

void	CHyperView::Initialize()
{
	m_pTrackElement = 0;
	m_pFocusElement = 0;
	m_nScroll = 0;
	m_nTabStop = 0;
	m_vTabStop.clear();

	HDC hdc = GetDC(CContext::m_hWnd);

	HFONT hFont;
	if (!m_hFont)
	{
		BREAK();
		m_hFont =  (HFONT) GetStockObject( SYSTEM_FIXED_FONT);
		hFont = (HFONT) SelectObject( hdc, m_hFont); 

		TEXTMETRIC	tm;
		GetTextMetrics( hdc, &tm );

		m_nFontHeight = tm.tmHeight;
	}
	else{
		hFont = (HFONT) SelectObject( hdc, m_hFont); 
	}

#ifdef DEF_BOGY_OEN_080407
	// ũ�⸦ �ʱ�ȭ ���� �ʴ� ���� ����
	m_pRoot->m_vnLineHeight.erase(m_pRoot->m_vnLineHeight.begin(), m_pRoot->m_vnLineHeight.end());
#endif

	m_pRoot->Layout();

	UpdatePosition(hdc);
	SelectObject(hdc, hFont);
	ReleaseDC(CContext::m_hWnd, hdc);
	if (!m_vTabStop.empty()) {
		m_vTabStop[m_nTabStop]->DoTab();
	}
	else
		SetFocus();
}

void	CHyperView::OnMove(POINT ptWindow)
{
	CControl::OnMove(ptWindow);

	HDC hdc = GetDC(CContext::m_hWnd);
	HFONT hFont = (HFONT) SelectObject(hdc, m_hFont);
	POINT pt = { 0, 0 };
	m_pRoot->UpdatePosition(hdc, pt, 0);
	SelectObject(hdc, hFont);
	ReleaseDC(CContext::m_hWnd, hdc);

	OnSize( KWindowCollector::GetViewWidth(), KWindowCollector::GetViewHeight() - ANGERHEIGHT);
}

void CHyperView::OnPaint(HDC hdc, LPRECT prcClip)
{
#ifdef GAME
	if ( m_dwStyle & KWS_EDGE && prcClip->top < m_ptWindow.y)
	{
		RECT rect = { m_ptWindow.x - KW_BORDER, m_ptWindow.y - KW_BORDER, 
			m_ptWindow.x + m_sizeWindow.cx + KW_BORDER, m_ptWindow.y + m_sizeWindow.cy + KW_BORDER };

		KControlDecorator::DrawEdge( hdc, &rect, prcClip);
	}
#else
	RECT rect = { m_ptWindow.x - 2, m_ptWindow.y - 2, m_ptWindow.x + m_sizeWindow.cx + 2, m_ptWindow.y + m_sizeWindow.cy + 2 };
	DrawEdge(hdc, &rect, EDGE_BUMP, BF_RECT);
#endif


	RECT rcClip = m_rcClip;
	if (prcClip == &m_rcClip || IntersectRect(&m_rcClip, &m_rcClip, prcClip)) {
		POINT pt = {0, 0};
		HFONT hFont = (HFONT) SelectObject( hdc, m_hFont); 
		int nBkMode = SetBkMode(hdc, TRANSPARENT);
		COLORREF nColor = GetTextColor(hdc);
		m_pRoot->OnPaint(hdc, pt, 0);
		SetTextColor(hdc, nColor);
		SetBkMode(hdc, nBkMode);
		SelectObject(hdc, hFont);
		CControl::OnPaint(hdc, &m_rcClip);
	}
	m_rcClip = rcClip;
}

void	CHyperView::OnLButtonDown(POINT pt, WPARAM wParam)
{
	if( 0 != m_pFocusElement)
		m_pFocusElement->KillFocus();

#ifdef DEF_BOGY_OEN_080407
	// ������ y�� �Ʒ��� üũ�ϴ� ���� y �� x ���� üũ�� ����
	if ( m_dwStyle & KWS_TITLE && 
		(pt.y < m_ptWindow.y - KW_BORDER && pt.y > m_ptWindow.y - KW_BORDER - KControlDecorator::GetTitleHeight()) && 
		(pt.x < m_ptWindow.x + m_sizeWindow.cx && pt.x > m_ptWindow.x)
		) 
#else
	if (m_dwStyle & KWS_TITLE && pt.y < m_ptWindow.y - KW_BORDER) 
#endif
	{
		m_ptFrom = pt;
		SetCapture();
		SetFocus();
	}

	OnMessage( CM_VIEW_LBDOWN, this);

	if (!m_pRoot->OnLButtonDown(pt))
		SetFocus();
}

void	CHyperView::OnRButtonDown(POINT pt, WPARAM wParam)
{
	OnMessage( CM_VIEW_RBDOWN, this);
#ifdef GAME
	CControlEx::CPositionRememberControl::OnRButtonDown(pt, wParam);
#else
	CControl::OnRButtonDown(pt, wParam);
#endif
}

void	CHyperView::OnMouseMove(POINT pt, WPARAM wParam)
{
	if( m_dwStyle & KWS_TITLE)
	{
		if (m_ptFrom.x >= 0) {
			if (wParam & MK_LBUTTON) {
				POINT ptWindow = {m_ptWindow.x + pt.x - m_ptFrom.x, m_ptWindow.y + pt.y - m_ptFrom.y};
				OnMove(ptWindow);
				m_ptFrom = pt;

#ifdef GAME
				OnSize( KWindowCollector::GetViewWidth(), KWindowCollector::GetViewHeight() - ANGERHEIGHT);
#endif
			}
			else {
				m_ptFrom.x = -1;
				ReleaseCapture();
			}
			return;
		}
	}

	if (m_pTrackElement) {
		if (m_pTrackElement->OnMouseMove(pt))
			return;
		m_pTrackElement = 0;
	}
	m_pRoot->OnMouseMove(pt);
}

void	CHyperView::OnMouseLeave(POINT pt, WPARAM wParam)
{
	if (m_pTrackElement) {
		m_pTrackElement->OnMouseLeave();
		m_pTrackElement = 0;
	}
}

void	CHyperView::SetTrackElement(CElement *pElement)
{
	SetTrack();
	m_pTrackElement = pElement;
}

void	CHyperView::VScroll(int dy)
{
	dy = max(min(dy + m_nScroll, m_nHeight - m_sizeWindow.cy), 0) - m_nScroll;
	if (dy == 0)
		return;
	m_nScroll += dy;

	HDC hdc = GetDC(CContext::m_hWnd);
	HFONT hFont = (HFONT) SelectObject( hdc, m_hFont); 
	UpdatePosition(hdc);
	SelectObject(hdc, hFont);
	ReleaseDC(CContext::m_hWnd, hdc);
}

#ifdef DEF_UI_RENEWALL_081016

void CHyperView::OnPaint() 
{ 
	// �ڽĵ� ������
	PaintWithDC();
	if (m_pRoot)
		m_pRoot->CContainer::OnPaint();
}

void CHyperView::GetWindowsRect(RECT* pRect, RECT* pOffset)
{
	int nLeft = pOffset->left, nRight = pOffset->right, nUp = pOffset->top, nDown = pOffset->bottom;

	if (m_aTitleTexture[MIDDLE_LEFT])  nLeft -= m_aTitleTexture[MIDDLE_LEFT]->m_nWidth;
	if (m_aTitleTexture[MIDDLE_RIGHT]) nRight += m_aTitleTexture[MIDDLE_RIGHT]->m_nWidth;
	if (m_aTitleTexture[UP_LEFT])      nUp -= m_aTitleTexture[UP_LEFT]->m_nHeight;
	if (m_aTitleTexture[DOWN_LEFT])    nDown += m_aTitleTexture[DOWN_LEFT]->m_nHeight;

	// ���� ������ �� �Ʒ� �������� ���� ��Ŀ�� ��ġ�� ���� �� �� �ִ�.
	pRect->left = m_ptWindow.x + nLeft;
	pRect->right = m_ptWindow.x + m_sizeWindow.cx + nRight;
	pRect->top = m_ptWindow.y + nUp;
	pRect->bottom = m_ptWindow.y + m_sizeWindow.cy + nDown;
}

#endif

void	CHyperView::UpdatePosition(HDC hdc)
{
	POINT pt = { 0, 0 };

	// �ڽĵ��� ��ġ�� ������Ʈ �Ѵ�.
	// �������� 0,0 �� �ڽ� �������� ���� �������� ���� ���۵ȴ�.
	pt = m_pRoot->UpdatePosition(hdc, pt, 0);
	m_nHeight = pt.y;

#ifndef	GAME
	// ȭ�� ���� �̻��̸� ��ũ���� ����
	if (m_nHeight > m_sizeWindow.cy) {
		EnableScrollBar(CContext::m_hWnd, SB_VERT, ESB_ENABLE_BOTH);
		SetScrollRange(CContext::m_hWnd, SB_VERT, 0, m_nHeight - m_sizeWindow.cy, FALSE);
		SetScrollPos(CContext::m_hWnd, SB_VERT, m_nScroll, TRUE);
	}
	else
		EnableScrollBar(CContext::m_hWnd, SB_VERT, ESB_DISABLE_BOTH);
#endif

	// DC ����
	if( m_pDC)
		InvalidateRect();
}

void	CHyperView::OnVScroll(WPARAM wParam)
{
	switch (LOWORD(wParam)) {
	case SB_LINEUP:
		VScroll(-m_nFontHeight);
		break;
	case SB_LINEDOWN:
		VScroll(m_nFontHeight);
		break;
	case SB_PAGEUP:
		VScroll( -m_nFontHeight * 3);
		break;
	case SB_PAGEDOWN:
		VScroll( m_nFontHeight * 3);
		break;

	case SB_TOP:
		VScroll(m_nFontHeight - m_sizeWindow.cy);
		break;

	case SB_BOTTOM:
		VScroll(m_sizeWindow.cy - m_nFontHeight);
		break;

	case SB_THUMBTRACK:
		VScroll( HIWORD( wParam) - m_nScroll);
		break;

	}
}

void	CHyperView::OnMouseWheel(WPARAM wParam)
{
	short dy = HIWORD(wParam);
	if (dy >= 0) {
		dy = (dy + WHEEL_DELTA - 1)/WHEEL_DELTA;
		VScroll(-(m_nFontHeight * dy));
	}
	else {
		dy = -dy;
		dy = (dy + WHEEL_DELTA - 1)/WHEEL_DELTA;
		VScroll(m_nFontHeight * dy);
	}
}

void CHyperView::OnKeyDown(UINT nChar)
{
	switch( nChar )
	{
	case VK_UP:
		VScroll(-m_nFontHeight);
		break;

	case VK_DOWN:
		VScroll(m_nFontHeight);
		break;

	case VK_PRIOR:
		VScroll(m_nFontHeight - m_sizeWindow.cy);
		break;

	case VK_NEXT:
		VScroll(m_sizeWindow.cy - m_nFontHeight);
		break;

	case VK_TAB:
		if (!m_vTabStop.empty()) {
			m_nTabStop = (m_nTabStop + 1) % (int)m_vTabStop.size();
			m_vTabStop[m_nTabStop]->DoTab();
		}
		break;

	case VK_RETURN:
		OnOK();
		break;

	case VK_ESCAPE:
		OnCancel();
		break;
	}
}

void CHyperView::GetText(LPCSTR szName, std::string *pstrText)
{
	CElement *pElement;
	if ((pElement = m_pRoot->FindElement(szName))) 
		pElement->GetText(pstrText);
	else
		pstrText->clear();
}

void CHyperView::GetText(LPCSTR szName, LPSTR strText, int nMaxLen)
{
	CElement *pElement;
	if ((pElement = m_pRoot->FindElement(szName))) 
		pElement->GetText(strText, nMaxLen);
	else
		strText[0] = 0;
}

void CHyperView::SetText(LPCSTR szName, LPCSTR szText)
{
	CElement *pElement;
	if ((pElement = m_pRoot->FindElement(szName))) 
		pElement->SetText(szText);
}

void CHyperView::SetTextafterRefresh(LPCSTR szName, LPCSTR szText)
{
	CElement *pElement;
	if ((pElement = m_pRoot->FindElement(szName))) 
		pElement->SetText(szText);
	refresh();
}

void CHyperView::SetChangedText(LPCSTR szName_, LPCSTR szChangedText_, char* pzTextName_, BOOL bExtension_)
{
#ifdef DEF_TASK_BUG_OEN_080523
	char szOrginText[MAX_PATH] = {0};
	char szDestText[MAX_PATH] = {0};
#else
	char szOrginText[50] = {0};
	char szDestText[50] = {0};
#endif

	GetText(szName_, szOrginText, sizeof(szOrginText));
	_snprintf(szDestText, sizeof(szDestText), szOrginText, szChangedText_);

	SetText(szName_, szDestText);

#ifdef DEF_TASK_SYSTEM_OEN_080109
	if (bExtension_)
	{
		// -ǥ�ð� �ִٸ� 1~9���� ��� ��ȯ �����ش�.
		std::string temp = szName_;
		char num[10] = {0};

		for (int i = 1; i <= 9; i++)
		{
			_snprintf(num, sizeof(num), "-%d", i);            
			temp = szName_;
			temp += num;

#ifdef DEF_TASK_BUG_OEN_080523
			memset(szOrginText, 0, sizeof(szOrginText));
			memset(szDestText, 0, sizeof(szDestText));
#endif

			GetText(temp.c_str(), szOrginText, sizeof(szOrginText));
			_snprintf(szDestText, sizeof(szDestText), szOrginText, szChangedText_);

			SetText(temp.c_str(), szDestText);
		}
	}
#endif
}

void CHyperView::SetChangedText(LPCSTR szName_, int nChangedText_, char* pzTextName_, BOOL bExtension_)
{
#ifdef DEF_TASK_BUG_OEN_080523
	char szOrginText[MAX_PATH] = {0};
	char szDestText[MAX_PATH] = {0};
#else
	char szOrginText[50] = {0};
	char szDestText[50] = {0};
#endif

	GetText(szName_, szOrginText, sizeof(szOrginText));
	_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_);

	SetText(szName_, szDestText);

#ifdef DEF_TASK_SYSTEM_OEN_080109
	if (bExtension_)
	{
		// -ǥ�ð� �ִٸ� 1~9���� ��� ��ȯ �����ش�.
		std::string temp = szName_;
		char num[10] = {0};

		for (int i = 1; i <= 9; i++)
		{
			_snprintf(num, sizeof(num), "-%d", i);            
			temp = szName_;
			temp += num;

#ifdef DEF_TASK_BUG_OEN_080523
			memset(szOrginText, 0, sizeof(szOrginText));
			memset(szDestText, 0, sizeof(szDestText));
#endif

			GetText(temp.c_str(), szOrginText, sizeof(szOrginText));
			_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_);

			SetText(temp.c_str(), szDestText);
		}
	}
#endif
}

void CHyperView::SetChangedText(LPCSTR szName_, int nChangedText_,  int nChangedText2_, char* pzTextName_, BOOL bExtension_)
{
#ifdef DEF_TASK_BUG_OEN_080523
	char szOrginText[MAX_PATH] = {0};
	char szDestText[MAX_PATH] = {0};
#else
	char szOrginText[50] = {0};
	char szDestText[50] = {0};
#endif

	GetText(szName_, szOrginText, sizeof(szOrginText));
	_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_, nChangedText2_); 

	SetText(szName_, szDestText);

#ifdef DEF_TASK_SYSTEM_OEN_080109
	if (bExtension_)
	{
		// -ǥ�ð� �ִٸ� 1~9���� ��� ��ȯ �����ش�.
		std::string temp = szName_;
		char num[10] = {0};

		for (int i = 1; i <= 9; i++)
		{
			_snprintf(num, sizeof(num), "-%d", i);            
			temp = szName_;
			temp += num;

#ifdef DEF_TASK_BUG_OEN_080523
			memset(szOrginText, 0, sizeof(szOrginText));
			memset(szDestText, 0, sizeof(szDestText));
#endif

			GetText(temp.c_str(), szOrginText, sizeof(szOrginText));
			_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_);

			SetText(temp.c_str(), szDestText);
		}
	}
#endif
}

void CHyperView::SetChangedText(LPCSTR szName_, int nChangedText_,  int nChangedText2_, int nChangedText3_, int nChangedText4_, char* pzTextName_, BOOL bExtension_)
{
#ifdef DEF_TASK_BUG_OEN_080523
	char szOrginText[MAX_PATH] = {0};
	char szDestText[MAX_PATH] = {0};
#else
	char szOrginText[50] = {0};
	char szDestText[50] = {0};
#endif

	GetText(szName_, szOrginText, sizeof(szOrginText));
	_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_, nChangedText2_, nChangedText3_, nChangedText4_); 

	SetText(szName_, szDestText);

#ifdef DEF_TASK_SYSTEM_OEN_080109
	if (bExtension_)
	{
		// -ǥ�ð� �ִٸ� 1~9���� ��� ��ȯ �����ش�.
		std::string temp = szName_;
		char num[10] = {0};

		for (int i = 1; i <= 9; i++)
		{
			_snprintf(num, sizeof(num), "-%d", i);            
			temp = szName_;
			temp += num;

#ifdef DEF_TASK_BUG_OEN_080523
			memset(szOrginText, 0, sizeof(szOrginText));
			memset(szDestText, 0, sizeof(szDestText));
#endif

			GetText(temp.c_str(), szOrginText, sizeof(szOrginText));
			_snprintf(szDestText, sizeof(szDestText), szOrginText, nChangedText_);

			SetText(temp.c_str(), szDestText);
		}
	}
#endif
}

lisp::var CHyperView::GetParameter(LPCSTR szName)
{
	CElement *pElement;
	if ((pElement = m_pRoot->FindElement(szName))) 
		return pElement->GetParameter();
	return lisp::nil;
}

void	CHyperView::OnLink(LPCSTR szName, lisp::var varParameter)
{
	if (stricmp(szName, "OK") == 0) {
		if (varParameter.consp())
			OnLink(varParameter.car(), varParameter.cdr());
		else
			OnOK();
	}
	else if (stricmp(szName, "Cancel") == 0) {
		OnCancel();
	}
	else if (stricmp(szName, "file") == 0) {
		Load(varParameter.car());
	}
}

void	CHyperView::OnOK()
{
	lisp::var var = GetParameter("OK");
	if (var.consp() && stricmp(var.car(), "OK") != 0) {
		OnLink(var.car(), var.cdr());
	}
}

CControl *CHyperView::OnControlFromPoint(POINT pt)
{
#ifdef GAME
	int nTitleHeight = ( m_dwStyle & CHyperView::KWS_TITLE)? KControlDecorator::GetTitleHeight() : 0;
#else
	int nTitleHeight = 20;
#endif

	// ������ ȭ�� �������� �ø���.
	RECT rect = m_rcClip;
	m_rcClip.left = m_ptWindow.x - KW_BORDER;
	m_rcClip.top = m_ptWindow.y - KW_BORDER - nTitleHeight;
	m_rcClip.right = m_ptWindow.x + m_sizeWindow.cx + KW_BORDER;
	m_rcClip.bottom = m_ptWindow.y + m_sizeWindow.cy + KW_BORDER;

	// �����Ѵ�.
	CControl *pControl = CControl::OnControlFromPoint(pt);

	// �ٽ� �������� ������.
	m_rcClip = rect;

	return pControl;
}

BOOL CHyperView::GetScrollInfoVert( int& nFull, int& nPage, int& nCur)
{
	nFull = m_nHeight;
	nPage = m_sizeWindow.cy;
	nCur = m_nScroll;

	return nFull > nPage;
}

void CHyperView::SetTexti( LPCSTR id, int nNumber)
{ 
	char buf[ MAX_PATH];
	itoa( nNumber, buf, 10);
	CHyperView::SetText( id, buf);
}