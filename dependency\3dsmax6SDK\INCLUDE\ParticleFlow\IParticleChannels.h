/*! \file IParticleChannels.h
    \brief Collection of headers of all particle channels.
*/
/**********************************************************************
 *<
	CREATED BY: Oleg <PERSON>

	HISTORY: created 5-21-02

 *>	Copyright (c) 2001, All Rights Reserved.
 **********************************************************************/

#ifndef _IPARTICLECHANNELS_H_
#define _IPARTICLECHANNELS_H_

#include "IParticleChannel.h"
#include "IParticleChannelAmount.h"
#include "IParticleChannelAngAxis.h"
#include "IParticleChannelBool.h"
#include "IParticleChannelFloat.h"
#include "IParticleChannelINode.h"
#include "IParticleChannelInt.h"
#include "IParticleChannelMatrix3.h"
#include "IParticleChannelMap.h"
#include "IParticleChannelMesh.h"
#include "IParticleChannelMeshMap.h"
#include "IParticleChannelMXSFloat.h"
#include "IParticleChannelMXSInteger.h"
#include "IParticleChannelMXSMatrix.h"
#include "IParticleChannelMXSVector.h"
#include "IParticleChannelPoint3.h"
#include "IParticleChannelPTV.h"
#include "IParticleChannelQuat.h"
#include "IParticleChannelID.h"
#include "IParticleChannelNew.h"
#include "IParticleChannelAcceleration.h"
#include "IParticleChannelBirthTime.h"
#include "IParticleChannelDeathTime.h"
#include "IParticleChannelLifespan.h"
#include "IParticleChannelEventStart.h"
#include "IParticleChannelMaterialIndex.h"
#include "IParticleChannelOrientation.h"
#include "IParticleChannelPosition.h"
#include "IParticleChannelScale.h"
#include "IParticleChannelSelection.h"
#include "IParticleChannelShape.h"
#include "IParticleChannelShapeTexture.h"
#include "IParticleChannelSpeed.h"
#include "IParticleChannelSpin.h"
#include "IParticleChannelTabUVVert.h"
#include "IParticleChannelTabTVFace.h"
#include "IParticleChannelTime.h"
#include "IParticleChannelVoid.h"


#endif // _IPARTICLECHANNELS_H_
