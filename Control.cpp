#include "stdafx.h"
#include "control.h"
#include "CControlEX.h"
#include <cmath>
#ifdef	GAME
#include "XSystem.h"
#include "roam.h"
#include "China\China.h"
#endif

#include "link.h"

static int g_nCharSet[6] = {
	DEFAULT_CHARSET,		// Default 
	CHINESEBIG5_CHARSET,	// TRADITIONAL CHINESE
	SHIFTJIS_CHARSET,		// JAPANESE
	HANGUL_CHARSET,			// KOREAN
	GB2312_CHARSET,			// SIMPLIFIED CHINESE
	ANSI_CHARSET,
};

using namespace Control;
int	Control::m_nCodePage = CP_UTF8;
int	Control::m_nClientCP;
int	Control::m_bHongKong;
Control::CRoot	Control::CContext::m_ctrRoot;
HWND	Control::CContext::m_hWnd;
DWORD   Control::CContext::m_dwProperty;
int   Control::CContext::m_nState;
LANGUAGE Control::CContext::m_nLanguage;
int Control::CContext::m_bCaptureLocked = 0;
CControl *Control::CContext::m_pctrFocus;
CControl *Control::CContext::m_pctrCapture;
CControl *Control::CContext::m_pctrTrack;
#ifdef	GAME
	#ifdef	OPENGL
		SIZE	Control::CContext::m_sizeViewport;
	#else
		LPDIRECT3DDEVICE9	Control::CContext::m_pd3dDevice;
	#endif
#endif

#ifdef DEF_BOGY_OEN_080407
//! ��Ʈ�� ��ĥ �� FALSE�� �Ǹ� �ƴҶ�(ĸ�� ������ �� TRUE) �� �Ǹ鼭 ������ �Ѵ�?
BOOL g_bProcessed;
#else
static BOOL	g_bProcessed;
#endif

void CControl::Create()
{
	m_pctrParent = 0;
	m_pctrOwner = 0;
	m_linkParent.Initialize();
	m_rcClip.left = m_rcClip.top = 0;
	m_rcClip.right = m_rcClip.bottom = 0x7fffffff;
	m_ptWindow.x = m_ptWindow.y = 0;
	m_sizeWindow.cx = m_sizeWindow.cy = 0x7fffffff;
}

void CControl::SetFocus() 
{ 
	CContext::SetFocus(this); 
}

/**
 *  ��Ʈ���� �����Ѵ�
 * \param *pctrParent ������ ��Ʈ���� �θ� ��Ʈ��
 * \param prcWindow ������ ��Ʈ�� ����(ũ��) ������ ȭ�� ��ǥ
 * \param nStyle ������ ��Ʈ�� ��Ÿ��
 * \return ���� ����
 */
BOOL CControl::Create(CControl *pctrParent, LPRECT prcWindow, int nStyle)
{
	m_linkParent.Initialize();

#ifndef DEF_UI_RENEWALL_081016
    if (nStyle & OVERLAPPED) {
#else
	if (nStyle & OVERLAPPED && !(nStyle & CHILD) ) {
#endif
		for ( ; !pctrParent->m_pctrOwner && pctrParent->m_pctrParent ; pctrParent = pctrParent->m_pctrParent)
			;
		m_pctrOwner = pctrParent;
		m_pctrParent = CContext::GetRoot();
		m_linkParent.PushBack(&pctrParent->m_linkChild);
	}
	else {
		m_pctrOwner = 0;
		m_pctrParent = pctrParent;
		m_linkParent.PushFront(&pctrParent->m_linkChild);
	}
	IntersectRect(&m_rcClip, prcWindow, &m_pctrParent->m_rcClip);
	m_ptWindow.x = prcWindow->left;
	m_ptWindow.y = prcWindow->top;
	m_sizeWindow.cx = prcWindow->right - prcWindow->left;
	m_sizeWindow.cy = prcWindow->bottom - prcWindow->top;

#ifdef GAME
	if( nStyle & VSCROLL)
	{
		m_pScroll = new CControlEx::CScroll();
#   ifndef DEF_UI_RENEWALL_081016
		RECT rcScroll = { m_sizeWindow.cx - KControlDecorator::GetScrollWidth(), 0, m_sizeWindow.cx, m_sizeWindow.cy};
#   else
        RECT rcScroll = { m_sizeWindow.cx - ((CControlEx::CScroll*)m_pScroll)->GetWidth(), 0, m_sizeWindow.cx, m_sizeWindow.cy};
#   endif
		OffsetRect( &rcScroll, m_ptWindow.x, m_ptWindow.y);
		m_pScroll->Create( this, &rcScroll, 0);
	}
#endif
	m_nStyle = nStyle;

	return TRUE;
}

#ifdef	GAME
void	CControl::OnInitDevice()
{
	if (m_pDC) {
		m_pDC->OnInitDevice();
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			if (!(pControl->m_pctrOwner))
				break;
			pControl->OnInitDevice();
		}
	}
	else {
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			pControl->OnInitDevice();
		}
	}
}

void	CControl::OnDeleteDevice()
{
	if (m_pDC) {
		m_pDC->OnDeleteDevice();
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			if (!(pControl->m_pctrOwner))
				break;
			pControl->OnDeleteDevice();
		}
	}
	else {
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			pControl->OnDeleteDevice();
		}
	}
}

void	CControl::CreateDC()
{
	if (m_pDC)
		return;
	if ((m_pDC = m_pctrParent->m_pDC) != 0)
		return;
	m_pDC = OnCreateDC();
}

Control::CDC *CControl::OnCreateDC()
{
	Control::CDC *pDC = new Control::CDC();
	pDC->Create(this, m_ptWindow, m_sizeWindow);
	return pDC;
}

#endif

void	CControl::Reset()
{
	if ( IsInitialized() )
	{
		for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; )
		{
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			pLink = pLink->m_pNext;
			pControl->Destroy();
		}
		CContext::Destroy(this);
		m_linkParent.Remove();
		m_linkParent.m_pNext = 0;
#ifdef	GAME
		if (m_pDC) {
			if (m_pDC->m_pControl == this)
				delete m_pDC;
		}
#endif
	}
	m_linkChild.Initialize();
#ifdef	GAME
	m_pDC = 0;
#endif

	delete m_pScroll; m_pScroll = 0;
}

#ifdef DEF_UI_RENEWALL_081016

void CControl::ReSize(int width_, int height_)
{
    // ���� ���� ����
    m_sizeWindow.cx = width_;
    m_sizeWindow.cy = height_;

    // ��ü Ŭ�� ���� ����!
    m_rcClip.right = m_rcClip.left + width_;
    m_rcClip.bottom = m_rcClip.top + height_;

    // dc�� ���� �� ����
    // ���������� ���� �ؽ�ó�� ������� ����ϹǷ� �ݵ�� ������ �־�� �Ѵ�.
    SAFE_DELETE(m_pDC);
    CreateDC();
}

#endif

CControl	*CControl::OnControlFromPoint(POINT pt)
{
#ifdef DEF_UI_RENEWALL_081016
    if (!IsFocus())
        return NULL;
#endif
	CLink* pLink;

	for (pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev)
	{
		CControl *pControl = NULL;

		pControl = LINK_POINTER(pLink, CControl, m_linkParent);

		if (!(pControl->m_pctrOwner))
			break;

		if ( (pControl = pControl->OnControlFromPoint(pt)) != 0 && !pControl->m_bDisabled && !pControl->m_bHide)
			return pControl;
	}

	if (!PtInRect(&m_rcClip, pt))
		return 0;

	for ( ; pLink != &m_linkChild; pLink = pLink->m_pPrev)
	{
		CControl *pControl = NULL;

		pControl = LINK_POINTER(pLink, CControl, m_linkParent);

		if ((pControl = pControl->OnControlFromPoint(pt)) != 0 && !pControl->m_bDisabled && !pControl->m_bHide)
			return pControl;
	}
	return this;
}

void	CControl::BringControlToTop()
{
#ifdef DEF_UI_RENEWALL_081016
    if (IsBringControlToTop())
#endif
	if (m_pctrOwner) {
		m_linkParent.Remove();
		m_linkParent.PushBack(&m_pctrOwner->m_linkChild);
		m_pctrOwner->BringControlToTop();
	}
	else if (m_pctrParent) {
		m_pctrParent->BringControlToTop();
	}
}

#ifdef	GAME
void	CControl::OnPaint()
{
	for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; pLink = pLink->m_pNext) {
		CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
		if( pControl && !pControl->m_bHide)
			pControl->OnPaint();
	}
}

void	CControl::PaintWithDC()
{
    // ����ó��
    if (m_pDC)
	m_pDC->OnPaint();
	for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; pLink = pLink->m_pNext)
	{
		CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
		if (pControl->m_pctrOwner)
		{
			for ( ; ; )
			{
#ifdef DEF_UI_RENEWALL_081016
                // ���̵� ó��
                if (!pControl->m_bHide)
#endif
				pControl->OnPaint();
				pLink = pLink->m_pNext;
				if (pLink == &m_linkChild)
					return;
				pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			}
		}

#ifdef GAME
		else if( pControl->WantPerFramePaint())
			pControl->OnPaint();
#endif

	}
}
#endif

void	CControl::OnPaint(HDC hdc, LPRECT prcClip)
{
	for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; pLink = pLink->m_pNext)
	{
		CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
		if (pControl->m_pctrOwner)
			break;

		if( !pControl->m_bHide)
			pControl->OnPaint(hdc, prcClip);
	}
}

void	CControl::OnSize( int cx, int cy)
{
	for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; pLink = pLink->m_pNext)
	{
		CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
		if (pControl->m_pctrOwner)
			pControl->OnSize( cx, cy);
	}
}

void CControl::OnMove(POINT ptWindow)
{
	SIZE size = { ptWindow.x - m_ptWindow.x, ptWindow.y - m_ptWindow.y};
	m_ptWindow = ptWindow;

	RECT rect = { m_ptWindow.x, m_ptWindow.y, m_ptWindow.x + m_sizeWindow.cx, m_ptWindow.y + m_sizeWindow.cy};

    // �θ� Ŭ���������� ���� ��ϵ� �������� ���� ������ �ڽ��� �������� ����
	IntersectRect(&m_rcClip, &rect, &m_pctrParent->m_rcClip);

#ifdef	GAME
	if (m_pDC && m_pDC->m_pControl == this) {
		m_pDC->OnMove(size);
	}
#endif

    // ��� �ڽ� ��Ʈ�ѷ����� �̵����� �ش�.
	for (CLink *pLink = m_linkChild.m_pNext; pLink != &m_linkChild; pLink = pLink->m_pNext) {
		CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
		if (pControl->m_pctrOwner)
			break;
		ptWindow.x = pControl->m_ptWindow.x + size.cx;
		ptWindow.y = pControl->m_ptWindow.y + size.cy;
		pControl->OnMove(ptWindow);
	}
}

#ifndef	GAME
void	CControl::InvalidateRect(LPRECT lpRect)
{
	RECT rect;

	CopyRect(&rect, lpRect);
	OffsetRect(&rect, m_ptWindow.x, m_ptWindow.y);
	IntersectRect(&rect, &rect, &m_rcClip);

	::InvalidateRect(CContext::m_hWnd, &rect, TRUE);
}


#elif defined(GAME)

void	CControl::InvalidateRect(LPRECT lpRect)
{
	RECT rect;

	CopyRect(&rect, lpRect);
	OffsetRect(&rect, m_ptWindow.x, m_ptWindow.y);
	if (IntersectRect(&rect, &rect, &m_rcClip))
		UpdateRect(&rect);
}

void CControl::InvalidateRectScreen( LPRECT lpRect)
{
	RECT rect;
	if( IntersectRect( &rect, &m_rcClip, lpRect) ) {
		if( m_pDC)
			UpdateRect( &rect);
		else if( m_pctrParent)
			m_pctrParent->InvalidateRectScreen( &rect);
	}
}

void	CControl::OnViewportChange()
{
	if (m_pDC) {
		m_pDC->OnViewportChange();
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			if (!(pControl->m_pctrOwner))
				break;
			pControl->OnViewportChange();
		}
	}
	else {
		for (CLink *pLink = m_linkChild.m_pPrev; pLink != &m_linkChild; pLink = pLink->m_pPrev) {
			CControl *pControl = LINK_POINTER(pLink, CControl, m_linkParent);
			pControl->OnViewportChange();
		}
	}
}

#endif

LANGUAGE	Control::LanguageIdToLanguage(int nLangaugeId)
{
	switch (nLangaugeId)
	{
		// Traditional Chinese 
		case 0x0404:	
			return TRADITIONAL_CHINESE;

		// Japanese
		case 0x0411:				
			return JAPANESE;

		// Korean
		case 0x0412:				
			return KOREAN;

		// Simplified Chinese
		case 0x0804:	
			return SIMPLIFIED_CHINESE;

		// USA
		case 0x0409:
			return ENGLISH;

	}
	return LANGUAGE_DEFAULT;
}

LANGUAGE	Control::CodePageToLanguage(int nCodePage)
{
	switch (nCodePage) {
	case 950:
		return TRADITIONAL_CHINESE;
	case 932:
		return JAPANESE;
	case 949:
		return KOREAN;
	case 936:
		return SIMPLIFIED_CHINESE;
	case 1252:
		return ENGLISH;
	}
	return LANGUAGE_DEFAULT;
}

#include "KGameSys.h"
#include "KGameSysex.h"
HFONT	Control::CreateFont(LANGUAGE nLanguage, int nHeight, int nWeight)
{
#if 0
	return ::CreateFont(nHeight, 0, 0, 0, nWeight, FALSE, FALSE, 0, g_nCharSet[nLanguage], 
		OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, NONANTIALIASED_QUALITY /* DEFAULT_QUALITY */, FIXED_PITCH | FF_DONTCARE, NULL);
#else

	char FontName[ MAX_PATH];

	if( nLanguage == KOREAN) {
		GetPrivateProfileString( "font", "name", "", FontName, sizeof(FontName), "./system.ini");        
	}
	else if (nLanguage == ENGLISH) {
		strcpy(FontName, "Tahoma");//"Fixedsys";// "Courier New";
	}
	else if (nLanguage == SIMPLIFIED_CHINESE) {
		if (GetACP() == Control::CP_TRADITIONAL_CHINESE) {
			strcpy(FontName, "mingliu");
			nLanguage = TRADITIONAL_CHINESE;
		}
		else
			strcpy(FontName, "simsun"); // simsun
	} 
	else if (nLanguage == TRADITIONAL_CHINESE) {
		strcpy(FontName, "mingliu");
	}
	else {
		FontName[0] = 0;
	}

	//m_nCodePage = nACP;
#   ifdef DEF_GDIPLUS_OEN_01012
	return ::CreateFont(nHeight, 0, 0, 0, nWeight, FALSE, FALSE, 0, g_nCharSet[nLanguage], 
		OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY  /* DEFAULT_QUALITY */, /*FIXED_PITCH |*/ FF_DONTCARE,
		FontName[0] ? FontName : 0);
#   else
    return ::CreateFont(nHeight, 0, 0, 0, nWeight, FALSE, FALSE, 0, g_nCharSet[nLanguage], 
        OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, NONANTIALIASED_QUALITY /* DEFAULT_QUALITY */, /*FIXED_PITCH |*/ FF_DONTCARE,
        FontName[0] ? FontName : 0);

#   endif
#endif
}

void	Control::DrawBox(HDC hdc, LPRECT pRect, LPRECT prcClip)
{
	RECT rect;

	IntersectRect(&rect, pRect, prcClip);
	if (!IsRectEmpty(&rect)) {
		if (rect.left == pRect->left) {
			MoveToEx(hdc, rect.left, rect.top,  NULL);
			LineTo(hdc, rect.left, rect.bottom);
		}
		if (rect.top == pRect->top) {
			MoveToEx(hdc, rect.left, rect.top,  NULL);
			LineTo(hdc, rect.right, rect.top);
		}
		if (rect.right == pRect->right) {
			MoveToEx(hdc, rect.right-1, rect.top,  NULL);
			LineTo(hdc, rect.right-1, rect.bottom);
		}
		if (rect.bottom == pRect->bottom) {
			MoveToEx(hdc, rect.left, rect.bottom-1,  NULL);
			LineTo(hdc, rect.right, rect.bottom-1);
		}
	}
}

DWORD	Control::_GetCharacterPlacement(HDC hdc, LPCTSTR lpString, int nCount, int nMaxExtent, LPGCP_RESULTS lpResults, DWORD dwFlags)
{
#if 0
	TEXTMETRIC	tm;
	GetTextMetrics( hdc, &tm );

	DWORD	nResult = 0;
	for (; nCount > 0; ) {

		if (!::IsDBCSLeadByteEx(m_nClinetCP, lpString[0])) {
			lpResults->lpDx[nResult] = tm.tmAveCharWidth;
			lpString++;
			nCount--;
		}
		else  {
			lpResults->lpDx[nResult] = tm.tmAveCharWidth << 1;	
			lpString+= 2;
			nCount-=2;
		}
		nResult++;
	}
	return nResult;
#else
//	return ::GetCharacterPlacement(hdc, lpString, nCount, nMaxExtent, lpResults, dwFlags);

	// Windows XP Bug: nCount means glyphs count
	LPSTR	str = (LPSTR) _alloca(nCount * 2);
	LPWSTR	glyphs = (LPWSTR) _alloca(sizeof(WCHAR) * nCount);
	LPINT	dxwidth = (LPINT) _alloca(nCount * sizeof(int));
	int nChar;
	int nGlyphs;
	DWORD	nResult = 0;
	// Windows 98 Bug: lpString must have only one type of Single Byte and MultiByte
	for (; nCount > 0; ) {
		if (!(lpString[0] & 0x80)) {
			for (nChar = 1; nChar < nCount; nChar++) 
				if (lpString[nChar] & 0x80)
					break;
			nGlyphs = nChar;
		}
		else if ( lpString[0] & 0x80) {
			for (nChar = 2; nChar < nCount; nChar+= 2) 
				if ( !(lpString[nChar] & 0x80))
					break;
			nGlyphs = nChar >> 1;
		}
		else if (::IsDBCSLeadByteEx(m_nClientCP, lpString[0])) {
			for (nChar = 2; nChar < nCount; nChar+= 2) 
				if (!::IsDBCSLeadByteEx(m_nClientCP, lpString[nChar]))
					break;
			nGlyphs = nChar >> 1;
		}
		else {
			for (nChar = 1; nChar < nCount; nChar++)
				if (!(lpString[nChar] & 0x80) || ::IsDBCSLeadByteEx(m_nClientCP, lpString[nChar]))
					break;
			nGlyphs = nChar;
		}
		memcpy(str, lpString, nChar);
		memset(str+nChar, 0, nGlyphs);
		GCP_RESULTS result;
		memset(&result, 0, sizeof(GCP_RESULTS));
		memset(glyphs, 0, nGlyphs * sizeof(WORD));
		memset(dxwidth, 0, nGlyphs * sizeof(int));
		result.lStructSize = sizeof(GCP_RESULTS);
		result.lpGlyphs = glyphs;
		result.nGlyphs = nChar;
		result.lpDx = dxwidth;

		if ( !(::GetCharacterPlacement(hdc, str, nChar, 0, &result, dwFlags)) )
		{
			#ifdef	_DEBUG
				ASSERT(::GetCharacterPlacement(hdc, str, nChar, 0, &result, dwFlags));
			#else
				::GetCharacterPlacement(hdc, str, nChar, 0, &result, dwFlags);
			#endif
		}
		lpString += nChar;
		nCount -= nChar;
		memcpy(lpResults->lpGlyphs + nResult, glyphs, nGlyphs * sizeof(WORD));
		memcpy(lpResults->lpDx + nResult, dxwidth, nGlyphs * sizeof(int));
		nResult += nGlyphs;
	}
	return nResult;
#endif
}

DWORD	Control::GetCharacterPlacement(HDC hdc, LPCTSTR lpString, int nCount, int nMaxExtent, LPGCP_RESULTS lpResults, DWORD dwFlags)
{
	if (m_bHongKong) {
		char *new_string = (char *) _alloca(nCount);
		GB2Big(new_string, lpString, nCount);
		lpString = new_string;
	}
	return _GetCharacterPlacement(hdc, lpString, nCount, nMaxExtent, lpResults, dwFlags);
}

void	Control::TransparentBlt(HDC hdcDest, int nXDest, int nYDest, int nWidth, int nHeight, 
						HDC hdcSrc, int nXSrc, int nYSrc, UINT cTransparent)
{
	// Windows 98, Me have bug
	if (XSystem::m_dwOSVersion >= 0x500) {
		::TransparentBlt(hdcDest, nXDest, nYDest, nWidth, nHeight, 
		hdcSrc, nXSrc, nYSrc, nWidth, nHeight, cTransparent);
		return;
	}

   COLORREF   cColor;
   HBITMAP    bmAndBack, bmAndObject, bmAndMem, bmSave;
   HBITMAP    bmBackOld, bmObjectOld, bmMemOld, bmSaveOld;
   HDC        hdcMem, hdcBack, hdcObject, hdcSave;
   // Create some DCs to hold temporary data.
   hdcBack   = CreateCompatibleDC(hdcDest);
   hdcObject = CreateCompatibleDC(hdcDest);
   hdcMem    = CreateCompatibleDC(hdcDest);
   hdcSave   = CreateCompatibleDC(hdcDest);

   // Create a bitmap for each DC. DCs are required for a number of
   // GDI functions.

   // Monochrome DC
   bmAndBack   = CreateBitmap(nWidth, nHeight, 1, 1, NULL);

   // Monochrome DC
   bmAndObject = CreateBitmap(nWidth, nHeight, 1, 1, NULL);

   bmAndMem    = CreateCompatibleBitmap(hdcDest, nWidth, nHeight);
   bmSave      = CreateCompatibleBitmap(hdcDest, nWidth, nHeight);

   // Each DC must select a bitmap object to store pixel data.
   bmBackOld   = (HBITMAP) SelectObject(hdcBack, bmAndBack);
   bmObjectOld = (HBITMAP) SelectObject(hdcObject, bmAndObject);
   bmMemOld    = (HBITMAP) SelectObject(hdcMem, bmAndMem);
   bmSaveOld   = (HBITMAP) SelectObject(hdcSave, bmSave);

   // Save the bitmap sent here, because it will be overwritten.
   BitBlt(hdcSave, 0, 0, nWidth, nHeight, hdcSrc, nXSrc, nYSrc, SRCCOPY);

   // Set the background color of the source DC to the color.
   // contained in the parts of the bitmap that should be transparent
   cColor = SetBkColor(hdcSrc, cTransparent);

   // Create the object mask for the bitmap by performing a BitBlt
   // from the source bitmap to a monochrome bitmap.
   BitBlt(hdcObject, 0, 0, nWidth, nHeight, hdcSrc, nXSrc, nYSrc,
          SRCCOPY);

   // Set the background color of the source DC back to the original
   // color.
   SetBkColor(hdcSrc, cColor);

   // Create the inverse of the object mask.
   BitBlt(hdcBack, 0, 0, nWidth, nHeight, hdcObject, 0, 0,
          NOTSRCCOPY);

   // Copy the background of the main DC to the destination.
   BitBlt(hdcMem, 0, 0, nWidth, nHeight, hdcDest, nXDest, nYDest,
          SRCCOPY);

   // Mask out the places where the bitmap will be placed.
   BitBlt(hdcMem, 0, 0, nWidth, nHeight, hdcObject, 0, 0, SRCAND);

   // Mask out the transparent colored pixels on the bitmap.
   BitBlt(hdcSave, 0, 0, nWidth, nHeight, hdcBack, 0, 0, SRCAND);

   // XOR the bitmap with the background on the destination DC.
   BitBlt(hdcMem, 0, 0, nWidth, nHeight, hdcSave, 0, 0, SRCPAINT);

   // Copy the destination to the screen.
   BitBlt(hdcDest, nXDest, nYDest, nWidth, nHeight, hdcMem, 0, 0,
          SRCCOPY);

   // Delete the memory bitmaps.
   DeleteObject(SelectObject(hdcBack, bmBackOld));
   DeleteObject(SelectObject(hdcObject, bmObjectOld));
   DeleteObject(SelectObject(hdcMem, bmMemOld));
   DeleteObject(SelectObject(hdcSave, bmSaveOld));

   // Delete the memory DCs.
   DeleteDC(hdcMem);
   DeleteDC(hdcBack);
   DeleteDC(hdcObject);
   DeleteDC(hdcSave);
}

BOOL	Control::ExtTextOut(HDC hdc, int X, int Y, UINT fuOptions, CONST RECT *lprc, LPCTSTR lpString, UINT cbCount, CONST INT *lpDx)
{
	if (m_bHongKong) {
		char *lpNewString = (char *) _alloca(cbCount);
		GB2Big(lpNewString, lpString, cbCount);
		lpString = lpNewString;
	}
	return ::ExtTextOut(hdc, X, Y, fuOptions, lprc, lpString, cbCount, lpDx);
}

BOOL	Control::TextOut(HDC hdc, int nXStart, int nYStart, LPCTSTR lpString, int cbString)
{
	if (m_bHongKong) {
		char *lpNewString = (char *) _alloca(cbString);
		GB2Big(lpNewString, lpString, cbString);
		lpString = lpNewString;
	}
	return ::TextOut(hdc, nXStart, nYStart, lpString, cbString);
}

int		Control::DrawText(HDC hdc, LPCTSTR lpString, int nCount, LPRECT lpRect, UINT uFormat)
{
	if (m_bHongKong) {
		char *lpNewString = (char *) _alloca(nCount);
		GB2Big(lpNewString, lpString, nCount);
		lpString = lpNewString;
	}
	return ::DrawText(hdc, lpString, nCount, lpRect, uFormat);
}


void	CRoot::OnLButtonDown(POINT pt, WPARAM wParam)
{
	CContext::SetFocus(0);
	SetCapture();
	g_bProcessed = FALSE;
}

void	CRoot::OnLButtonUp(POINT pt, WPARAM wParam)
{
	ReleaseCapture();
	g_bProcessed = FALSE;
}

void	CRoot::OnRButtonDown(POINT pt, WPARAM wParam)
{
	CContext::SetFocus(0);
	SetCapture();
	g_bProcessed = FALSE;
}

void	CRoot::OnRButtonUp(POINT pt, WPARAM wParam)
{
	ReleaseCapture();
	g_bProcessed = FALSE;
}

void	CRoot::OnMouseMove(POINT pt, WPARAM wParam)
{
	g_bProcessed = FALSE;
}

BOOL	CContext::Create(HWND hWnd)
{
	m_ctrRoot.Create();
	m_hWnd = hWnd;
	OnInputLangChange();
	ImmAssociateContext(m_hWnd, 0);

#ifdef	OPENGL // GAME
	GLint vnViewport[4];
	glGetIntegerv(GL_VIEWPORT, vnViewport);
	m_sizeViewport.cx = vnViewport[2];
	m_sizeViewport.cy = vnViewport[3];
#endif

	return TRUE;
}

void	CContext::Destroy()
{
	m_pctrFocus = 0;
	m_pctrCapture = 0;
	m_pctrTrack = 0;
	m_dwProperty = 0;
	m_nLanguage = LANGUAGE_DEFAULT;
	m_nState = 0;
	m_hWnd = 0;
}

void	CContext::Destroy(CControl *pControl) 
{ 
	if (m_pctrFocus == pControl) 
		SetFocus(NULL); 
	if (m_pctrCapture == pControl) 
		m_pctrCapture = 0; 
	if (m_pctrTrack == pControl)
		m_pctrTrack = 0;
}

void	CContext::OnInputLangChange()
{
	HKL	hkl = GetKeyboardLayout(0);
	m_dwProperty = ImmGetProperty( hkl, IGP_PROPERTY );
	m_nLanguage = LanguageIdToLanguage(LOWORD(hkl));
}

void	CContext::OnSetFocus()
{
	m_nState |= STATE_FOCUS;
	if (m_pctrFocus)
		m_pctrFocus->OnSetFocus();
}

void	CContext::OnKillFocus()
{
	if ((m_nState & STATE_COMPOSE) && (m_nLanguage != KOREAN))
	{
		HIMC hIMC;
		if ( hIMC = ImmGetContext(m_hWnd) ) 
		{
			ImmNotifyIME(hIMC, NI_COMPOSITIONSTR, CPS_COMPLETE, 0);
			ImmReleaseContext(m_hWnd,hIMC);
		}
	}	
	if (m_pctrFocus)
		m_pctrFocus->OnKillFocus();
	m_nState &= ~STATE_FOCUS;
}

void	CContext::SetFocus(CControl *pControl)
{
	if (pControl && !(m_nState & STATE_FOCUS))
		::SetFocus(m_hWnd);
	if (m_pctrFocus != pControl) {
		if (m_pctrFocus && (m_nState & STATE_FOCUS))
			m_pctrFocus->OnKillFocus();
		m_pctrFocus = pControl;
		if (m_pctrFocus && (m_nState & STATE_FOCUS))
			m_pctrFocus->OnSetFocus();
	}
}

static UINT g_nLeadByte;

BOOL	CContext::OnImeStartComposition()
{
	m_nState |= STATE_COMPOSE;
	if (!(m_dwProperty & IME_PROP_AT_CARET))
		return FALSE;
	if (m_pctrFocus)
		m_pctrFocus->OnImeStartComposition();
	return TRUE;
}

BOOL	CContext::OnImeComposition(LPARAM lParam)
{
	if (!(m_dwProperty & IME_PROP_AT_CARET))
		return FALSE;
	if (m_pctrFocus)
		m_pctrFocus->OnImeComposition(lParam);
	return TRUE;
}

BOOL	CContext::OnImeEndComposition()
{
	m_nState &= ~STATE_COMPOSE;
	if (!(m_dwProperty & IME_PROP_AT_CARET))
		return FALSE;
	if (m_pctrFocus)
		m_pctrFocus->OnImeEndComposition();
	return TRUE;
}

void	CContext::OnImeNotify(WPARAM wParam)
{
	switch (wParam )
	{
		case IMN_OPENCANDIDATE:
			m_nState |= STATE_CANDIDATE;
			break;

		case IMN_CLOSECANDIDATE:
			m_nState &= ~STATE_CANDIDATE;
			break;
	}
	if (m_pctrFocus)
		m_pctrFocus->OnImeNotify(wParam);
}

CControl *	CContext::GetFocus()
{
	return (m_nState & STATE_FOCUS) ? m_pctrFocus : NULL;
}

BOOL	CContext::OnLButtonDown(POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;

	if (m_pctrCapture)
	{
		m_pctrCapture->OnLButtonDown( pt, wParam );
		return g_bProcessed;
	}

	CControl *pControl = ControlFromPoint(pt);
	
	ASSERT( pControl);

	if( pControl)
	{
#ifdef DEF_UI_RENEWALL_081016
        // ��� Ư�� ��Ʈ���� ���� ���Ʒ� ���踦 �����Ѵ�.
        if (pControl->IsBringControlToTop())
#endif

		pControl->BringControlToTop();
		pControl->OnLButtonDown( pt, wParam );
	}

	return g_bProcessed;
}

BOOL	CContext::OnLButtonDblClk( POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;
	if (m_pctrCapture) {
		m_pctrCapture->OnLButtonDblClk( pt, wParam);
		return g_bProcessed;
	}
	CControl *pControl = ControlFromPoint(pt);
	if( pControl){
		pControl->BringControlToTop();
		pControl->OnLButtonDblClk( pt, wParam);
	}
	return g_bProcessed;
}

BOOL	CContext::OnLButtonUp(POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;
	if (m_pctrCapture) {
		m_pctrCapture->OnLButtonUp(pt, wParam);
		return g_bProcessed;
	}

    // �������� ���� ��Ʈ���� ��´�.
	CControl *pControl = ControlFromPoint(pt);

    // ��Ʈ�ѿ� ����
	if( pControl)
		pControl->OnLButtonUp(pt, wParam);

	return g_bProcessed;
}

BOOL	CContext::OnRButtonDown(POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;

    // ĸ���� ��Ʈ���� �ִٸ� �ش� ĸ���� ��Ʈ�� ���Ը� �޽����� �����Ѵ�.
	if (m_pctrCapture) {
		m_pctrCapture->OnRButtonDown(pt, wParam);
		return g_bProcessed;
	}

    // �׿��� �޽����� �ش� ��ġ�� �ִ� �����쿡�� �޽����� �����Ѵ�.
	CControl *pControl = ControlFromPoint(pt);

	if( pControl) {
		pControl->BringControlToTop();
		pControl->OnRButtonDown(pt, wParam);
	}
	
	return g_bProcessed;
}

BOOL	CContext::OnRButtonUp(POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;

    // ĸ���� ��Ʈ���� �ִٸ� �ش� ��Ʈ�ѿ��� �޽����� ������ �ش�.
	if (m_pctrCapture) {
		m_pctrCapture->OnRButtonUp(pt, wParam);
		return g_bProcessed;
	}

    // ĸ���� ��Ʈ���� ���ٸ� ���� �ڸ��� ��Ʈ�ѿ��� �޽����� �����Ѵ�.
	CControl *pControl = ControlFromPoint(pt);

	if( pControl)
		pControl->OnRButtonUp(pt, wParam);

	return g_bProcessed;
}

BOOL	CContext::OnMButtonDown(POINT pt, WPARAM wParam)
{
	SetFocus(0);
	m_ctrRoot.SetCapture();
	return FALSE;
}

BOOL	CContext::OnMButtonUp(POINT pt, WPARAM wParam)
{
	m_ctrRoot.ReleaseCapture();
	return FALSE;
}

void	CContext::OnSize( int cx, int cy)
{
	m_ctrRoot.OnSize( cx, cy);
}

BOOL	CContext::OnMouseMove(POINT pt, WPARAM wParam)
{
	g_bProcessed = TRUE;
	if (m_pctrCapture) {
		m_pctrCapture->OnMouseMove(pt, wParam);
		return g_bProcessed;
	}
	CControl *pControl = ControlFromPoint(pt);
	if (m_pctrTrack && pControl != m_pctrTrack) {
		m_pctrTrack->OnMouseLeave(pt, wParam);
		m_pctrTrack = 0;
	}

	if( pControl)
#ifdef DEF_STATUS_TOOLTIP_DIOB_080305

#ifndef DEF_UI_RENEWALL_081016
	{
		//�����÷����� ĳ�� ����â "chargen"�� ������� ���� ������ ������ ��Ȱ��ȭ ���Ѷ�.
		if( ( pControl->m_sizeWindow.cx != 145 || pControl->m_sizeWindow.cy != 324 ) && !KGameSys::m_bGamePlay )
		{
			KGameSys::SetToolTip( "" );
		}
		else if( ( pControl->m_sizeWindow.cx == 145 && pControl->m_sizeWindow.cy == 324 ) && !KGameSys::m_bGamePlay )
		{
			pControl->StatusToolTip( pt );
		}
		pControl->OnMouseMove( pt, wParam );
	}
#else	// #ifdef DEF_UI_RENEWALL_081016
	{
		//�����÷����� ĳ�� ����â "chargen"�� ������� ���� ������ ������ ��Ȱ��ȭ ���Ѷ�.
		if( ( pControl->m_sizeWindow.cx != 270 || pControl->m_sizeWindow.cy != 620 ) && !KGameSys::m_bGamePlay )
		{
			KGameSys::SetToolTip( "" );
		}
		else if( ( pControl->m_sizeWindow.cx == 270 && pControl->m_sizeWindow.cy == 620 ) && !KGameSys::m_bGamePlay )
		{
			pControl->StatusToolTip( pt );
		}
		pControl->OnMouseMove( pt, wParam );
	}

#endif	// #endif DEF_UI_RENEWALL_081016

#else	// #ifndef DEF_STATUS_TOOLTIP_DIOB_080305
		pControl->OnMouseMove(pt, wParam);
#endif	// #endif DEF_STATUS_TOOLTIP_DIOB_080305

	return g_bProcessed;
}

#ifdef	GAME

Control::CDC::CDC()
{
	m_hBitmap = 0;
	m_hbr = 0;
#ifdef	OPENGL
	m_nTexture = 0;
#else
	m_pTexture = 0;
#endif

#ifdef DEF_UI_RENEWALL_081016
    SetRect(&m_rtDraw, 0, 0, 0, 0);
#endif

	Destroy();
}

#ifndef DEF_UI_RENEWALL_081016
void Control::CDC::Create(CControl *pControl, POINT pt, SIZE size, DWORD nColor, COLORREF nColorKey)
#else
void Control::CDC::Create(CControl *pControl, POINT pt, SIZE size, DWORD nColor, COLORREF nColorKey, RECT* pDrawRect)
#endif
{
	m_pControl = pControl;
	m_pt = pt;
	m_size = size;
	m_xrgbColorKey = ((nColorKey & 0xff) << 16) | (nColorKey & 0x0000ff00) | ((nColorKey & 0x00ff0000) >> 16);
#ifdef	OPENGL
	m_agbrColor = nColor;
#else
 	m_argbColor = (WORD) (((nColor & 0xf0) << 4) | ((nColor & 0x0000f000) >> 8) | 
 		((nColor & 0x00f00000) >> 20) | ((nColor & 0xf0000000) >> 16));
#endif

#ifdef DEF_UI_RENEWALL_081016
    // ����� ����� �� ������ �����Ѵ�.
    if (pDrawRect)
    {
        m_rtDraw = *pDrawRect;
        // �������� �̵� ��Ų��.
        OffsetRect(&m_rtDraw, -m_rtDraw.left, -m_rtDraw.top);
    }
#endif

    BITMAPINFO bmi;
    ZeroMemory( &bmi.bmiHeader, sizeof(BITMAPINFOHEADER) );
    bmi.bmiHeader.biSize        = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth       =  m_size.cx;
    bmi.bmiHeader.biHeight      = -m_size.cy;
    bmi.bmiHeader.biPlanes      = 1;
    bmi.bmiHeader.biCompression = BI_RGB;
    bmi.bmiHeader.biBitCount    = 32;

	HDC hDC = CreateCompatibleDC(0);
	m_pBits = 0;
    m_hBitmap = CreateDIBSection( hDC, &bmi, DIB_RGB_COLORS,
                                  (void**)&m_pBits, NULL, 0 );
	ASSERT(m_hBitmap);
	ASSERT(m_pBits);
#ifdef	OPENGL
	for (size.cx = 1; size.cx < m_size.cx; size.cx <<= 1)
		;
	for (size.cy = 1; size.cy < m_size.cy; size.cy <<= 1)
		;
	{
		int i = m_size.cx * m_size.cy;
		DWORD *pBits = m_pBits;
		for ( ; i > 0; --i) {
			*pBits++ = nColorKey;
		}
	}
	glGenTextures(1, &m_nTexture);
	glBindTexture(GL_TEXTURE_2D, m_nTexture);
	
	{
		glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, size.cx, size.cy, 0, GL_ALPHA, GL_UNSIGNED_BYTE, m_pBits);
		DWORD *pBits = m_pBits;
		for (int i = m_size.cx * m_size.cy ; i > 0; --i) 
			*pBits++ = m_agbrColor;
		glTexSubImage2D(GL_TEXTURE_2D, 0, 0, 0, m_size.cx, m_size.cy, GL_RGBA, GL_UNSIGNED_BYTE, m_pBits);
	}
	glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST); // texture
	glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST); // texture
#else
#ifdef	_SOCKET_TEST
//	ASSERT(size.cx <= 512 && size.cy <= 512);
#endif
	if ((XSystem::m_caps.TextureCaps & (D3DPTEXTURECAPS_POW2  /*| D3DPTEXTURECAPS_NONPOW2CONDITIONAL*/)) == D3DPTEXTURECAPS_POW2) {
		for (size.cx = 1; size.cx < m_size.cx; size.cx <<= 1)
			;
		for (size.cy = 1; size.cy < m_size.cy; size.cy <<= 1)
			;
	}
	if (XSystem::m_caps.TextureCaps & D3DPTEXTURECAPS_SQUAREONLY) {
		if (size.cx < size.cy)
			size.cx = size.cy;
		else
			size.cy = size.cx;
	}
	HRESULT hr = CContext::m_pd3dDevice->CreateTexture( size.cx, size.cy, 1,
                                      0, D3DFMT_A4R4G4B4,
                                      D3DPOOL_MANAGED, &m_pTexture, NULL);
	ASSERT(SUCCEEDED(hr));

	if( FAILED( hr)) {
		ELOG( "FAIL CreateTexture At %s, %s, size = ( %d, %d), hr = %x\n, __FILE__ ,__LINE__", size.cx, size.cy, hr);
		DeleteDC(hDC);
		return;
	}

    D3DLOCKED_RECT d3dlr;
    m_pTexture->LockRect( 0, &d3dlr, 0, 0 );
    BYTE* pDstRow;
    pDstRow = (BYTE*)d3dlr.pBits;
    WORD* pDst;
    int x, y;

    for( y=0; y < size.cy; y++ )
    {
        pDst = (WORD*) pDstRow;
        for( x=0; x < size.cx; x++ )
        {
			*pDst++ = m_argbColor;
        }
        pDstRow += d3dlr.Pitch;
    }
    m_pTexture->UnlockRect(0);
#endif
	DeleteDC(hDC);

	m_fSizeX = (float) m_size.cx / (float) size.cx;
	m_fSizeY = (float) m_size.cy / (float) size.cy;
	OnViewportChange();

	m_hbr = CreateSolidBrush(nColorKey);
}

void Control::CDC::Destroy()
{
	DeleteObject(m_hBitmap);
	DeleteObject(m_hbr);
	m_hBitmap = 0;
	m_pControl = 0;
	m_hbr = 0;
	SetRectEmpty(&m_rcCaret);
#ifdef	OPENGL
	if (m_nTexture) {
		glDeleteTextures(1, &m_nTexture);
		m_nTexture = 0;
	}
#else
	if (m_pTexture) {
		m_pTexture->Release();
		m_pTexture = 0;
	}
#endif
}

void	Control::CDC::UpdateRect(LPRECT pRect)
{
	HDC hdc = CreateCompatibleDC(0);
	ASSERT(hdc);
	SetWindowOrgEx(hdc, m_pt.x, m_pt.y, 0);
	HGDIOBJ hBitmap = SelectObject(hdc, m_hBitmap);
	FillRect(hdc, pRect, m_hbr);
	m_pControl->OnPaint(hdc, pRect);
	SelectObject(hdc, hBitmap);
	SetWindowOrgEx(hdc, 0, 0, 0);
//	DeleteDC(hdc);		// sea 20060825 �߸� delete 
	DeleteObject(hdc);	
	UpdateTexture(pRect);
}

void	Control::CDC::OnInitDevice()
{

}

void	Control::CDC::OnDeleteDevice()
{

}

void Control::CDC::OnPaint()
{
#ifdef	OPENGL 
	glPushAttrib(GL_ENABLE_BIT | GL_TEXTURE_BIT | GL_COLOR_BUFFER_BIT | GL_CURRENT_BIT);
	glMatrixMode(GL_MODELVIEW);
	glPushMatrix();
	glLoadIdentity();
	glMatrixMode(GL_PROJECTION);
	glPushMatrix();
	glLoadIdentity();

	glBindTexture(GL_TEXTURE_2D, m_nTexture);

	glDisable(GL_LIGHTING);	// lighting/enable
	glDisable(GL_DEPTH_TEST); // depth-buffer/enable
//	glDepthMask(false); // depth-buffer
	glEnable(GL_BLEND);			// color-buffer/enable
	glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA); // color-buffer
	glDisable(GL_ALPHA_TEST); // color-buffer/enable
//	glAlphaFunc(GL_GREATER, 0.0f); // color-buffer
	glEnable(GL_TEXTURE_2D); // texture/enable
	glTexEnvi(GL_TEXTURE_ENV, GL_TEXTURE_ENV_MODE, GL_REPLACE); // texture


	glBegin(GL_QUADS);
	glTexCoord2f(0.0f,0.0f); glVertex2f(m_fLeft, m_fTop);
	glTexCoord2f(0.0f,m_fSizeY); glVertex2f(m_fLeft, m_fBottom);
	glTexCoord2f(m_fSizeX, m_fSizeY); glVertex2f(m_fRight, m_fBottom);
	glTexCoord2f(m_fSizeX,0.0f); glVertex2f(m_fRight, m_fTop);
	glEnd();
	
	if (!IsRectEmpty(&m_rcCaret) && !((GetTickCount() - m_nCaretTick) & 32)) {
		glDisable(GL_TEXTURE_2D); // texture/enable

		glColor3ubv((GLubyte *)&m_nCaretColor); // current
		glBegin(GL_QUADS);
		glVertex2f(m_fCaretLeft, m_fCaretTop);
		glVertex2f(m_fCaretLeft, m_fCaretBottom);
		glVertex2f(m_fCaretRight, m_fCaretBottom);
		glVertex2f(m_fCaretRight, m_fCaretTop);
		glEnd();
	}
	glMatrixMode(GL_PROJECTION);
	glPopMatrix();
	glMatrixMode(GL_MODELVIEW);
	glPopMatrix();
	glPopAttrib();
#else
	CContext::m_pd3dDevice->SetRenderState( D3DRS_LIGHTING, FALSE);
    CContext::m_pd3dDevice->SetRenderState( D3DRS_ZENABLE, FALSE);
    CContext::m_pd3dDevice->SetRenderState( D3DRS_ALPHABLENDENABLE, TRUE );
    CContext::m_pd3dDevice->SetRenderState( D3DRS_SRCBLEND,   D3DBLEND_SRCALPHA );
    CContext::m_pd3dDevice->SetRenderState( D3DRS_DESTBLEND,  D3DBLEND_INVSRCALPHA );
	CContext::m_pd3dDevice->SetSamplerState(0, D3DSAMP_ADDRESSU,  D3DTADDRESS_CLAMP);
	CContext::m_pd3dDevice->SetSamplerState(0, D3DSAMP_ADDRESSV,  D3DTADDRESS_CLAMP);
	CContext::m_pd3dDevice->SetSamplerState(0, D3DSAMP_ADDRESSW,  D3DTADDRESS_CLAMP);
    CContext::m_pd3dDevice->SetSamplerState( 0, D3DSAMP_MINFILTER, D3DTEXF_POINT);
    CContext::m_pd3dDevice->SetSamplerState( 0, D3DSAMP_MAGFILTER, D3DTEXF_POINT );
    CContext::m_pd3dDevice->SetSamplerState( 0, D3DSAMP_MIPFILTER, D3DTEXF_NONE );
    CContext::m_pd3dDevice->SetFVF(D3DFVF_XYZRHW | D3DFVF_TEX1);
    CContext::m_pd3dDevice->SetTexture( 0, m_pTexture );
    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_COLOROP,   D3DTOP_SELECTARG1 );
//    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_COLOROP,   D3DTOP_MODULATE);
    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_COLORARG1, D3DTA_TEXTURE );	
//	CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_COLORARG2, D3DTA_TFACTOR );
    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_ALPHAOP,   D3DTOP_SELECTARG1 );
    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_ALPHAARG1, D3DTA_TEXTURE );
	CContext::m_pd3dDevice->SetTextureStageState(0, D3DTSS_TEXCOORDINDEX, 0);
    CContext::m_pd3dDevice->SetTextureStageState( 1, D3DTSS_COLOROP,   D3DTOP_DISABLE );

#ifdef _USE_DRAWPRIMITIVEUP
	CContext::m_pd3dDevice->DrawPrimitiveUP(D3DPT_TRIANGLESTRIP, 2, m_vVertex, sizeof(VERTEX));
#else
	DrawPrimitiveUPTest( D3DPT_TRIANGLESTRIP, 2, m_vVertex, sizeof(VERTEX), 4 * sizeof(VERTEX));
#endif

    CContext::m_pd3dDevice->SetRenderState( D3DRS_ALPHABLENDENABLE, FALSE);

	if (!IsRectEmpty(&m_rcCaret) && !((GetTickCount() - m_nCaretTick) & 256)) {
	    CContext::m_pd3dDevice->SetTextureStageState( 0, D3DTSS_COLOROP,   D3DTOP_DISABLE );
	    CContext::m_pd3dDevice->SetFVF(D3DFVF_XYZRHW | D3DFVF_DIFFUSE);

#ifdef _USE_DRAWPRIMITIVEUP
		CContext::m_pd3dDevice->DrawPrimitiveUP(D3DPT_TRIANGLESTRIP, 2, m_vCaretVertex, sizeof(CARET_VERTEX));
#else
		DrawPrimitiveUPTest( D3DPT_TRIANGLESTRIP, 2, m_vCaretVertex, sizeof(CARET_VERTEX), 4 * sizeof(CARET_VERTEX));
#endif
	}
    CContext::m_pd3dDevice->SetRenderState( D3DRS_ZENABLE, TRUE);
#endif
//	glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST); // texture
//	glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST); // texture
}

void	Control::CDC::UpdateTexture(LPRECT pRect)
{
	POINT pt = { pRect->left - m_pt.x, pRect->top - m_pt.y };
	SIZE  size = { pRect->right - pRect->left, pRect->bottom - pRect->top};

	if (size.cx == 0 || size.cy == 0 || m_pBits == 0)
		return;

	DWORD *pBits = m_pBits + pt.y * m_size.cx + pt.x;
	int width = m_size.cx - size.cx;

#ifdef	OPENGL
	for (int y = size.cy; y > 0; y--) {
		for (int x = size.cx; x > 0; x--) {
			if ((*pBits & 0xffffff) == m_xrgbColorKey)
				*pBits = m_agbrColor;	// transparent
			else {
				BYTE b = ((BYTE *)pBits)[0];
				((BYTE *)pBits)[0] = ((BYTE *)pBits)[2];
				((BYTE *)pBits)[2] = b;
				((BYTE *)pBits)[3] = 0xff; // opaque
			}
			pBits++;
		}
		pBits += width;
	}
	glPushClientAttrib(GL_CLIENT_PIXEL_STORE_BIT);
	glBindTexture(GL_TEXTURE_2D, m_nTexture);
	glPixelStorei(GL_UNPACK_ROW_LENGTH, m_size.cx); // pixel-store
	glPixelStorei(GL_UNPACK_SKIP_PIXELS, pt.x);
	glPixelStorei(GL_UNPACK_SKIP_ROWS, pt.y);
	glTexSubImage2D(GL_TEXTURE_2D, 0, pt.x, pt.y, size.cx, size.cy, GL_RGBA, GL_UNSIGNED_BYTE, m_pBits);
	glPopClientAttrib();
#else

    // ����� �����.
    D3DLOCKED_RECT d3dlr;
	RECT  rect = {pt.x, pt.y, pt.x + size.cx, pt.y + size.cy};
	HRESULT hr = m_pTexture->LockRect( 0, &d3dlr, &rect, 0 );    

	if( SUCCEEDED(hr) )
	{
		BYTE* pDstRow;
		pDstRow = (BYTE*)d3dlr.pBits;
		WORD* pDst;
		int x, y;

		for( y = size.cy; --y >= 0; )
		{
			pDst = (WORD*)pDstRow;
			for( x = size.cx; --x >= 0; )
			{
				DWORD bit = *pBits++;

                // �÷�Ű (������) �� ���� ��� ���� ó���� �Ѵ�.
				if ( (bit & 0xffffff) == m_xrgbColorKey )
				{
#ifdef DEF_UI_RENEWALL_081016
                    // ���� �ؽ�ó�� �Ƚ��� ��ġ�� ��´�.
                    POINT tempPt = { pt.x + x, pt.y + y };

                    // Ư������(m_rtDraw) �� ���� ���� ó���� �� �ش�.
                    if ( !IsRectEmpty(&m_rtDraw) && PtInRect(&m_rtDraw, tempPt) )
                    {
                        *pDst++ = (WORD) 0x0000;
                    }
                    else
#endif
					*pDst++ = m_argbColor;	// transparent
				}
				else
				{
					*pDst++ = (WORD) (0xf000 | ((bit & 0x00f00000) >> 12) | ((bit & 0x0000f000) >> 8) | ((bit & 0x000000f0) >> 4)); 
				}
			}
			pDstRow += d3dlr.Pitch;
			pBits += width;
		}
		m_pTexture->UnlockRect(0);
	}
	
#endif
		
}

void	Control::CDC::OnViewportChange()
{
#ifdef	OPENGL
	SIZE size = m_pControl->m_pContext->m_sizeViewport;

	m_fLeft = (float) (m_pt.x * 2 - size.cx)  / (float) size.cx;
	m_fRight = (float)((m_pt.x + m_size.cx) * 2 - size.cx) / (float) size.cx;
	m_fTop = (float) (size.cy - m_pt.y * 2) / (float) size.cy; 
	m_fBottom = (float) (size.cy - (m_pt.y + m_size.cy) * 2) / (float) size.cy; 

	m_fCaretLeft = (float) (m_rcCaret.left * 2 - size.cx)  / (float) size.cx;
	m_fCaretRight = (float)(m_rcCaret.right * 2 - size.cx) / (float) size.cx;
	m_fCaretTop = (float) (size.cy - m_rcCaret.top * 2) / (float) size.cy; 
	m_fCaretBottom = (float) (size.cy - m_rcCaret.bottom * 2) / (float) size.cy; 
#else
	m_vVertex[0].p = D3DXVECTOR4(m_pt.x - 0.5f, m_pt.y - 0.5f, 0.9f, 1.0f);
	m_vVertex[0].tu = 0;
	m_vVertex[0].tv = 0;
	m_vVertex[1].p = D3DXVECTOR4(m_pt.x + m_size.cx - 0.5f, m_pt.y - 0.5f, 0.9f, 1.0f);
	m_vVertex[1].tu = m_fSizeX;
	m_vVertex[1].tv = 0;
	m_vVertex[2].p = D3DXVECTOR4(m_pt.x - 0.5f, m_pt.y + m_size.cy - 0.5f, 0.9f, 1.0f);
	m_vVertex[2].tu = 0;
	m_vVertex[2].tv = m_fSizeY;
	m_vVertex[3].p = D3DXVECTOR4(m_pt.x + m_size.cx - 0.5f, m_pt.y + m_size.cy - 0.5f, 0.9f, 1.0f);
	m_vVertex[3].tu = m_fSizeX;
	m_vVertex[3].tv = m_fSizeY;

	m_vCaretVertex[0].p = D3DXVECTOR4(m_rcCaret.left - 0.5f, m_rcCaret.top - 0.5f, 0.9f, 1.0f);
	m_vCaretVertex[0].color = m_nCaretColor;
	m_vCaretVertex[1].p = D3DXVECTOR4(m_rcCaret.right - 0.5f, m_rcCaret.top - 0.5f, 0.9f, 1.0f);
	m_vCaretVertex[1].color = m_nCaretColor;
	m_vCaretVertex[2].p = D3DXVECTOR4(m_rcCaret.left - 0.5f, m_rcCaret.bottom - 0.5f, 0.9f, 1.0f);
	m_vCaretVertex[2].color = m_nCaretColor;
	m_vCaretVertex[3].p = D3DXVECTOR4(m_rcCaret.right - 0.5f, m_rcCaret.bottom - 0.5f, 0.9f, 1.0f);
	m_vCaretVertex[3].color = m_nCaretColor;
#endif
}

void	Control::CDC::OnMove(SIZE size)
{
	m_pt.x += size.cx;
	m_pt.y += size.cy;
	OffsetRect(&m_rcCaret, size.cx, size.cy);
	OnViewportChange();
}
#endif