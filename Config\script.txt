(script "blend" (code 
						(IF (LT (VertexType) 5)
							(	(Assert (LE (BoneCount) 26))
								(CreateTexture 0 (Param 1))
								(Effect (Param 0))
								(SetInt "VertexType" (VertexType))
								(LightDirection "LightDirection")
								(ProjMatrix "ProjMatrix")
								(AmbientColor "AmbientColor")
								(DiffuseColor "DiffuseColor")
								(WorldViewMatrix "WorldViewMatrix")
								(Fog "Fog")
				  ))))

(script "skinmesh" (code 
						(IF (LT (VertexType) 5)
							(	(Assert (LE (BoneCount) 26))
								(CreateTexture 0 (Param 0))
								(Effect "skinmesh")
								(SetInt "VertexType" (VertexType))
								(LightDirection "LightDirection")
								(ProjMatrix "ProjMatrix")
								(AmbientColor "AmbientColor")
								(DiffuseColor "DiffuseColor")
								(WorldViewMatrix "WorldViewMatrix")
								(Fog "Fog")
				  ))))

(script "glow" (code 
					(Call "blend" "glow" (Param 0))))

(model "Data\Model\Clothes\CW_5_S01" 
	(script 
;		(("cw_5_s01" "babc")
;				(CreateTexture 0 (TextureName)) 
;				(Effect "skinmesh" )
;				(SetInt "VertexType" (VertexType)) 
;				)
		(() 
			(Call "glow" (TextureName)))))
(model "Data\Model\Clothes\CW_1_S01" 
	(script 
;		(()
;				(CreateTexture 0 (TextureName)) 
;				(Effect "skinmesh" )
;				(SetInt "VertexType" (VertexType)) 
;				)
		( () ; "cw_5_s01_sp" 
			(Call "glow" (TextureName)))))
	